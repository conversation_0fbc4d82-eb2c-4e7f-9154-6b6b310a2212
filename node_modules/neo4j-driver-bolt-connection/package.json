{"name": "neo4j-driver-bolt-connection", "version": "5.28.1", "description": "Implements the connection with the Neo4j Database using the Bolt Protocol", "main": "lib/index.js", "types": "types/index.d.ts", "scripts": {"build": "tsc", "test": "jest --passWithNoTests", "test::watch": "jest --watch", "test::unit": "npm run test", "prepare": "npm run build", "clean": "rm -fr node_modules lib"}, "repository": {"type": "git", "url": "git://github.com/neo4j/neo4j-javascript-driver.git"}, "keywords": ["bolt", "neo4j", "driver"], "browser": {"./lib/channel/node/index.js": "./lib/channel/browser/index.js"}, "author": "Neo4j", "license": "Apache-2.0", "bugs": {"url": "https://github.com/neo4j/neo4j-javascript-driver/issues"}, "homepage": "https://github.com/neo4j/neo4j-javascript-driver#readme", "devDependencies": {"@types/jest": "^29.5.3", "fast-check": "^3.10.0", "jest": "^29.6.2", "lolex": "^6.0.0", "ts-jest": "^29.1.1", "typescript": "^4.9.5"}, "dependencies": {"buffer": "^6.0.3", "neo4j-driver-core": "5.28.1", "string_decoder": "^1.3.0"}, "gitHead": "f491ed05323b36c5bea25bddf683e055747042cb"}