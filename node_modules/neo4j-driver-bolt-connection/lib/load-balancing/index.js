"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeastConnectedLoadBalancingStrategy = exports.LoadBalancingStrategy = void 0;
/**
 * Copyright (c) "Neo4j"
 * Neo4j Sweden AB [https://neo4j.com]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var load_balancing_strategy_1 = __importDefault(require("./load-balancing-strategy"));
exports.LoadBalancingStrategy = load_balancing_strategy_1.default;
var least_connected_load_balancing_strategy_1 = __importDefault(require("./least-connected-load-balancing-strategy"));
exports.LeastConnectedLoadBalancingStrategy = least_connected_load_balancing_strategy_1.default;
exports.default = least_connected_load_balancing_strategy_1.default;
