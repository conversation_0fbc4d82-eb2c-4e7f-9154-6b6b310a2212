"use strict";
/**
 * Copyright (c) "Neo4j"
 * Neo4j Sweden AB [https://neo4j.com]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Unpacker = exports.Packer = void 0;
var v1 = __importStar(require("./packstream-v1"));
var Packer = /** @class */ (function (_super) {
    __extends(Packer, _super);
    function Packer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    Packer.prototype.disableByteArrays = function () {
        throw new Error('Bolt V2 should always support byte arrays');
    };
    return Packer;
}(v1.Packer));
exports.Packer = Packer;
var Unpacker = /** @class */ (function (_super) {
    __extends(Unpacker, _super);
    /**
     * @constructor
     * @param {boolean} disableLosslessIntegers if this unpacker should convert all received integers to native JS numbers.
     * @param {boolean} useBigInt if this unpacker should convert all received integers to Bigint
     */
    function Unpacker(disableLosslessIntegers, useBigInt) {
        if (disableLosslessIntegers === void 0) { disableLosslessIntegers = false; }
        if (useBigInt === void 0) { useBigInt = false; }
        return _super.call(this, disableLosslessIntegers, useBigInt) || this;
    }
    return Unpacker;
}(v1.Unpacker));
exports.Unpacker = Unpacker;
