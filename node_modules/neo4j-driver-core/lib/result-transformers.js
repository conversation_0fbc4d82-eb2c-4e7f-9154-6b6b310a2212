"use strict";
/**
 * Copyright (c) "Neo4j"
 * Neo4j Sweden AB [https://neo4j.com]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var result_eager_1 = __importDefault(require("./result-eager"));
var error_1 = require("./error");
/**
 * Protocol for transforming {@link Result}.
 *
 * @typedef {function<T>(result:Result):Promise<T>} ResultTransformer
 * @interface
 *
 * @see {@link resultTransformers} for provided implementations.
 * @see {@link Driver#executeQuery} for usage.
 */
/**
 * Defines the object which holds the common {@link ResultTransformer} used with {@link Driver#executeQuery}.
 */
var ResultTransformers = /** @class */ (function () {
    function ResultTransformers() {
    }
    /**
     * Creates a {@link ResultTransformer} which transforms {@link Result} to {@link EagerResult}
     * by consuming the whole stream.
     *
     * This is the default implementation used in {@link Driver#executeQuery}
     *
     * @example
     * // This:
     * const { keys, records, summary } = await driver.executeQuery('CREATE (p:Person{ name: $name }) RETURN p', { name: 'Person1'}, {
     *   resultTransformer: neo4j.resultTransformers.eagerResultTransformer()
     * })
     * // is equivalent to:
     * const { keys, records, summary } = await driver.executeQuery('CREATE (p:Person{ name: $name }) RETURN p', { name: 'Person1'})
     *
     * @returns {ResultTransformer<EagerResult<Entries>>} The result transformer
     * @alias {@link ResultTransformers#eager}
     */
    ResultTransformers.prototype.eagerResultTransformer = function () {
        return createEagerResultFromResult;
    };
    /**
     * Creates a {@link ResultTransformer} which transforms {@link Result} to {@link EagerResult}
     * by consuming the whole stream.
     *
     * This is the default implementation used in {@link Driver#executeQuery} and a alias to
     * {@link resultTransformers.eagerResultTransformer}
     *
     * @example
     * // This:
     * const { keys, records, summary } = await driver.executeQuery('CREATE (p:Person{ name: $name }) RETURN p', { name: 'Person1'}, {
     *   resultTransformer: neo4j.resultTransformers.eager()
     * })
     * // is equivalent to:
     * const { keys, records, summary } = await driver.executeQuery('CREATE (p:Person{ name: $name }) RETURN p', { name: 'Person1'})
     *
     * @returns {ResultTransformer<EagerResult<Entries>>} The result transformer
     * @experimental this is a preview
     * @since 5.22.0
     * @alias {@link ResultTransformers#eagerResultTransformer}
     */
    ResultTransformers.prototype.eager = function () {
        return createEagerResultFromResult;
    };
    /**
     * Creates a {@link ResultTransformer} which maps the {@link Record} in the result and collects it
     * along with the {@link ResultSummary} and {@link Result#keys}.
     *
     * NOTE: The config object requires map or/and collect to be valid.
     *
     * @example
     * // Mapping the records
     * const { keys, records, summary } = await driver.executeQuery('MATCH (p:Person{ age: $age }) RETURN p.name as name', { age: 25 }, {
     *   resultTransformer: neo4j.resultTransformers.mappedResultTransformer({
     *     map(record) {
     *        return record.get('name')
     *     }
     *   })
     * })
     *
     * records.forEach(name => console.log(`${name} has 25`))
     *
     * @example
     * // Mapping records and collect result
     * const names = await driver.executeQuery('MATCH (p:Person{ age: $age }) RETURN p.name as name', { age: 25 }, {
     *   resultTransformer: neo4j.resultTransformers.mappedResultTransformer({
     *     map(record) {
     *        return record.get('name')
     *     },
     *     collect(records, summary, keys) {
     *        return records
     *     }
     *   })
     * })
     *
     * names.forEach(name => console.log(`${name} has 25`))
     *
     * @example
     * // The transformer can be defined one and used everywhere
     * const getRecordsAsObjects = neo4j.resultTransformers.mappedResultTransformer({
     *   map(record) {
     *      return record.toObject()
     *   },
     *   collect(objects) {
     *      return objects
     *   }
     * })
     *
     * // The usage in a driver.executeQuery
     * const objects = await driver.executeQuery('MATCH (p:Person{ age: $age }) RETURN p.name as name', { age: 25 }, {
     *   resultTransformer: getRecordsAsObjects
     * })
     * objects.forEach(object => console.log(`${object.name} has 25`))
     *
     *
     * // The usage in session.executeRead
     * const objects = await session.executeRead(tx => getRecordsAsObjects(tx.run('MATCH (p:Person{ age: $age }) RETURN p.name as name')))
     * objects.forEach(object => console.log(`${object.name} has 25`))
     *
     * @param {object} config The result transformer configuration
     * @param {function(record:Record):R} [config.map=function(record) {  return record }] Method called for mapping each record
     * @param {function(records:R[], summary:ResultSummary, keys:string[]):T} [config.collect=function(records, summary, keys) { return { records, summary, keys }}] Method called for mapping
     * the result data to the transformer output.
     * @returns {ResultTransformer<T>} The result transformer
     * @see {@link Driver#executeQuery}
     */
    ResultTransformers.prototype.mappedResultTransformer = function (config) {
        return createMappedResultTransformer(config);
    };
    /**
     * Creates a {@link ResultTransformer} which maps the {@link Record} in the result and collects it
     * along with the {@link ResultSummary} and {@link Result#keys}.
     *
     * NOTE: The config object requires map or/and collect to be valid.
     *
     * This method is a alias to {@link ResultTransformers#mappedResultTransformer}
     *
     *
     * @example
     * // Mapping the records
     * const { keys, records, summary } = await driver.executeQuery('MATCH (p:Person{ age: $age }) RETURN p.name as name', { age: 25 }, {
     *   resultTransformer: neo4j.resultTransformers.mapped({
     *     map(record) {
     *        return record.get('name')
     *     }
     *   })
     * })
     *
     * records.forEach(name => console.log(`${name} has 25`))
     *
     * @example
     * // Mapping records and collect result
     * const names = await driver.executeQuery('MATCH (p:Person{ age: $age }) RETURN p.name as name', { age: 25 }, {
     *   resultTransformer: neo4j.resultTransformers.mapped({
     *     map(record) {
     *        return record.get('name')
     *     },
     *     collect(records, summary, keys) {
     *        return records
     *     }
     *   })
     * })
     *
     * names.forEach(name => console.log(`${name} has 25`))
     *
     * @example
     * // The transformer can be defined one and used everywhere
     * const getRecordsAsObjects = neo4j.resultTransformers.mapped({
     *   map(record) {
     *      return record.toObject()
     *   },
     *   collect(objects) {
     *      return objects
     *   }
     * })
     *
     * // The usage in a driver.executeQuery
     * const objects = await driver.executeQuery('MATCH (p:Person{ age: $age }) RETURN p.name as name', { age: 25 }, {
     *   resultTransformer: getRecordsAsObjects
     * })
     * objects.forEach(object => console.log(`${object.name} has 25`))
     *
     *
     * // The usage in session.executeRead
     * const objects = await session.executeRead(tx => getRecordsAsObjects(tx.run('MATCH (p:Person{ age: $age }) RETURN p.name as name')))
     * objects.forEach(object => console.log(`${object.name} has 25`))
     *
     * @param {object} config The result transformer configuration
     * @param {function(record:Record):R} [config.map=function(record) {  return record }] Method called for mapping each record
     * @param {function(records:R[], summary:ResultSummary, keys:string[]):T} [config.collect=function(records, summary, keys) { return { records, summary, keys }}] Method called for mapping
     * the result data to the transformer output.
     * @returns {ResultTransformer<T>} The result transformer
     * @experimental This is a preview feature
     * @alias {@link ResultTransformers#mappedResultTransformer}
     * @since 5.22.0
     * @see {@link Driver#executeQuery}
     */
    ResultTransformers.prototype.mapped = function (config) {
        return createMappedResultTransformer(config);
    };
    /**
     * Creates a {@link ResultTransformer} which collects the first record {@link Record} of {@link Result}
     * and discard the rest of the records, if existent.
     *
     * @example
     * // Using in executeQuery
     * const maybeFirstRecord = await driver.executeQuery('MATCH (p:Person{ age: $age }) RETURN p.name as name', { age: 25 }, {
     *   resultTransformer: neo4j.resultTransformers.first()
     * })
     *
     * @example
     * // Using in other results
     * const record = await neo4j.resultTransformers.first()(result)
     *
     *
     * @template Entries The shape of the record.
     * @returns {ResultTransformer<Record<Entries>|undefined>} The result transformer
     * @see {@link Driver#executeQuery}
     * @experimental This is a preview feature.
     * @since 5.22.0
     */
    ResultTransformers.prototype.first = function () {
        return first;
    };
    /**
     * Creates a {@link ResultTransformer} which consumes the result and returns the {@link ResultSummary}.
     *
     * This result transformer is a shortcut to `(result) => result.summary()`.
     *
     * @example
     * const summary = await driver.executeQuery('CREATE (p:Person{ name: $name }) RETURN p', { name: 'Person1'}, {
     *   resultTransformer: neo4j.resultTransformers.summary()
     * })
     *
     * @returns {ResultTransformer<ResultSummary<T>>} The result transformer
     * @see {@link Driver#executeQuery}
     * @experimental This is a preview feature
     */
    ResultTransformers.prototype.summary = function () {
        return summary;
    };
    return ResultTransformers;
}());
/**
 * Holds the common {@link ResultTransformer} used with {@link Driver#executeQuery}.
 */
var resultTransformers = new ResultTransformers();
Object.freeze(resultTransformers);
exports.default = resultTransformers;
function createEagerResultFromResult(result) {
    return __awaiter(this, void 0, void 0, function () {
        var _a, summary, records, keys;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0: return [4 /*yield*/, result];
                case 1:
                    _a = _b.sent(), summary = _a.summary, records = _a.records;
                    return [4 /*yield*/, result.keys()];
                case 2:
                    keys = _b.sent();
                    return [2 /*return*/, new result_eager_1.default(keys, records, summary)];
            }
        });
    });
}
function createMappedResultTransformer(config) {
    var _this = this;
    if (config == null || (config.collect == null && config.map == null)) {
        throw (0, error_1.newError)('Requires a map or/and a collect functions.');
    }
    return function (result) { return __awaiter(_this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, new Promise(function (resolve, reject) {
                        var state = { records: [], keys: [] };
                        result.subscribe({
                            onKeys: function (keys) {
                                state.keys = keys;
                            },
                            onNext: function (record) {
                                if (config.map != null) {
                                    var mappedRecord = config.map(record);
                                    if (mappedRecord !== undefined) {
                                        state.records.push(mappedRecord);
                                    }
                                }
                                else {
                                    state.records.push(record);
                                }
                            },
                            onCompleted: function (summary) {
                                if (config.collect != null) {
                                    resolve(config.collect(state.records, summary, state.keys));
                                }
                                else {
                                    var obj = { records: state.records, summary: summary, keys: state.keys };
                                    resolve(obj);
                                }
                            },
                            onError: function (error) {
                                reject(error);
                            }
                        });
                    })];
                case 1: return [2 /*return*/, _a.sent()];
            }
        });
    }); };
}
function first(result) {
    return __awaiter(this, void 0, void 0, function () {
        var it, _a, value, done;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    it = result[Symbol.asyncIterator]();
                    return [4 /*yield*/, it.next()];
                case 1:
                    _a = _b.sent(), value = _a.value, done = _a.done;
                    _b.label = 2;
                case 2:
                    _b.trys.push([2, , 3, 6]);
                    if (done === true) {
                        return [2 /*return*/, undefined];
                    }
                    return [2 /*return*/, value];
                case 3:
                    if (!(it.return != null)) return [3 /*break*/, 5];
                    return [4 /*yield*/, it.return()];
                case 4:
                    _b.sent();
                    _b.label = 5;
                case 5: return [7 /*endfinally*/];
                case 6: return [2 /*return*/];
            }
        });
    });
}
function summary(result) {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, result.summary()];
                case 1: return [2 /*return*/, _a.sent()];
            }
        });
    });
}
