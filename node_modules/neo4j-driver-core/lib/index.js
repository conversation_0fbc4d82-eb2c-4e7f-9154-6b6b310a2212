"use strict";
/**
 * Copyright (c) "Neo4j"
 * Neo4j Sweden AB [https://neo4j.com]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EagerResult = exports.Result = exports.Stats = exports.QueryStatistics = exports.ProfiledPlan = exports.Plan = exports.GqlStatusObject = exports.Notification = exports.ServerInfo = exports.queryType = exports.ResultSummary = exports.Record = exports.isPathSegment = exports.PathSegment = exports.isPath = exports.Path = exports.isUnboundRelationship = exports.UnboundRelationship = exports.isRelationship = exports.Relationship = exports.isNode = exports.Node = exports.Time = exports.LocalTime = exports.LocalDateTime = exports.isTime = exports.isLocalTime = exports.isLocalDateTime = exports.isDuration = exports.isDateTime = exports.isDate = exports.Duration = exports.DateTime = exports.Date = exports.Point = exports.isPoint = exports.internal = exports.toString = exports.toNumber = exports.inSafeRange = exports.isInt = exports.int = exports.Integer = exports.error = exports.isRetriableError = exports.GQLError = exports.newGQLError = exports.Neo4jError = exports.newError = exports.authTokenManagers = void 0;
exports.resolveCertificateProvider = exports.clientCertificateProviders = exports.notificationFilterMinimumSeverityLevel = exports.notificationFilterDisabledClassification = exports.notificationFilterDisabledCategory = exports.notificationSeverityLevel = exports.notificationClassification = exports.notificationCategory = exports.resultTransformers = exports.routing = exports.staticAuthTokenManager = exports.bookmarkManager = exports.auth = exports.json = exports.driver = exports.types = exports.Driver = exports.Session = exports.TransactionPromise = exports.ManagedTransaction = exports.Transaction = exports.Connection = exports.Releasable = exports.ConnectionProvider = void 0;
var error_1 = require("./error");
Object.defineProperty(exports, "newError", { enumerable: true, get: function () { return error_1.newError; } });
Object.defineProperty(exports, "Neo4jError", { enumerable: true, get: function () { return error_1.Neo4jError; } });
Object.defineProperty(exports, "newGQLError", { enumerable: true, get: function () { return error_1.newGQLError; } });
Object.defineProperty(exports, "GQLError", { enumerable: true, get: function () { return error_1.GQLError; } });
Object.defineProperty(exports, "isRetriableError", { enumerable: true, get: function () { return error_1.isRetriableError; } });
var integer_1 = __importStar(require("./integer"));
exports.Integer = integer_1.default;
Object.defineProperty(exports, "int", { enumerable: true, get: function () { return integer_1.int; } });
Object.defineProperty(exports, "isInt", { enumerable: true, get: function () { return integer_1.isInt; } });
Object.defineProperty(exports, "inSafeRange", { enumerable: true, get: function () { return integer_1.inSafeRange; } });
Object.defineProperty(exports, "toNumber", { enumerable: true, get: function () { return integer_1.toNumber; } });
Object.defineProperty(exports, "toString", { enumerable: true, get: function () { return integer_1.toString; } });
var temporal_types_1 = require("./temporal-types");
Object.defineProperty(exports, "Date", { enumerable: true, get: function () { return temporal_types_1.Date; } });
Object.defineProperty(exports, "DateTime", { enumerable: true, get: function () { return temporal_types_1.DateTime; } });
Object.defineProperty(exports, "Duration", { enumerable: true, get: function () { return temporal_types_1.Duration; } });
Object.defineProperty(exports, "isDate", { enumerable: true, get: function () { return temporal_types_1.isDate; } });
Object.defineProperty(exports, "isDateTime", { enumerable: true, get: function () { return temporal_types_1.isDateTime; } });
Object.defineProperty(exports, "isDuration", { enumerable: true, get: function () { return temporal_types_1.isDuration; } });
Object.defineProperty(exports, "isLocalDateTime", { enumerable: true, get: function () { return temporal_types_1.isLocalDateTime; } });
Object.defineProperty(exports, "isLocalTime", { enumerable: true, get: function () { return temporal_types_1.isLocalTime; } });
Object.defineProperty(exports, "isTime", { enumerable: true, get: function () { return temporal_types_1.isTime; } });
Object.defineProperty(exports, "LocalDateTime", { enumerable: true, get: function () { return temporal_types_1.LocalDateTime; } });
Object.defineProperty(exports, "LocalTime", { enumerable: true, get: function () { return temporal_types_1.LocalTime; } });
Object.defineProperty(exports, "Time", { enumerable: true, get: function () { return temporal_types_1.Time; } });
var graph_types_1 = require("./graph-types");
Object.defineProperty(exports, "Node", { enumerable: true, get: function () { return graph_types_1.Node; } });
Object.defineProperty(exports, "isNode", { enumerable: true, get: function () { return graph_types_1.isNode; } });
Object.defineProperty(exports, "Relationship", { enumerable: true, get: function () { return graph_types_1.Relationship; } });
Object.defineProperty(exports, "isRelationship", { enumerable: true, get: function () { return graph_types_1.isRelationship; } });
Object.defineProperty(exports, "UnboundRelationship", { enumerable: true, get: function () { return graph_types_1.UnboundRelationship; } });
Object.defineProperty(exports, "isUnboundRelationship", { enumerable: true, get: function () { return graph_types_1.isUnboundRelationship; } });
Object.defineProperty(exports, "Path", { enumerable: true, get: function () { return graph_types_1.Path; } });
Object.defineProperty(exports, "isPath", { enumerable: true, get: function () { return graph_types_1.isPath; } });
Object.defineProperty(exports, "PathSegment", { enumerable: true, get: function () { return graph_types_1.PathSegment; } });
Object.defineProperty(exports, "isPathSegment", { enumerable: true, get: function () { return graph_types_1.isPathSegment; } });
var record_1 = __importDefault(require("./record"));
exports.Record = record_1.default;
var spatial_types_1 = require("./spatial-types");
Object.defineProperty(exports, "isPoint", { enumerable: true, get: function () { return spatial_types_1.isPoint; } });
Object.defineProperty(exports, "Point", { enumerable: true, get: function () { return spatial_types_1.Point; } });
var result_summary_1 = __importStar(require("./result-summary"));
exports.ResultSummary = result_summary_1.default;
Object.defineProperty(exports, "queryType", { enumerable: true, get: function () { return result_summary_1.queryType; } });
Object.defineProperty(exports, "ServerInfo", { enumerable: true, get: function () { return result_summary_1.ServerInfo; } });
Object.defineProperty(exports, "Plan", { enumerable: true, get: function () { return result_summary_1.Plan; } });
Object.defineProperty(exports, "ProfiledPlan", { enumerable: true, get: function () { return result_summary_1.ProfiledPlan; } });
Object.defineProperty(exports, "QueryStatistics", { enumerable: true, get: function () { return result_summary_1.QueryStatistics; } });
Object.defineProperty(exports, "Stats", { enumerable: true, get: function () { return result_summary_1.Stats; } });
var notification_1 = __importStar(require("./notification"));
exports.Notification = notification_1.default;
Object.defineProperty(exports, "GqlStatusObject", { enumerable: true, get: function () { return notification_1.GqlStatusObject; } });
Object.defineProperty(exports, "notificationCategory", { enumerable: true, get: function () { return notification_1.notificationCategory; } });
Object.defineProperty(exports, "notificationClassification", { enumerable: true, get: function () { return notification_1.notificationClassification; } });
Object.defineProperty(exports, "notificationSeverityLevel", { enumerable: true, get: function () { return notification_1.notificationSeverityLevel; } });
var notification_filter_1 = require("./notification-filter");
Object.defineProperty(exports, "notificationFilterDisabledCategory", { enumerable: true, get: function () { return notification_filter_1.notificationFilterDisabledCategory; } });
Object.defineProperty(exports, "notificationFilterDisabledClassification", { enumerable: true, get: function () { return notification_filter_1.notificationFilterDisabledClassification; } });
Object.defineProperty(exports, "notificationFilterMinimumSeverityLevel", { enumerable: true, get: function () { return notification_filter_1.notificationFilterMinimumSeverityLevel; } });
var result_1 = __importDefault(require("./result"));
exports.Result = result_1.default;
var result_eager_1 = __importDefault(require("./result-eager"));
exports.EagerResult = result_eager_1.default;
var connection_provider_1 = __importStar(require("./connection-provider"));
exports.ConnectionProvider = connection_provider_1.default;
Object.defineProperty(exports, "Releasable", { enumerable: true, get: function () { return connection_provider_1.Releasable; } });
var connection_1 = __importDefault(require("./connection"));
exports.Connection = connection_1.default;
var transaction_1 = __importDefault(require("./transaction"));
exports.Transaction = transaction_1.default;
var transaction_managed_1 = __importDefault(require("./transaction-managed"));
exports.ManagedTransaction = transaction_managed_1.default;
var transaction_promise_1 = __importDefault(require("./transaction-promise"));
exports.TransactionPromise = transaction_promise_1.default;
var session_1 = __importDefault(require("./session"));
exports.Session = session_1.default;
var driver_1 = __importStar(require("./driver")), driver = driver_1;
exports.Driver = driver_1.default;
exports.driver = driver;
var auth_1 = __importDefault(require("./auth"));
exports.auth = auth_1.default;
var bookmark_manager_1 = require("./bookmark-manager");
Object.defineProperty(exports, "bookmarkManager", { enumerable: true, get: function () { return bookmark_manager_1.bookmarkManager; } });
var auth_token_manager_1 = require("./auth-token-manager");
Object.defineProperty(exports, "authTokenManagers", { enumerable: true, get: function () { return auth_token_manager_1.authTokenManagers; } });
Object.defineProperty(exports, "staticAuthTokenManager", { enumerable: true, get: function () { return auth_token_manager_1.staticAuthTokenManager; } });
var driver_2 = require("./driver");
Object.defineProperty(exports, "routing", { enumerable: true, get: function () { return driver_2.routing; } });
var types = __importStar(require("./types"));
exports.types = types;
var json = __importStar(require("./json"));
exports.json = json;
var result_transformers_1 = __importDefault(require("./result-transformers"));
exports.resultTransformers = result_transformers_1.default;
var client_certificate_1 = require("./client-certificate");
Object.defineProperty(exports, "clientCertificateProviders", { enumerable: true, get: function () { return client_certificate_1.clientCertificateProviders; } });
Object.defineProperty(exports, "resolveCertificateProvider", { enumerable: true, get: function () { return client_certificate_1.resolveCertificateProvider; } });
var internal = __importStar(require("./internal")); // todo: removed afterwards
exports.internal = internal;
/**
 * Object containing string constants representing predefined {@link Neo4jError} codes.
 */
var error = {
    SERVICE_UNAVAILABLE: error_1.SERVICE_UNAVAILABLE,
    SESSION_EXPIRED: error_1.SESSION_EXPIRED,
    PROTOCOL_ERROR: error_1.PROTOCOL_ERROR
};
exports.error = error;
/**
 * @private
 */
var forExport = {
    authTokenManagers: auth_token_manager_1.authTokenManagers,
    newError: error_1.newError,
    Neo4jError: error_1.Neo4jError,
    newGQLError: error_1.newGQLError,
    GQLError: error_1.GQLError,
    isRetriableError: error_1.isRetriableError,
    error: error,
    Integer: integer_1.default,
    int: integer_1.int,
    isInt: integer_1.isInt,
    inSafeRange: integer_1.inSafeRange,
    toNumber: integer_1.toNumber,
    toString: integer_1.toString,
    internal: internal,
    isPoint: spatial_types_1.isPoint,
    Point: spatial_types_1.Point,
    Date: temporal_types_1.Date,
    DateTime: temporal_types_1.DateTime,
    Duration: temporal_types_1.Duration,
    isDate: temporal_types_1.isDate,
    isDateTime: temporal_types_1.isDateTime,
    isDuration: temporal_types_1.isDuration,
    isLocalDateTime: temporal_types_1.isLocalDateTime,
    isLocalTime: temporal_types_1.isLocalTime,
    isTime: temporal_types_1.isTime,
    LocalDateTime: temporal_types_1.LocalDateTime,
    LocalTime: temporal_types_1.LocalTime,
    Time: temporal_types_1.Time,
    Node: graph_types_1.Node,
    isNode: graph_types_1.isNode,
    Relationship: graph_types_1.Relationship,
    isRelationship: graph_types_1.isRelationship,
    UnboundRelationship: graph_types_1.UnboundRelationship,
    isUnboundRelationship: graph_types_1.isUnboundRelationship,
    Path: graph_types_1.Path,
    isPath: graph_types_1.isPath,
    PathSegment: graph_types_1.PathSegment,
    isPathSegment: graph_types_1.isPathSegment,
    Record: record_1.default,
    ResultSummary: result_summary_1.default,
    queryType: result_summary_1.queryType,
    ServerInfo: result_summary_1.ServerInfo,
    Notification: notification_1.default,
    GqlStatusObject: notification_1.GqlStatusObject,
    Plan: result_summary_1.Plan,
    ProfiledPlan: result_summary_1.ProfiledPlan,
    QueryStatistics: result_summary_1.QueryStatistics,
    Stats: result_summary_1.Stats,
    Result: result_1.default,
    EagerResult: result_eager_1.default,
    Transaction: transaction_1.default,
    ManagedTransaction: transaction_managed_1.default,
    TransactionPromise: transaction_promise_1.default,
    Session: session_1.default,
    Driver: driver_1.default,
    Connection: connection_1.default,
    Releasable: connection_provider_1.Releasable,
    types: types,
    driver: driver,
    json: json,
    auth: auth_1.default,
    bookmarkManager: bookmark_manager_1.bookmarkManager,
    routing: driver_2.routing,
    resultTransformers: result_transformers_1.default,
    notificationCategory: notification_1.notificationCategory,
    notificationClassification: notification_1.notificationClassification,
    notificationSeverityLevel: notification_1.notificationSeverityLevel,
    notificationFilterDisabledCategory: notification_filter_1.notificationFilterDisabledCategory,
    notificationFilterDisabledClassification: notification_filter_1.notificationFilterDisabledClassification,
    notificationFilterMinimumSeverityLevel: notification_filter_1.notificationFilterMinimumSeverityLevel,
    clientCertificateProviders: client_certificate_1.clientCertificateProviders,
    resolveCertificateProvider: client_certificate_1.resolveCertificateProvider
};
exports.default = forExport;
