"use strict";
/**
 * Copyright (c) "Neo4j"
 * Neo4j Sweden AB [https://neo4j.com]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var constants_1 = require("./constants");
var QueryExecutor = /** @class */ (function () {
    function QueryExecutor(_createSession) {
        this._createSession = _createSession;
    }
    QueryExecutor.prototype.execute = function (config, query, parameters) {
        return __awaiter(this, void 0, void 0, function () {
            var session, listenerHandle, executeInTransaction;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        session = this._createSession({
                            database: config.database,
                            bookmarkManager: config.bookmarkManager,
                            impersonatedUser: config.impersonatedUser,
                            auth: config.auth
                        });
                        listenerHandle = installEventListenerWhenPossible(
                        // Solving linter and types definitions issue
                        config.signal, 'abort', function () { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                            switch (_a.label) {
                                case 0: return [4 /*yield*/, session.close()];
                                case 1: return [2 /*return*/, _a.sent()];
                            }
                        }); }); });
                        // @ts-expect-error The method is private for external users
                        session._configureTransactionExecutor(true, constants_1.TELEMETRY_APIS.EXECUTE_QUERY);
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, , 3, 5]);
                        executeInTransaction = config.routing === 'READ'
                            ? session.executeRead.bind(session)
                            : session.executeWrite.bind(session);
                        return [4 /*yield*/, executeInTransaction(function (tx) { return __awaiter(_this, void 0, void 0, function () {
                                var result;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0:
                                            result = tx.run(query, parameters);
                                            return [4 /*yield*/, config.resultTransformer(result)];
                                        case 1: return [2 /*return*/, _a.sent()];
                                    }
                                });
                            }); }, config.transactionConfig)];
                    case 2: return [2 /*return*/, _a.sent()];
                    case 3:
                        listenerHandle.uninstall();
                        return [4 /*yield*/, session.close()];
                    case 4:
                        _a.sent();
                        return [7 /*endfinally*/];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    return QueryExecutor;
}());
exports.default = QueryExecutor;
function installEventListenerWhenPossible(target, event, listener) {
    if (typeof (target === null || target === void 0 ? void 0 : target.addEventListener) === 'function') {
        target.addEventListener(event, listener);
    }
    return {
        uninstall: function () {
            if (typeof (target === null || target === void 0 ? void 0 : target.removeEventListener) === 'function') {
                target.removeEventListener(event, listener);
            }
        }
    };
}
