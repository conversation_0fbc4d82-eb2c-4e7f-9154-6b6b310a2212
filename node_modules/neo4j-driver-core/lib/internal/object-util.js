"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBrokenObjectReason = exports.isBrokenObject = exports.createBrokenObject = void 0;
/**
 * Copyright (c) "Neo4j"
 * Neo4j Sweden AB [https://neo4j.com]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
// eslint-disable-next-line @typescript-eslint/naming-convention
var __isBrokenObject__ = '__isBrokenObject__';
// eslint-disable-next-line @typescript-eslint/naming-convention
var __reason__ = '__reason__';
/**
 * Creates a object which all method call will throw the given error
 *
 * @param {Error} error The error
 * @param {any} object The object. Default: {}
 * @returns {any} A broken object
 */
function createBrokenObject(error, object) {
    if (object === void 0) { object = {}; }
    var fail = function () {
        throw error;
    };
    return new Proxy(object, {
        get: function (_, p) {
            if (p === __isBrokenObject__) {
                return true;
            }
            else if (p === __reason__) {
                return error;
            }
            else if (p === 'toJSON') {
                return undefined;
            }
            fail();
        },
        set: fail,
        apply: fail,
        construct: fail,
        defineProperty: fail,
        deleteProperty: fail,
        getOwnPropertyDescriptor: fail,
        getPrototypeOf: fail,
        has: fail,
        isExtensible: fail,
        ownKeys: fail,
        preventExtensions: fail,
        setPrototypeOf: fail
    });
}
exports.createBrokenObject = createBrokenObject;
/**
 * Verifies if it is a Broken Object
 * @param {any} object The object
 * @returns {boolean} If it was created with createBrokenObject
 */
function isBrokenObject(object) {
    return object !== null && typeof object === 'object' && object[__isBrokenObject__] === true;
}
exports.isBrokenObject = isBrokenObject;
/**
 * Returns if the reason the object is broken.
 *
 * This method should only be called with instances create with {@link createBrokenObject}
 *
 * @param {any} object The object
 * @returns {Error} The reason the object is broken
 */
function getBrokenObjectReason(object) {
    return object[__reason__];
}
exports.getBrokenObjectReason = getBrokenObjectReason;
