"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isPoint = exports.Point = void 0;
/**
 * Copyright (c) "Neo4j"
 * Neo4j Sweden AB [https://neo4j.com]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var util_1 = require("./internal/util");
var POINT_IDENTIFIER_PROPERTY = '__isPoint__';
/**
 * Represents a single two or three-dimensional point in a particular coordinate reference system.
 * Created `Point` objects are frozen with `Object.freeze()` in constructor and thus immutable.
 */
var Point = /** @class */ (function () {
    /**
     * @constructor
     * @param {T} srid - The coordinate reference system identifier.
     * @param {number} x - The `x` coordinate of the point.
     * @param {number} y - The `y` coordinate of the point.
     * @param {number} [z=undefined] - The `z` coordinate of the point or `undefined` if point has 2 dimensions.
     */
    function Point(srid, x, y, z) {
        /**
         * The coordinate reference system identifier.
         * @type {T}
         */
        this.srid = (0, util_1.assertNumberOrInteger)(srid, 'SRID');
        /**
         * The `x` coordinate of the point.
         * @type {number}
         */
        this.x = (0, util_1.assertNumber)(x, 'X coordinate');
        /**
         * The `y` coordinate of the point.
         * @type {number}
         */
        this.y = (0, util_1.assertNumber)(y, 'Y coordinate');
        /**
         * The `z` coordinate of the point or `undefined` if point is 2-dimensional.
         * @type {number}
         */
        this.z = z === null || z === undefined ? z : (0, util_1.assertNumber)(z, 'Z coordinate');
        Object.freeze(this);
    }
    /**
     * @ignore
     */
    Point.prototype.toString = function () {
        return this.z != null && !isNaN(this.z)
            ? "Point{srid=".concat(formatAsFloat(this.srid), ", x=").concat(formatAsFloat(this.x), ", y=").concat(formatAsFloat(this.y), ", z=").concat(formatAsFloat(this.z), "}")
            : "Point{srid=".concat(formatAsFloat(this.srid), ", x=").concat(formatAsFloat(this.x), ", y=").concat(formatAsFloat(this.y), "}");
    };
    return Point;
}());
exports.Point = Point;
function formatAsFloat(number) {
    return Number.isInteger(number) ? number.toString() + '.0' : number.toString();
}
Object.defineProperty(Point.prototype, POINT_IDENTIFIER_PROPERTY, {
    value: true,
    enumerable: false,
    configurable: false,
    writable: false
});
/**
 * Test if given object is an instance of {@link Point} class.
 * @param {Object} obj the object to test.
 * @return {boolean} `true` if given object is a {@link Point}, `false` otherwise.
 */
function isPoint(obj) {
    var anyObj = obj;
    return obj != null && anyObj[POINT_IDENTIFIER_PROPERTY] === true;
}
exports.isPoint = isPoint;
