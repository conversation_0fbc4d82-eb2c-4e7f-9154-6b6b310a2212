/**
 * Copyright (c) "Neo4j"
 * Neo4j Sweden AB [https://neo4j.com]
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @property {function(username: string, password: string, realm: ?string)} basic the function to create a
 * basic authentication token.
 * @property {function(base64EncodedTicket: string)} kerberos the function to create a Kerberos authentication token.
 * Accepts a single string argument - base64 encoded Kerberos ticket.
 * @property {function(base64EncodedTicket: string)} bearer the function to create a Bearer authentication token.
 * Accepts a single string argument - base64 encoded Bearer ticket.
 * @property {function(principal: string, credentials: string, realm: string, scheme: string, parameters: ?object)} custom
 * the function to create a custom authentication token.
 */
declare const auth: {
    basic: (username: string, password: string, realm?: string) => {
        scheme: string;
        principal: string;
        credentials: string;
        realm: string;
    } | {
        scheme: string;
        principal: string;
        credentials: string;
        realm?: undefined;
    };
    kerberos: (base64EncodedTicket: string) => {
        scheme: string;
        principal: string;
        credentials: string;
    };
    bearer: (base64EncodedToken: string) => {
        scheme: string;
        credentials: string;
    };
    none: () => {
        scheme: string;
    };
    custom: (principal: string, credentials: string, realm: string, scheme: string, parameters?: object) => any;
};
export default auth;
