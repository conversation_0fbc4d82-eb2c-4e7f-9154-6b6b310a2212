{"name": "neo4j-driver-core", "version": "5.28.1", "description": "Internals of neo4j-driver", "main": "lib/index.js", "types": "types/index.d.ts", "scripts": {"build": "tsc -p tsconfig.build.json", "build::es6": "tsc -p tsconfig.build.json --target ES6 --outdir lib6", "test": "jest", "test::watch": "jest --watch", "test::unit": "npm run test", "test::deno": "deno test --allow-all ./test/deno/", "predocs": "npm run build && npm run build::es6", "docs": "esdoc -c esdoc.json", "prepare": "npm run build", "clean": "rm -fr node_modules lib types"}, "repository": {"type": "git", "url": "git://github.com/neo4j/neo4j-javascript-driver.git"}, "keywords": ["neo4j", "driver"], "browser": {"./lib/internal/bolt-agent/index.js": "./lib/internal/bolt-agent/browser/index.js"}, "author": "Neo4j", "license": "Apache-2.0", "bugs": {"url": "https://github.com/neo4j/neo4j-javascript-driver/issues"}, "homepage": "https://github.com/neo4j/neo4j-javascript-driver#readme", "devDependencies": {"@types/jest": "^29.5.10", "esdoc": "^1.1.0", "esdoc-importpath-plugin": "^1.0.2", "esdoc-standard-plugin": "^1.0.0", "fast-check": "^3.14.0", "jest": "^29.7.0", "source-map": "0.7.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^4.9.5"}, "gitHead": "f491ed05323b36c5bea25bddf683e055747042cb"}