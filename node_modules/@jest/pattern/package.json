{"name": "@jest/pattern", "version": "30.0.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-pattern"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@types/node": "*", "jest-regex-util": "30.0.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418"}