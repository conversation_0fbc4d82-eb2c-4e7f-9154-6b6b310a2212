{"name": "neo4j-driver", "version": "5.28.1", "description": "The official Neo4j driver for Javascript", "author": "Neo4j", "license": "Apache-2.0", "repository": {"type": "git", "url": "git://github.com/neo4j/neo4j-javascript-driver.git"}, "scripts": {"lint": "eslint --fix --ext .js ./", "test": "gulp test", "test::unit": "gulp test-nodejs-unit && gulp run-ts-declaration-tests", "test::browser": "gulp test-browser", "test::integration": "gulp test-nodejs-integration", "test::stress": "gulp run-stress-tests-without-jasmine", "build": "gulp all", "start-neo4j": "gulp start-neo4j", "stop-neo4j": "gulp stop-neo4j", "run-stress-tests": "gulp run-stress-tests-without-jasmine", "run-ts-declaration-tests": "gulp run-ts-declaration-tests", "docs": "esdoc -c esdoc.json", "versionRelease": "gulp set --x $VERSION && npm version $VERSION --no-git-tag-version", "browser": "gulp browser && gulp test-browser", "prepare": "npm run build", "clean": "rm -fr node_modules lib build"}, "main": "lib/index.js", "browser": {"./lib/internal/node/index.js": "./lib/internal/browser/index.js"}, "unpkg": "lib/browser/neo4j-web.js", "jsdelivr": "lib/browser/neo4j-web.js", "types": "types/index.d.ts", "devDependencies": {"@babel/core": "^7.20.7", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/register": "^7.18.9", "@rollup/plugin-commonjs": "^21.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "@types/jasmine": "^4.3.1", "async": "^3.2.5", "browserify-sign": "^4.2.2", "esdoc": "^1.1.0", "esdoc-importpath-plugin": "^1.0.2", "esdoc-standard-plugin": "^1.0.0", "fancy-log": "^2.0.0", "fs-extra": "^11.1.1", "gulp": "^4.0.2", "gulp-batch": "^1.0.5", "gulp-decompress": "^3.0.0", "gulp-download": "^0.0.1", "gulp-file": "^0.4.0", "gulp-install": "^1.1.0", "gulp-jasmine": "^4.0.0", "gulp-replace": "^1.1.4", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "gulp-watch": "^5.0.1", "jasmine-spec-reporter": "^7.0.0", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^5.1.0", "karma-source-map-support": "^1.4.0", "karma-spec-reporter": "^0.0.36", "karma-typescript": "^5.5.4", "karma-typescript-es6-transform": "^5.5.4", "lolex": "^6.0.0", "minimist": "^1.2.8", "rollup": "^2.77.4-1", "rollup-plugin-polyfill-node": "^0.11.0", "semver": "^7.5.4", "source-map": "0.7.4", "testcontainers": "^8.16.0", "tmp": "0.2.1", "typescript": "^4.9.5", "vinyl-buffer": "^1.0.1", "webpack": "^5.89.0"}, "dependencies": {"neo4j-driver-bolt-connection": "5.28.1", "neo4j-driver-core": "5.28.1", "rxjs": "^7.8.1"}, "gitHead": "f491ed05323b36c5bea25bddf683e055747042cb"}