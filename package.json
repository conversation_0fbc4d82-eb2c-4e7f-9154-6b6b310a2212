{"name": "sol-contract-2-graph-db", "version": "1.0.0", "description": "Solidity contract analysis to graph database", "main": "dist/index.js", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "ts-node src/index.ts", "build": "tsc -p tsconfig.json", "test": "jest", "lint": "eslint . --ext .ts", "format": "prettier -w .", "foundry:install": "echo 'Please install Foundry: https://book.getfoundry.sh/getting-started/installation' && curl -L https://foundry.paradigm.xyz | bash", "foundry:update": "forge update", "flatten": "ts-node src/scripts/flatten.ts", "parse": "ts-node src/scripts/parse.ts"}, "keywords": ["solidity", "smart-contracts", "graph-database", "neo4j", "slither"], "author": "", "license": "MIT", "dependencies": {"neo4j-driver": "^5.28.1", "dotenv": "^16.4.1", "minimist": "^1.2.8"}, "devDependencies": {"@jest/globals": "^30.0.0-beta.3", "@types/jest": "^29.5.14", "@types/minimist": "^1.2.5", "@types/node": "^22.15.29", "@types/yargs": "^17.0.24", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}