// SPDX-License-Identifier: BUSL-1.1
/*
Licensor:           Moai Labs LLC
Licensed Works:     This Contract
Change Date:        4 years after initial deployment of this contract.
Change License:     GNU General Public License v2.0 or later
*/
pragma solidity 0.8.24;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/// @title UpsideLinkToken
/// @notice ERC20 token whose transfers are restricted by an external protocol whitelist
contract UpsideMetaCoin is ERC20, ERC20Permit, Ownable {
    IUpsideProtocol public immutable upsideProtocol;

    error NonTransferable();

    string private _customName;
    string private _customSymbol;

    event NameAndSymbolSet(string newName, string newSymbol);

    /// @notice Constructor
    /// @param _name The initial token name
    /// @param _symbol The initial token symbol
    /// @param _totalSupply Initial total supply minted to deployer
    /// @param _upsideProtocol Address of the UpsideProtocol contract
    constructor(
        string memory _name,
        string memory _symbol,
        uint256 _totalSupply,
        address _upsideProtocol
    ) ERC20(_name, _symbol) Ownable(msg.sender) ERC20Permit(_name) {
        upsideProtocol = IUpsideProtocol(_upsideProtocol);
        _customName = _name;
        _customSymbol = _symbol;
        _mint(msg.sender, _totalSupply);
    }

    /// @notice Returns the token name (overridden)
    function name() public view override returns (string memory) {
        return _customName;
    }

    /// @notice Returns the token symbol (overridden)
    function symbol() public view override returns (string memory) {
        return _customSymbol;
    }

    /// @notice Update the token name and symbol (owner only)
    /// @dev It is known that Permit may not work correctly if the token name is updated
    /// @dev without some custom implementation on the user's side/app side
    function setNameAndSymbol(string memory _name, string memory _symbol) external onlyOwner {
        _customName = _name;
        _customSymbol = _symbol;

        emit NameAndSymbolSet(_name, _symbol);
    }

    // Modifier that checks whether transfers are allowed based on the whitelist
    modifier isTransferAllowed() {
        if (
            !upsideProtocol.metaCoinWhitelist(address(this), msg.sender) &&
            !upsideProtocol.metaCoinInfoMap(address(this)).isFreelyTransferable
        ) revert NonTransferable();
        _;
    }

    // Override transfer with the new modifier
    function transfer(address to, uint256 amount) public override isTransferAllowed returns (bool) {
        _transfer(_msgSender(), to, amount);
        return true;
    }

    // Override transferFrom with the new modifier, checking the sender address
    // @dev Intention is to allow specific contracts to transfer tokens (eg Uniswap, StakingContract, etc)
    function transferFrom(address from, address to, uint256 amount) public override isTransferAllowed returns (bool) {
        uint256 currentAllowance = allowance(from, _msgSender());
        require(currentAllowance >= amount, "ERC20: transfer amount exceeds allowance");
        _approve(from, _msgSender(), currentAllowance - amount);
        _transfer(from, to, amount);
        return true;
    }
}

interface IUpsideProtocol {
    struct MetaCoinInfo {
        address deployer;
        bool isFreelyTransferable;
        uint256 liquidityTokenReserves;
        uint256 metaCoinReserves;
        uint256 createdAtUnix;
    }

    function metaCoinInfoMap(address token) external returns (MetaCoinInfo memory);
    function metaCoinWhitelist(address token, address wallet) external view returns (bool);
}

/*
Business Source License 1.1

License text copyright (c) 2017 MariaDB Corporation Ab, All Rights Reserved. “Business Source License” is a trademark of MariaDB Corporation Ab.

The Licensor hereby grants you the right to copy, modify, create derivative works, redistribute, and make non-production use of the Licensed Work. The Licensor may make an Additional Use Grant, above, permitting limited production use.

Effective on the Change Date, or the fourth anniversary of the first publicly available distribution of a specific version of the Licensed Work under this License, whichever comes first, the Licensor hereby grants you rights under the terms of the Change License, and the rights granted in the paragraph above terminate.

If your use of the Licensed Work does not comply with the requirements currently in effect as described in this License, you must purchase a commercial license from the Licensor, its affiliated entities, or authorized resellers, or you must refrain from using the Licensed Work.

All copies of the original and modified Licensed Work, and derivative works of the Licensed Work, are subject to this License. This License applies separately for each version of the Licensed Work and the Change Date may vary for each version of the Licensed Work released by Licensor.

You must conspicuously display this License on each original or modified copy of the Licensed Work. If you receive the Licensed Work in original or modified form from a third party, the terms and conditions set forth in this License apply to your use of that work.

Any use of the Licensed Work in violation of this License will automatically terminate your rights under this License for the current and all other versions of the Licensed Work.

This License does not grant you any right in any trademark or logo of Licensor or its affiliates (provided that you may use a trademark or logo of Licensor as expressly required by this License).

TO THE EXTENT PERMITTED BY APPLICABLE LAW, THE LICENSED WORK IS PROVIDED ON AN “AS IS” BASIS. LICENSOR HEREBY DISCLAIMS ALL WARRANTIES AND CONDITIONS, EXPRESS OR IMPLIED, INCLUDING (WITHOUT LIMITATION) WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, NON-INFRINGEMENT, AND TITLE.

MariaDB hereby grants you permission to use this License’s text to license your works, and to refer to it using the trademark “Business Source License”, as long as you comply with the Covenants of Licensor below.

Covenants of Licensor

In consideration of the right to use this License’s text and the “Business Source License” name and trademark, Licensor covenants to MariaDB, and to all other recipients of the licensed work to be provided by Licensor:

To specify as the Change License the GPL Version 2.0 or any later version, or a license that is compatible with GPL Version 2.0 or a later version, where “compatible” means that software provided under the Change License can be included in a program with software provided under GPL Version 2.0 or a later version. Licensor may specify additional Change Licenses without limitation.

To either: (a) specify an additional grant of rights to use that does not impose any additional restriction on the right granted in this License, as the Additional Use Grant; or (b) insert the text “None”.

To specify a Change Date.

Not to modify this License in any other way.

Notice

The Business Source License (this document, or the “License”) is not an Open Source license. However, the Licensed Work will eventually be made available under an Open Source License, as stated in this License.
*/
