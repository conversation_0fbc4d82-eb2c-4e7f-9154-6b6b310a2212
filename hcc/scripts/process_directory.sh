#!/bin/bash

# Check if directory is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <directory_path>"
    exit 1
fi

# Get absolute path of the directory
DIR_PATH=$(realpath "$1")

# Create output directory if it doesn't exist
OUTPUT_DIR="$DIR_PATH/analyzed"
mkdir -p "$OUTPUT_DIR"

# Create a temporary directory for processing
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

# Find all .sol files and prepare them for processing
find "$DIR_PATH" -name "*.sol" | while read -r file; do
    # Copy file to temp dir with unique name to avoid path issues
    cp "$file" "$TEMP_DIR/$(basename "$file")"
done

# Check if we found any .sol files
if [ -z "$(ls -A "$TEMP_DIR"/*.sol 2>/dev/null)" ]; then
    echo "No .sol files found in $DIR_PATH"
    exit 0
fi

echo "Found $(ls -1 "$TEMP_DIR"/*.sol | wc -l) .sol files to process"

# Process all files in a single Docker container
for file in "$TEMP_DIR"/*.sol; do
    filename=$(basename "$file")
    echo "Processing: $filename"
    
    # Run the analysis and capture output
    docker run --rm -v "$TEMP_DIR:/contract" hcc "hcc_analysis --re --io /contract/$filename" > "$TEMP_DIR/${filename%.sol}_analysis.txt"
    
    # Check if patched file was created
    if [ -f "$TEMP_DIR/${filename%.sol}_patched.sol" ]; then
        # Move patched file to have the same name as original for later organization
        mv "$TEMP_DIR/${filename%.sol}_patched.sol" "$TEMP_DIR/patched_$filename"
    fi
done

# Organize the output files
echo "Organizing output files..."
find "$DIR_PATH" -name "*.sol" | while read -r orig_file; do
    filename=$(basename "$orig_file")
    rel_path="${orig_file#$DIR_PATH/}"
    rel_dir=$(dirname "$rel_path")
    
    # Create output directories
    out_dir="$OUTPUT_DIR/$rel_dir"
    mkdir -p "$out_dir"
    
    # Move analysis results
    if [ -f "$TEMP_DIR/${filename%.sol}_analysis.txt" ]; then
        mv "$TEMP_DIR/${filename%.sol}_analysis.txt" "$out_dir/"
    fi
    
    # Move patched files
    if [ -f "$TEMP_DIR/patched_$filename" ]; then
        mkdir -p "$out_dir/patched/$rel_dir"
        mv "$TEMP_DIR/patched_$filename" "$out_dir/patched/$rel_path"
    fi
done

echo "Analysis complete. Results are in: $OUTPUT_DIR"
