version: '3.8'

services:
  # CPG Contract Checker Application
  cpg-contract-checker:
    build:
      context: .
      dockerfile: Dockerfile
    networks:
      - cpg-network
    environment:
      # Connection settings - use service name in Docker network
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_URL=bolt://neo4j:7687
      - NEO4J_BOLT_URL=bolt://neo4j:7687
      - NEO4J_HOST=neo4j
      - NEO4J_SERVER=neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - NEO4J_BOLT_PORT=7687
      - NEO4J_HTTP_PORT=7474
      
      # Additional connection settings
      - NEO4J_dbms_connector_bolt_enabled=true
      - NEO4J_dbms_connector_http_enabled=true
      
      # Allow file system access
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      
      # Security
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=*
      
      # Performance
      - NEO4J_dbms_memory_pagecache_size=512M
      - NEO4J_dbms_memory_heap_initial__size=512M
      - NEO4J_dbms_memory_heap_max__size=1G
      
    volumes:
      - ./contracts:/contracts:ro
      
    depends_on:
      neo4j:
        condition: service_healthy
  
  # Neo4j Database
  neo4j:
    image: neo4j:5.3
    container_name: cpg-neo4j
    hostname: neo4j
    networks:
      - cpg-network
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      # Authentication
      - NEO4J_AUTH=neo4j/password
      
      # Network settings
      - NEO4J_dbms_connector_bolt_listen__address=0.0.0.0:7687
      - NEO4J_dbms_connector_http_listen__address=0.0.0.0:7474
      - NEO4J_dbms_connector_https_listen__address=0.0.0.0:7473
      
      # Enable APOC plugin
      - NEO4J_PLUGINS=["apoc"]
      
      # Security settings
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*,db.*,apoc.coll.*,apoc.load.*,apoc.trigger.*,apoc.json.*,apoc.util.*,apoc.cypher.*,apoc.export.*,apoc.import.*
      
      # APOC settings
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_apoc_import_file_allow__read__from__filesystem=true
      
      # Memory settings
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=1G
      - NEO4J_dbms_memory_pagecache_size=512m
      
      # Additional Neo4j settings
      - NEO4J_dbms_default__database=neo4j
      - NEO4J_dbms_default__listen__address=0.0.0.0
      
    volumes:
      - neo4j_data:/data
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_logs:/logs
      - neo4j_plugins:/plugins
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "password", "--format", "plain", "RETURN 1"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  cpg-network:
    driver: bridge

volumes:
  neo4j_data:
  neo4j_import:
  neo4j_logs:
  neo4j_plugins:
