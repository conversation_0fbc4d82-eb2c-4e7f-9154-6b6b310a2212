# Server configuration
server.default_database=neo4j

# Security settings
dbms.security.procedures.unrestricted=apoc.*,gds.*
dbms.security.procedures.allowlist=apoc.*,gds.*

# Allow file system access for imports/exports
server.config.strict_validation.enabled=false

# File import/export
server.directories.import=/import
server.directories.plugins=/plugins

# APOC configuration
# APOC Configuration
apoc.import.file.enabled=true
apoc.export.file.enabled=true
apoc.import.file.use_neo4j_config=true
apoc.import.file.allow_read_from_filesystem=true
apoc.trigger.enabled=true
apoc.uuid.enabled=true
apoc.import.file.allow_reading_from_filesystem=true
apoc.export.file.allow_overwrite=true
apoc.import.file.allow_reading_from_filesystem=true
apoc.cypher.mapExpression={
  "apoc.convert.toMap": "apoc.map.clean({0},[''],[''])",
  "apoc.map.fromPairs": "apoc.map.clean({0},[''],[''])",
  "apoc.map.fromValues": "apoc.map.clean({0},[''],[''])",
  "apoc.map.merge": "apoc.map.clean({0},[''],[''])",
  "apoc.map.mergeList": "apoc.map.clean({0},[''],[''])",
  "apoc.map.fromLists": "apoc.map.clean({0},[''],[''])",
  "apoc.map.groupBy": "apoc.map.clean({0},[''],[''])",
  "apoc.map.groupByMulti": "apoc.map.clean({0},[''],[''])",
  "apoc.map.sortedProperties": "apoc.map.clean({0},[''],[''])"
}

# Memory settings
dbms.memory.heap.initial_size=512m
dbms.memory.heap.max_size=1G
dbms.memory.pagecache.size=512m
db.tx_log.rotation.retention_policy=100M size

server.memory.pagecache.size=512M

server.default_listen_address=0.0.0.0
server.directories.logs=/logs
server.config.strict_validation=false
