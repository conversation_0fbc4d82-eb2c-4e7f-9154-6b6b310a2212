import re
import sys

def clean_hcc_output(input_file, output_file):
    with open(input_file, 'r') as f:
        lines = f.readlines()

    output = []
    current_issue = []
    in_issue_block = False

    # Patterns to identify different sections
    bug_pattern = r'\[  BUG  \]'
    end_pattern = r'\[  HCC  \]|\[ BOOST \]|\[  GC.  \]'
    code_block_pattern = r'^\s+Code$'

    for line in lines:
        if re.search(bug_pattern, line):
            # If we were in an issue block, save it before starting a new one
            if in_issue_block and current_issue:
                output.extend(current_issue)
                output.append('\n' + '-'*80 + '\n')
            current_issue = [line]
            in_issue_block = True
        elif in_issue_block:
            # Check if we've reached the end of an issue block
            if re.search(end_pattern, line) and not re.search(code_block_pattern, line):
                in_issue_block = False
                if current_issue:
                    output.extend(current_issue)
                    output.append('\n' + '-'*80 + '\n')
                    current_issue = []
            else:
                current_issue.append(line)
    
    # Add any remaining issue
    if current_issue:
        output.extend(current_issue)

    # Write the cleaned output
    with open(output_file, 'w') as f:
        f.writelines(output)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python clean_hcc.py <input_file> <output_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    clean_hcc_output(input_file, output_file)
    print(f"Cleaned output saved to: {output_file}")