plugins {
    id 'org.jetbrains.kotlin.jvm' version '1.6.21'
    id 'application'
}

repositories {
    mavenCentral()
}

dependencies {
    implementation platform('org.jetbrains.kotlin:kotlin-bom')
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk8'
    
    implementation 'org.neo4j:neo4j:4.4.9'
    implementation 'com.beust:klaxon:5.6'

    testImplementation 'org.jetbrains.kotlin:kotlin-test'
    testImplementation 'org.jetbrains.kotlin:kotlin-test-junit5'
}

sourceSets {
    main.kotlin.srcDirs += 'src/'
}

application {
    mainClass = 'MainKt'
}

tasks.withType(JavaExec) {
    jvmArgs = (project.hasProperty('jvmArgs') ? 
        project.jvmArgs.split('\s+').toList() : []) + [
        '-Xms4g', 
        '-Xmx14g',
        '--add-opens=java.base/java.nio=ALL-UNNAMED',
        '--add-opens=java.base/sun.nio.ch=ALL-UNNAMED',
        '--add-opens=java.base/java.lang=ALL-UNNAMED'
    ]
}

run {
    standardInput = System.in
}

compileKotlin {
    kotlinOptions {
        languageVersion = "1.6"
        apiVersion = "1.6"
        jvmTarget = "17"
    }
}

test {
    useJUnitPlatform()
}
