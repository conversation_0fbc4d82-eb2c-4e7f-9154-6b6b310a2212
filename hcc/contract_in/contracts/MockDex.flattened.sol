// SPDX-License-Identifier: MIT
pragma solidity =0.8.24;

// contracts/MockDex.sol
// contracts/test/MockDEX.sol

contract MockDEX {
    mapping(string => address) public symbolToToken;
    
    function addToken(string memory symbol, address token) external {
        symbolToToken[symbol] = token;
    }
    
    function getTokenBySymbol(string memory symbol) external view returns (address) {
        return symbolToToken[symbol];
    }
}
