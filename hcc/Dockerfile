# Use base image with multi-architecture support
FROM --platform=linux/amd64 gradle:7.4.2-jdk17

WORKDIR /root

# Install system dependencies including 32-bit support
RUN dpkg --add-architecture i386 && \
    apt-get update && \
    apt-get install -y python3 python3-pip wget git libc6:i386 libgcc-s1:i386 libstdc++6:i386 && \
    rm -rf /var/lib/apt/lists/*

# Download and install solc 0.8.24 for x86_64
RUN wget https://binaries.soliditylang.org/linux-amd64/solc-linux-amd64-v0.8.24+commit.e11b9ed9 -O /usr/local/bin/solc && \
    chmod +x /usr/local/bin/solc && \
    mkdir -p /usr/local/share/solc && \
    ln -s /usr/local/bin/solc /usr/local/share/solc/solc-v0.8.24+commit.e11b9ed9

# Verify solc installation
RUN /usr/local/bin/solc --version

# Copy build files
COPY build.gradle settings.gradle /hcc/
COPY ./src /hcc/src
COPY ./scripts/run.sh /hcc/run.sh
RUN chmod +x /hcc/run.sh

# Set Kotlin language version explicitly
RUN echo "kotlin.code.style=official" > /hcc/gradle.properties && \
    echo "kotlin.jvm.target.validation.mode=warning" >> /hcc/gradle.properties

# Compile
WORKDIR /hcc
RUN gradle clean build --no-daemon --stacktrace --info

# Add JVM arguments for Neo4j compatibility
ENV JAVA_OPTS="--add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED"

ENTRYPOINT ["/hcc/run.sh"]
