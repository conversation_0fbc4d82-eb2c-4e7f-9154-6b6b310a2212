
what we are building here is in this folder hcc-ts

we are building a cpg (control flow graph) for solidity contracts

we are using neo4j as our database

we are using typescript as our programming language

we are using docker to run neo4j

we are using docker compose to run our application

our end goal is to find vulnerbilties in solidity contracts. we will first 
have the contract parsed and then we will build the ast, cpg and then we will run the queries. 
the queries will be run against the cpg and we should find complex vulnerbilitieis based on state changes and various other factors and invarioants.

WE need to basically recreate or get an upgraded version of the HCC project in typescript. The HCC is the entire codebase of this project and we develop the typescript version in the hcc-ts folder. 

it is very important that the final TS product is with similar or better quality as HCC. 

We need to make sure we process all modern solidity data structures and features. 

the product will mostly be working with latest versions of solidity compiler. 

Lets revise the HCC project and make sure we cover all the needed features and data structures, that they integrate. Also all the data flow. 
Check carefully the solidity parser, how we create the AST and CPG. if we need better options we need to find some from external library. on the neo4j we need a comprehensive integration of the CPG, showing useful representation of the entire codebase and sophisticated relationship. not too much and too detailed, but also make sure nothing is missing. 

First make a plan of the project and divide it into smaller tasks. 

Revise the HCC project. Then review what we start with i.e. the HCC-TS. 

Propose a better version of the project if needed. compare with the HCC and strive for excellence. let me know if external tools would be benefitial to perform part of the tasks. 

Once we implement any change on the parser and AST transformations we need to test with a simple test. Every time we add a new node or relationship we need to test with a simple test. and thus extend our test base. this way going forward we will make sure our quality does not drop and we are not introducing bugs.

We want to have a better detection of vulnerabilities than a simple static analyzer. Every bug I find in any blog or contest will be added and should be able to be detected with the graph approach. 

Do not remove any tests.
Avoid installing globally any tools, only to local env or dev env. 

Lets add a simple reentracy or similar contract for analysis and elaborate the state and taint tracking and data flow graph. the this contract will be checked with the queries, when we are ready to test the queries. 
