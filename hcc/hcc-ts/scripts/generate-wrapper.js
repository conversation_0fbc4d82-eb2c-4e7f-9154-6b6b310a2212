"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = generateWrapperContract;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
function generateWrapperContract(folderPath) {
    const files = (0, fs_1.readdirSync)(folderPath)
        .filter(f => f.endsWith('.sol') && f !== 'Wrapper.sol')
        .map(f => `import "./${f}";\n`);
    const content = `// SPDX-License-Identifier: MIT\npragma solidity ^0.8.0;\n\n${files.join('')}\ncontract Wrapper {}\n`;
    const tempPath = path_1.default.join(folderPath, 'Wrapper.sol');
    (0, fs_1.writeFileSync)(tempPath, content);
    return tempPath;
}
