import neo4j, { Driver, Session, Record } from 'neo4j-driver';
import fs from 'fs';
import path from 'path';
import logger from '../logger/logger';

import { QueryMeta } from './resultFormatter';
export interface QueryResult {
  queryName: string;
  result: any;
  error?: string;
  meta?: QueryMeta;
}

export interface QueryRunnerConfig {
  uri: string;
  user: string;
  password: string;
  queriesDir: string;
}

export class QueryRunner {
  private driver: Driver;

  constructor(private config: QueryRunnerConfig) {
    this.driver = neo4j.driver(config.uri, neo4j.auth.basic(config.user, config.password));
  }

  async close() {
    await this.driver.close();
  }

  async runAllQueries(): Promise<QueryResult[]> {
    const queries = this.loadQueries();
    return this.runQueriesInParallel(queries);
  }

  async runQueriesInParallel(
    queries: { name: string; query: string; meta?: QueryMeta }[],
    concurrency: number = 8
  ): Promise<QueryResult[]> {
    logger.info(`Running ${queries.length} queries in parallel (concurrency: ${concurrency})...`);
    const results: QueryResult[] = [];
    let idx = 0;
    const runNext = async (): Promise<void> => {
      while (idx < queries.length) {
        const myIdx = idx++;
        const { name, query, meta } = queries[myIdx];
        const session: Session = this.driver.session();
        try {
          const res = await session.run(query);
          results[myIdx] = { queryName: name, result: res.records.map((r: Record) => r.toObject()), meta };
          logger.info(`Query '${name}' executed successfully.`);
        } catch (err: any) {
          logger.error(`Query '${name}' failed: ${err.message}`);
          results[myIdx] = { queryName: name, result: null, error: err.message, meta };
        } finally {
          await session.close();
        }
      }
    };
    // Launch workers
    await Promise.all(Array(concurrency).fill(0).map(() => runNext()));
    return results;
  }

  loadQueries(): { name: string; query: string; meta?: QueryMeta }[] {
    const files = fs.readdirSync(this.config.queriesDir).filter(f => f.endsWith('.cypher'));
    return files.map(f => {
      const name = path.basename(f, '.cypher');
      const query = fs.readFileSync(path.join(this.config.queriesDir, f), 'utf8');
      let meta: QueryMeta | undefined = undefined;
      const metaPath = path.join(this.config.queriesDir, `${name}.meta.json`);
      if (fs.existsSync(metaPath)) {
        try {
          meta = JSON.parse(fs.readFileSync(metaPath, 'utf8'));
        } catch {}
      }
      return { name, query, meta };
    });
  }

  filterQueriesByTag(tag: string): { name: string; query: string; meta?: QueryMeta }[] {
    return this.loadQueries().filter(q => q.meta?.tags?.includes(tag));
  }
}

