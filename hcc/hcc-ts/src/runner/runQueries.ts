import path from 'path';
import logger from '../logger/logger';
import { QueryRunner } from './queryRunner';
import { checkNeo4jHealth } from '../db/checkNeo4jHealth';
import { formatResults, writeResultsToFile, OutputFormat } from './resultFormatter';
import minimist from 'minimist';

async function main() {
  const argv = minimist(process.argv.slice(2));
  const format: OutputFormat = argv.format || 'text';
  const tag: string | undefined = argv.tag;
  const output: string | undefined = argv.output;
  const concurrency: number = argv.concurrency ? parseInt(argv.concurrency, 10) : 8; // Default to 8
  const checkDb: boolean = argv['check-db'] || false;

  const neo4jConfig = {
    uri: 'bolt://localhost:7687',
    user: 'neo4j',
    password: 'password',
    queriesDir: path.join(__dirname, '../queries')
  };

  if (checkDb) {
    logger.info('Checking Neo4j health...');
    const healthy = await checkNeo4jHealth(neo4jConfig.uri, neo4jConfig.user, neo4jConfig.password);
    if (healthy) {
      logger.info('Neo4j is healthy and ready.');
      process.exit(0);
    } else {
      logger.error('Neo4j is not available or not ready.');
      process.exit(1);
    }
  }

  const runner = new QueryRunner(neo4jConfig);
  try {
    let results;
    if (tag) {
      logger.info(`Filtering queries by tag: ${tag}`);
      const filtered = runner.filterQueriesByTag(tag);
      results = await runner.runQueriesInParallel(filtered, concurrency);
    } else {
      const allQueries = runner.loadQueries();
      results = await runner.runQueriesInParallel(allQueries, concurrency);
    }
    const formatted = formatResults(results, format);
    if (output) {
      writeResultsToFile(results, format, output);
      logger.info(`Results written to ${output}`);
    } else {
      // Print to console
      if (format === 'json' || format === 'markdown') {
        // Print as a block for readability
        console.log('\n' + formatted + '\n');
      } else {
        console.log(formatted);
      }
    }
  } finally {
    await runner.close();
  }
}

main();
