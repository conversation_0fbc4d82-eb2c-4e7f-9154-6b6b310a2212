import { neo4jConfig } from '../config';
import { Neo4jClient } from '../db/neo4j';
import { ControlFlowVisualizer } from '../visualization/controlFlow';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import fs from 'fs';
import { ArgumentsCamelCase } from 'yargs';
import { getSolidityAST } from '../cpg/astToCpg';
import { astToCpg } from '../cpg/astToCpg';

interface QueryCommandArgs {
  file: string;
  output?: string;
}

interface VisualizeCommandArgs {
  function?: string;
  output?: string;
}

interface CPGGraph {
  nodes: any[];
  edges: any[];
  addNode: (node: any) => any;
  addEdge: (edge: any) => void;
  getNode: (id: string) => any;
  getNodeChildren: (id: string) => any[];
}

interface CPGNode {
  id: string;
}

export async function runCli() {
  console.log('CLI starting with args:', process.argv.slice(2));
  const neo4j = new Neo4jClient(neo4jConfig);
  console.log('Neo4j Connection:', await neo4j.verifyConnection());
  const visualizer = new ControlFlowVisualizer(neo4j);

  const argv = await yargs(hideBin(process.argv))
    .command<QueryCommandArgs>('query <file>', 'Run predefined queries', (yargs) => {
      return yargs
        .positional('file', {
          describe: 'Solidity file to analyze',
          type: 'string',
          demandOption: true
        });
    })
    .command<VisualizeCommandArgs>('visualize [function]', 'Generate control flow visualization', (yargs) => {
      return yargs
        .positional('function', {
          describe: 'Function ID to visualize',
          type: 'string'
        });
    })
    .option('output', {
      alias: 'o',
      type: 'string',
      description: 'Output file path'
    })
    .demandCommand(1)
    .parseAsync();

  try {
    if (argv._[0] === 'query') {
      console.log('Processing file:', (argv as ArgumentsCamelCase<QueryCommandArgs>).file);
      await runPredefinedQueries(neo4j, (argv as ArgumentsCamelCase<QueryCommandArgs>).file);
    } else if (argv._[0] === 'visualize') {
      const vizArgs = argv as ArgumentsCamelCase<VisualizeCommandArgs>;
      const dot = await visualizer.generateDotGraph(vizArgs.function);
      
      if (vizArgs.output) {
        fs.writeFileSync(vizArgs.output, dot);
        console.log(`Visualization saved to ${vizArgs.output}`);
      } else {
        console.log(dot);
      }
    }
  } finally {
    await neo4j.close();
  }
}

async function runPredefinedQueries(neo4j: Neo4jClient, filePath: string) {
  console.log('Starting analysis for:', filePath);
  try {
    const ast = await getSolidityAST(filePath);
    console.log('AST nodes:', ast.nodes?.length || 0);
    
    const graph: CPGGraph = {
      nodes: [],
      edges: [],
      addNode: (node) => {
        graph.nodes.push(node);
        return node;
      },
      addEdge: (edge) => {
        graph.edges.push(edge);
      },
      getNode: (id) => {
        return graph.nodes.find(n => n.id === id);
      },
      getNodeChildren: (id) => {
        return graph.edges
          .filter(e => e.source === id)
          .map(e => graph.nodes.find(n => n.id === e.target))
          .filter(Boolean) as CPGNode[];
      }
    };

    const cpg = astToCpg(ast);
    console.log('CPG nodes:', cpg.nodes.length, 'edges:', cpg.edges.length);
    
    await neo4j.importCPG(graph);
    console.log('Neo4j import completed');
  } catch (error) {
    console.error('Analysis failed:', error);
  }
}

// Add to package.json scripts:
// "analyze": "ts-node src/runner/cli.ts query contract.sol",
// "visualize": "ts-node src/runner/cli.ts visualize -o graph.dot"
