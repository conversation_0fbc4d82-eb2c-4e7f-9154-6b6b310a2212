import fs from 'fs';

export type OutputFormat = 'json' | 'markdown' | 'text';

export interface QueryResult {
  queryName: string;
  result: any;
  error?: string;
  meta?: QueryMeta;
}

export interface QueryMeta {
  description?: string;
  severity?: string;
  tags?: string[];
}

export function formatResults(results: QueryResult[], format: OutputFormat = 'text'): string {
  switch (format) {
    case 'json':
      return JSON.stringify(results, null, 2);
    case 'markdown':
      return results.map(r => formatMarkdown(r)).join('\n\n');
    case 'text':
    default:
      return results.map(r => formatText(r)).join('\n');
  }
}

function formatMarkdown(r: QueryResult): string {
  let md = `### Query: ${r.queryName}\n`;
  if (r.meta?.description) md += `**Description:** ${r.meta.description}\n`;
  if (r.meta?.severity) md += `**Severity:** ${r.meta.severity}\n`;
  if (r.meta?.tags) md += `**Tags:** ${r.meta.tags.join(', ')}\n`;
  if (r.error) {
    md += `**Error:** ${r.error}\n`;
  } else {
    md += `**Result:**\n\n
\`
${JSON.stringify(r.result, null, 2)}
\
`;
  }
  return md;
}

function formatText(r: QueryResult): string {
  let txt = `Query: ${r.queryName}`;
  if (r.meta?.description) txt += `\nDescription: ${r.meta.description}`;
  if (r.meta?.severity) txt += `\nSeverity: ${r.meta.severity}`;
  if (r.meta?.tags) txt += `\nTags: ${r.meta.tags.join(', ')}`;
  if (r.error) {
    txt += `\nError: ${r.error}`;
  } else {
    txt += `\nResult:\n${JSON.stringify(r.result, null, 2)}`;
  }
  return txt;
}

export function writeResultsToFile(results: QueryResult[], format: OutputFormat, filePath: string) {
  const output = formatResults(results, format);
  fs.writeFileSync(filePath, output, 'utf8');
}
