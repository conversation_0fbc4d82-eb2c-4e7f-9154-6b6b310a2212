import path from "path"
import logger from "./logger/logger"
import { parseSolidity } from "./ast"
import { astToCpg } from "./cpg/astToCpg"
import { Neo4jClient } from "./db/neo4j"
import { CPGGraph, CPGNode, CPCEdge } from './cpg/cpg';

async function main() {
  try {
    const contractPath = path.join(__dirname, "../contracts/MappingStructTest.sol")
    logger.info(`Parsing Solidity contract: ${path.basename(contractPath)}`)
    const { ast } = await parseSolidity(contractPath)
    logger.info("Building CPG...")
    const cpg = astToCpg(ast)
    logger.info("Importing CPG to Neo4j...")
    const neo4j = new Neo4jClient({
      uri: "bolt://localhost:7687",
      user: "neo4j",
      password: "password",
    })

    const graph: CPGGraph = {
      nodes: [],
      edges: [],
      addNode: (node: CPGNode) => {
        graph.nodes.push(node);
        return node;
      },
      addEdge: (edge: CPCEdge) => {
        graph.edges.push(edge);
      },
      getNode: (id: string) => {
        return graph.nodes.find(n => n.id === id);
      },
      getNodeChildren: (id: string) => {
        return graph.edges
          .filter(e => e.source === id)
          .map(e => graph.nodes.find(n => n.id === e.target))
          .filter(Boolean) as CPGNode[];
      }
    };

    await neo4j.importCPG(graph);
    await neo4j.close()
    logger.info("Done.")
    logger.info("To check your CPG in Neo4j:")
    logger.info("1. Open http://localhost:7474 in your browser")
    logger.info("2. Login with user: neo4j, password: password")
    logger.info("3. Run: MATCH (n:CPGNode) RETURN n LIMIT 25")
    logger.info('   or: MATCH (n:CPGNode {type: "AssemblyBlock"}) RETURN n')
  } catch (error) {
    logger.error(
      "Error in main: " +
        (error instanceof Error ? error.message : String(error))
    )
    process.exit(1)
  }
}

main().catch((err) => {
  logger.error(err)
  process.exit(1)
})
