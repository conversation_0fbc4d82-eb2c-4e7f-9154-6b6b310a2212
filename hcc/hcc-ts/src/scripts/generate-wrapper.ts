import { writeFileSync, readdirSync } from 'fs';
import path from 'path';

export default function generateWrapperContract(folderPath: string): string {
  const files = readdirSync(folderPath)
    .filter(f => f.endsWith('.sol') && f !== 'Wrapper.sol')
    .map(f => `import "./${f}";\n`);
  
  const content = `// SPDX-License-Identifier: MIT\npragma solidity ^0.8.0;\n${files.join('')}`;
  const tempPath = path.join(folderPath, 'Wrapper.sol');
  writeFileSync(tempPath, content);
  return tempPath;
}
