import { spawnSync } from 'child_process';
import { existsSync, mkdirSync, readdirSync, writeFileSync } from 'fs';
import path from 'path';
import Logger from '../logger/logger';
import generateWrapper from '../scripts/generate-wrapper';

export function flattenFolder(folderPath: string): string {
  try {
    if (!existsSync(folderPath)) {
      throw new Error(`Folder does not exist: ${folderPath}`);
    }

    const solFiles = readdirSync(folderPath).filter(f => f.endsWith('.sol'));
    if (solFiles.length === 0) {
      throw new Error(`No Solidity files found in ${folderPath}`);
    }

    const outputDir = path.join(folderPath, 'output');
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir);
    }
    
    const wrapperPath = generateWrapper(folderPath);
    const result = spawnSync('forge', ['flatten', wrapperPath], {
      encoding: 'utf-8',
    });

    if (result.error || result.status !== 0) {
      throw new Error(`Flattening failed: ${result.stderr || result.error}`);
    }

    const outputPath = path.join(outputDir, 'flattened.sol');
    writeFileSync(outputPath, result.stdout);
    Logger.info(`Flattened output saved to: ${outputPath}`);
    return outputPath;
  } catch (error) {
    Logger.error(`Error during folder flattening: ${error}`);
    const message = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Error during folder flattening: ${message}`);
  }
}
