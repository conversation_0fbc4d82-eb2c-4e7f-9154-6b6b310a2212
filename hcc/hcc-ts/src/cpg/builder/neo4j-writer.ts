import { Driver, Session } from 'neo4j-driver';
import { CPGNode, CPCEdge } from '../cpg';
import { Neo4jSchema } from '../schema/neo4j-schema';

export class Neo4jWriter {
  constructor(private readonly driver: Driver) {}

  public async writeNodes(nodes: CPGNode[]): Promise<void> {
    const session = this.driver.session();
    try {
      await Neo4jSchema.createIndexes(this.driver);
      await session.executeWrite(async tx => {
        await tx.run(`
          UNWIND $nodes AS node
          MERGE (n:CPGNode {id: node.id})
          SET n += node.properties
          SET n:${nodes.map(n => n.type).join(':')}
        `, { nodes });
      });
    } finally {
      await session.close();
    }
  }

  public async writeRelationships(edges: CPCEdge[]): Promise<void> {
    const session = this.driver.session();
    try {
      await session.executeWrite(async tx => {
        await tx.run(`
          UNWIND $edges AS edge
          MATCH (a {id: edge.source})
          MATCH (b {id: edge.target})
          MERGE (a)-[r:${edges.map(e => e.type).join('|')}]->(b)
          SET r += edge.properties
        `, { edges });
      });
    } finally {
      await session.close();
    }
  }
}
