import { Driver } from 'neo4j-driver';
import { SlitherOutput } from '../../ast/slither-types';
import { MainTransformer } from '../transformation/main-transformer';
import { ContractTransformer } from '../transformation/nodes/contract.transformer';
import { EventTransformer } from '../transformation/nodes/event.transformer';
import { FunctionTransformer } from '../transformation/nodes/function.transformer';
import { ModifierTransformer } from '../transformation/nodes/modifier.transformer';
import { VariableTransformer } from '../transformation/nodes/variable.transformer';
import { Neo4jWriter } from './neo4j-writer';
import { TransformContext } from '../transformation/core/transformer';
import { CPGNode } from '../cpg';
import { ASTNode } from '../../ast/types';
import { CPGGraph } from '../cpg';

export interface CpgBuildResult {
  nodeCount: number;
  edgeCount: number;
  error?: Error;
}

export class CPGBuilder {
  private transformer: MainTransformer;
  private writer: Neo4jWriter;

  constructor(driver: Driver) {
    this.transformer = new MainTransformer();
    // Manually register transformers since constructor doesn't accept them
    ;(this.transformer as any).register([
      new ContractTransformer(),
      new FunctionTransformer(),
      new ModifierTransformer(),
      new VariableTransformer(),
      new EventTransformer()
    ]);
    this.writer = new Neo4jWriter(driver);
  }

  public async build(slitherOutput: SlitherOutput): Promise<CpgBuildResult> {
    const ast = (slitherOutput.results as any).ast;
    if (!slitherOutput.success || !ast) {
      throw new Error('Slither analysis failed or missing AST');
    }
    
    const startTime = Date.now();
    try {
      const graph: CPGGraph = {
        nodes: [],
        edges: [],
        addNode: (node) => {
          graph.nodes.push(node);
          return node;
        },
        addEdge: (edge) => {
          graph.edges.push(edge);
        },
        getNode: (id) => {
          return graph.nodes.find(n => n.id === id);
        },
        getNodeChildren: (id) => {
          return graph.edges
            .filter(e => e.source === id)
            .map(e => graph.nodes.find(n => n.id === e.target))
            .filter(Boolean) as any[];
        }
      };
      const context: TransformContext = {
        graph,
        transformChild: (node) => {
          const childNode: CPGNode = {
            id: `node-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
            type: node.nodeType,
            src: node.src || '0:0:0',
            attributes: {}
          };
          graph.addNode(childNode);
          return childNode;
        },
        generateId: () => `id-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
      };
      this.transformer.transform(ast, context);
      
      await this.writer.writeNodes(graph.nodes);
      await this.writer.writeRelationships(graph.edges);
      
      return {
        nodeCount: graph.nodes.length,
        edgeCount: graph.edges.length
      };
    } catch (error) {
      return {
        nodeCount: 0,
        edgeCount: 0,
        error: error instanceof Error ? error : new Error('Unknown error')
      };
    }
  }
}
