import { Driver } from 'neo4j-driver';

export enum NodeType {
  // Core AST nodes
  CONTRACT = 'Contract',
  FUNCTION = 'Function',
  VARIABLE = 'Variable',
  PARAMETER = 'Parameter',
  MODIFIER = 'Modifier',
  STRUCT = 'Struct',
  ERROR = 'Error',
  EVENT = 'Event',
  
  // Control flow
  BLOCK = 'Block',
  IF_STATEMENT = 'IfStatement',
  LOOP = 'Loop',
  
  // Expressions
  ASSIGNMENT = 'Assignment',
  BINARY_OPERATION = 'BinaryOperation',
  FUNCTION_CALL = 'FunctionCall',
  
  // Security-specific
  ACCESS_CONTROL = 'AccessControl',
  REENTRANCY = 'Reentrancy',
  ARITHMETIC = 'Arithmetic',
}

export enum RelationshipType {
  // Structural relationships
  CONTAINS = 'CONTAINS',
  HAS_FUNCTION = 'HAS_FUNCTION',
  HAS_VARIABLE = 'HAS_VARIABLE',
  HAS_PARAMETER = 'HAS_PARAMETER',
  
  // Control flow
  NEXT = 'NEXT',
  BRANCH = 'BRANCH',
  
  // Data flow
  READS = 'READS',
  WRITES = 'WRITES',
  
  // Security relationships
  POTENTIAL_VULNERABILITY = 'POTENTIAL_VULNERABILITY',
  DATA_DEPENDENCY = 'DATA_DEPENDENCY',
  CONTROL_DEPENDENCY = 'CONTROL_DEPENDENCY',
}

export interface CPGNode {
  id: string;
  type: NodeType;
  name: string;
  properties: Record<string, any>;
}

export class Neo4jSchema {
  static async createIndexes(driver: Driver) {
    const session = driver.session();
    try {
      await session.run(`
        CREATE INDEX contract_name_address IF NOT EXISTS 
        FOR (n:${NodeType.CONTRACT}) 
        ON (n.name, n.address)
      `);
      
      await session.run(`
        CREATE INDEX function_name_visibility IF NOT EXISTS 
        FOR (n:${NodeType.FUNCTION}) 
        ON (n.name, n.visibility)
      `);
      
      await session.run(`
        CREATE INDEX variable_name_type IF NOT EXISTS 
        FOR (n:${NodeType.VARIABLE}) 
        ON (n.name, n.type)
      `);
      
      // Security-specific indexes
      await session.run(`
        CREATE INDEX access_control_pattern IF NOT EXISTS 
        FOR (n:${NodeType.ACCESS_CONTROL}) 
        ON (n.pattern)
      `);
      
      await session.run(`
        CREATE INDEX reentrancy_pattern IF NOT EXISTS 
        FOR (n:${NodeType.REENTRANCY}) 
        ON (n.pattern)
      `);
    } finally {
      await session.close();
    }
  }

  static getNodeLabels(type: NodeType): string[] {
    // Maintain backward compatibility with generic CPGNode label
    return [type, 'CPGNode'];
  }
}
