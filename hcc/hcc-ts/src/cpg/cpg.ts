// CPG schema for Solidity support

export enum RelationshipType {
  CONTAINS = 'CONTAINS',
  REFERS_TO = 'REFERS_TO',
  REFERENCES_LOCAL_VAR = 'REFERENCES_LOCAL_VAR',
  DECLARES_GLOBAL_VAR = 'DECLARES_GLOBAL_VAR',
  CONTAINS_STATE_VAR = 'CONTAINS_STATE_VAR',
  CALLS = 'CALLS',
  INHERITS = 'INHERITS',
  INHERITS_FROM = 'INHERITS_FROM',
  IMPLEMENTS = 'IMPLEMENTS',
  HAS_VARIABLE = 'HAS_VARIABLE',
  HAS_PARAMETER = 'HAS_PARAMETER',
  HAS_RETURN = 'HAS_RETURN',
  HAS_MODIFIER = 'HAS_MODIFIER',
  HAS_EVENT = 'HAS_EVENT',
  HAS_ERROR = 'HAS_ERROR',
  HAS_STATEMENT = 'HAS_STATEMENT',
  HAS_EXPRESSION = 'HAS_EXPRESSION',
  HAS_TYPE = 'HAS_TYPE',
  HAS_BASE_TYPE = 'HAS_BASE_TYPE',
  HAS_MAPPING = 'HAS_MAPPING',
  HAS_ARRAY = 'HAS_ARRAY',
  HAS_STRUCT = 'HAS_STRUCT',
  HAS_ENUM = 'HAS_ENUM',
  HAS_FUNCTION = 'HAS_FUNCTION',
  HAS_CONSTRUCTOR = 'HAS_CONSTRUCTOR',
  HAS_FALLBACK = 'HAS_FALLBACK',
  HAS_RECEIVE = 'HAS_RECEIVE',
  HAS_CATCH = 'HAS_CATCH',
  PARENT_OF = 'PARENT_OF',
  NEXT = 'NEXT',
  BRANCH = 'BRANCH',
  READS = 'READS',
  WRITES = 'WRITES',
  POTENTIAL_VULNERABILITY = 'POTENTIAL_VULNERABILITY',
  DATA_DEPENDENCY = 'DATA_DEPENDENCY',
  CONTROL_DEPENDENCY = 'CONTROL_DEPENDENCY',
  TAINT_FLOW = 'TAINT_FLOW'
}

export type CPGNodeType = 
  | 'ContractDefinition'
  | 'FunctionDefinition'
  | 'ModifierDefinition'
  | 'EventDefinition'
  | 'StateVariableDeclaration'
  | string;

export interface CPGNode {
  id: string;
  type: CPGNodeType;
  src: string;
  attributes: Record<string, any>;
}

export type CPCEdgeType = RelationshipType;

export interface CPCEdge {
  source: string;
  target: string;
  type: RelationshipType;
  properties?: Record<string, any>;
}

export interface CPGGraph {
  nodes: CPGNode[];
  edges: CPCEdge[];
  addNode: (node: CPGNode) => CPGNode;
  addEdge: (edge: CPCEdge) => void;
  getNode: (id: string) => CPGNode | undefined;
  getNodeChildren: (id: string) => CPGNode[];
}
