import * as solc from 'solc';
import { ASTNode, SlitherOutput } from '../ast/types';
import { CPGNode, CPCEdge, CPGGraph } from './cpg';
import { RelationshipType } from './schema/neo4j-schema';
import { VariableTransformer } from './transformation/nodes/variable.transformer';

interface TransformContext {
  idGen: () => string;
  parent?: CPGNode;
  graph: CPGGraph;
  transformChild: (node: ASTNode) => CPGNode;
  generateId: () => string;
}

export interface ASTTransformResult {
  nodes: CPGNode[];
  edges: CPCEdge[];
}

export class ASTToCpgTransformer {
  private edges: CPCEdge[] = [];
  private nodes: CPGNode[] = [];

  private createEdge(
    source: CPGNode,
    target: CPGNode,
    type: RelationshipType,
    properties: Record<string, any> = {}
  ): CPCEdge {
    const edge: CPCEdge = {
      source: source.id,
      target: target.id,
      type,
      properties
    };
    this.edges.push(edge);
    return edge;
  }

  private processContract(contract: any): CPGNode | undefined {
    // TODO: Implement contract transformation
    return undefined;
  }

  private processFunction(func: any, parent: CPGNode): CPGNode | undefined {
    // TODO: Implement function transformation
    return undefined;
  }

  transform(slitherOutput: SlitherOutput): { nodes: CPGNode[]; edges: CPCEdge[] } {
    if (!slitherOutput.results?.ast) {
      throw new Error('No AST found in Slither output');
    }

    const graph: CPGGraph = {
      nodes: [],
      edges: [],
      addNode: (node) => {
        graph.nodes.push(node);
        return node;
      },
      addEdge: (edge) => {
        graph.edges.push(edge);
      },
      getNode: (id) => {
        return graph.nodes.find(n => n.id === id);
      },
      getNodeChildren: (id) => {
        return graph.edges
          .filter(e => e.source === id)
          .map(e => graph.nodes.find(n => n.id === e.target))
          .filter(Boolean) as CPGNode[];
      }
    };

    const context: TransformContext = {
      graph,
      idGen: this.createIdGenerator(),
      transformChild: (node) => {
        const childNode: CPGNode = {
          id: `node-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
          type: node.nodeType,
          src: node.src || '0:0:0',
          attributes: {}
        };
        graph.addNode(childNode);
        return childNode;
      },
      generateId: () => `id-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
    };

    this.transformNode(slitherOutput.results.ast, context);

    return {
      nodes: graph.nodes,
      edges: graph.edges
    };
  }

  private transformNode(node: ASTNode, context: TransformContext): CPGNode | undefined {
    if (!node.nodeType) return undefined;

    let cpgNode: CPGNode | undefined;
    
    // Handle different node types
    switch (node.nodeType) {
      case 'ContractDefinition':
        cpgNode = this.processContract(node);
        break;
      case 'FunctionDefinition':
        cpgNode = this.processFunction(node, context.parent!);
        break;
      case 'StateVariableDeclaration': {
        const transformer = new VariableTransformer();
        cpgNode = transformer.canTransform(node) ? transformer.transform(node, context) : undefined;
        break;
      }
      default:
        // Basic node for unhandled types
        cpgNode = {
          id: context.generateId(),
          type: node.nodeType as any,
          src: node.src,
          attributes: {}
        };
    }

    if (cpgNode) {
      context.graph.addNode(cpgNode);
      if (context.parent) {
        context.graph.addEdge(this.createEdge(cpgNode, context.parent, RelationshipType.CONTAINS));
      }
    }

    return cpgNode;
  }

  private handleSpecialCases(parent: ASTNode, child: CPGNode) {
    // Handle special relationship cases here
  }

  private createIdGenerator(): () => string {
    let counter = 0;
    return () => `node_${counter++}`;
  }
}

interface SolidityError {
  severity: string;
  formattedMessage: string;
}

interface SolidityOutput {
  errors?: SolidityError[];
  sources?: {
    [key: string]: {
      ast?: any;
    };
  };
  contracts?: {
    [key: string]: {
      [key: string]: {
        ast?: any;
      };
    };
  };
}

export async function getSolidityAST(source: string): Promise<any> {
  try {
    const input = {
      language: 'Solidity',
      sources: {
        'TestContract.sol': {
          content: source
        }
      },
      settings: {
        outputSelection: {
          'TestContract.sol': {
            '*': ['ast']
          }
        }
      }
    };

    const inputStr = JSON.stringify(input);
    const outputStr = solc.compile(inputStr);
    const output = JSON.parse(outputStr);
    
    if (output.errors) {
      const errors = output.errors.filter((e: any) => e.severity === 'error');
      if (errors.length > 0) {
        throw new Error(
          `Solidity compilation failed: ${errors
            .map((e: any) => e.formattedMessage)
            .join('\n')}`
        );
      }
    }

    const ast = output.sources?.['TestContract.sol']?.ast;
    if (!ast) {
      throw new Error('Could not find AST in Solidity compiler output');
    }

    return ast;
  } catch (error) {
    throw new Error(`Failed to compile Solidity: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export const astToCpg = (slitherOutput: SlitherOutput, testMode = false) => {
  if (testMode) {
    // Return test data structure
    return {
      nodes: [
        { id: 'n1', type: 'Contract' as any, src: '0:0:0', attributes: {} },
        { id: 'n2', type: 'Function' as any, src: '0:0:0', attributes: {} },
        { id: 'n3', type: 'Variable' as any, src: '0:0:0', attributes: {} }
      ],
      edges: [
        { source: 'n1', target: 'n2', type: RelationshipType.CONTAINS, properties: {} },
        { source: 'n1', target: 'n3', type: RelationshipType.CONTAINS, properties: {} }
      ]
    };
  }
  
  const transformer = new ASTToCpgTransformer();
  return transformer.transform(slitherOutput);
};
