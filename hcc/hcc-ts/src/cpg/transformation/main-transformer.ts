import { ASTNode } from '../../ast/types';
import { CPGNode } from '../../cpg';
import { TransformContext } from './core/transformer';
import { BaseTransformer } from './core/base-transformer';
import { ContractTransformer } from './nodes/contract.transformer';
import { FunctionTransformer } from './nodes/function.transformer';
import { VariableTransformer } from './nodes/variable.transformer';
import { ModifierTransformer } from './nodes/modifier.transformer';
import { ExpressionTransformer } from './nodes/expression.transformer';
import { EventTransformer } from './nodes/event.transformer';
import { StructTransformer } from './nodes/struct.transformer';
import { ErrorTransformer } from './nodes/error.transformer';
import { TryCatchTransformer } from './nodes/trycatch.transformer';
import { UserDefinedValueTypeTransformer } from './nodes/userdefinedvaluetype.transformer';
import { IfStatementTransformer } from './nodes/ifstatement.transformer';
import { ForStatementTransformer } from './nodes/forstatement.transformer';
import { WhileStatementTransformer } from './nodes/whilestatement.transformer';
import { DoWhileStatementTransformer } from './nodes/dowhilestatement.transformer';
import { UncheckedBlockTransformer } from './nodes/uncheckedblock.transformer';
import { YulTransformer } from './nodes/yul.transformer';

const errorTransformer = new ErrorTransformer();

export class MainTransformer extends BaseTransformer {
  private transformers: BaseTransformer[];

  constructor() {
    super();
    this.transformers = [
      new ContractTransformer(),
      new FunctionTransformer(),
      new VariableTransformer(),
      new ModifierTransformer(),
      new ExpressionTransformer(),
      new EventTransformer(),
      new StructTransformer(),
      new ErrorTransformer(),
      new UserDefinedValueTypeTransformer(),
      new IfStatementTransformer(),
      new ForStatementTransformer(),
      new WhileStatementTransformer(),
      new DoWhileStatementTransformer(),
      new TryCatchTransformer(),
      new UncheckedBlockTransformer(),
      new YulTransformer()
    ];
  }

  canTransform(node: ASTNode): boolean {
    return this.transformers.some(t => t.canTransform(node));
  }

  transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
    for (const transformer of this.transformers) {
      if (transformer.canTransform(node)) {
        return transformer.transform(node, context);
      }
    }
    if (errorTransformer.canTransform(node)) {
      return errorTransformer.transform(node, context);
    }
    return undefined;
  }
}
