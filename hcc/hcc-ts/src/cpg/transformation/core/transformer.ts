import { ASTNode } from '../../../ast/types';
import { CPGNode, CPCEdge, RelationshipType, CPGGraph } from '../../cpg';

export interface TransformContext {
  graph: CPGGraph;
  parent?: CPGNode;
  transformChild: (node: ASTNode) => CPGNode;
  generateId: () => string;
}

export interface ASTTransformer {
  canTransform(node: ASTNode): boolean;
  transform(node: ASTNode, context: TransformContext): CPGNode | undefined;
}

export abstract class ASTTransformerImpl implements ASTTransformer {
  protected nodeIdCounter = 0;
  
  abstract canTransform(node: ASTNode): boolean;

  protected generateId(node: ASTNode): string {
    return `${node.nodeType}_${node.src}`;
  }

  protected createEdge(
    source: CPGNode,
    target: CPGNode,
    type: RelationshipType,
    graph: CPGGraph
  ): void {
    const edge = { source: source.id, target: target.id, type };
    if (graph && typeof graph.addEdge === 'function') {
      graph.addEdge(edge);
    } else if (Array.isArray(graph.edges)) {
      graph.edges.push(edge);
    }
  }

  protected processChildren(node: ASTNode, context: TransformContext): void {
    if (!node.children) return;
    
    const childContext = {
      ...context,
      parent: this.transform(node, context) || undefined
    };
    
    for (const child of node.children) {
      this.transform(child, childContext);
    }
  }

  transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
    throw new Error('Method not implemented.');
  }
}
