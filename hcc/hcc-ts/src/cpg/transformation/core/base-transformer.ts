import { ASTNode } from '../../../ast/types';
import { CPGNode, CPGGraph, CPCEdge, RelationshipType } from '../../cpg';
import { TransformContext } from './transformer';

/**
 * Base transformer implementing core CPG node creation functionality.
 * 
 * ID Generation Strategy:
 * - Uses format: `${nodeType}_${srcStart}` where:
 *   - `nodeType`: The AST node type (e.g., 'FunctionDefinition')
 *   - `srcStart`: First part of Solidity src property (start position)
 * - Requires valid 3-part src format: `start:length:offset`
 * - Throws explicit errors for invalid/missing src
 * - Benefits:
 *   - Deterministic IDs (same input → same ID)
 *   - Traceable back to source code
 *   - No collisions between nodes
 */
export abstract class BaseTransformer {
  protected graph?: CPGGraph;

  constructor(graph?: CPGGraph) {
    this.graph = graph;
  }

  abstract canTransform(node: ASTNode): boolean;
  abstract transform(node: ASTNode, context: TransformContext): CPGNode | undefined;

  protected generateId(node: ASTNode): string {
    if (!node.src) throw new Error(`Missing src property for ${node.nodeType} node`);
    
    const parts = node.src.split(':');
    if (parts.length < 3 || !parts[0] || !parts[1] || !parts[2]) {
      throw new Error(`Invalid src format for ${node.nodeType} node: ${node.src}`);
    }
    
    return `${node.nodeType}_${parts[0]}`;
  }

  protected createCpgNode(
    node: ASTNode,
    data: Omit<CPGNode, 'id'>
  ): CPGNode {
    return {
      id: `${data.type}_${node.src}`,
      ...data
    };
  }

  protected createEdge(
    source: CPGNode,
    target: CPGNode,
    type: RelationshipType,
    properties: Record<string, any> = {}
  ): CPCEdge {
    const edge = { source: source.id, target: target.id, type, properties };
    if (this.graph) {
      this.graph.addEdge(edge);
    }
    return edge;
  }

  protected propagateTaint(source: CPGNode, target: CPGNode, context: TransformContext) {
    if (source.attributes.taintStatus === 'tainted') {
      target.attributes.taintStatus = 'tainted';
      target.attributes.taintSources = [...(target.attributes.taintSources || []), source.id];
      this.markAsTainted(target, context);
    }
  }

  protected markAsTainted(node: CPGNode, context: TransformContext) {
    node.attributes.taintStatus = 'tainted';
    if (context.parent) {
      this.createEdge(
        context.parent, 
        node, 
        RelationshipType.TAINT_FLOW, 
        context.graph
      );
    }
  }

  protected markAsSanitized(node: CPGNode) {
    node.attributes.taintStatus = 'sanitized';
  }
}
