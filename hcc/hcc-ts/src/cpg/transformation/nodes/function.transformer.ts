import { FunctionDefinition, ASTNode } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class FunctionTransformer extends BaseTransformer {
  canTransform(node: any): node is FunctionDefinition {
    return node.nodeType === 'FunctionDefinition';
  }

  transform(node: FunctionDefinition, context: TransformContext): CPGNode | undefined {
    const parameterCount = node.parameters?.parameters?.length || 0;
    const returnParamsCount = node.returnParameters?.parameters?.length || 0;
    
    const cpgNode: CPGNode = {
      id: this.generateId(node),
      type: 'FunctionDefinition',
      src: node.src,
      attributes: {
        name: node.name,
        parameters: parameterCount,
        returnParameters: returnParamsCount,
        visibility: node.visibility,
        stateMutability: node.stateMutability,
        virtual: node.virtual || false
      }
    };

    // Handle function modifiers
    if (node.modifiers) {
      for (const modifier of node.modifiers) {
        const modifierNode: ASTNode = {
          nodeType: 'ModifierInvocation',
          name: modifier.modifierName.name,
          src: modifier.src
        };
        const modifierId = this.generateId(modifierNode);
        this.createEdge(
          cpgNode,
          { id: modifierId } as CPGNode,
          RelationshipType.HAS_MODIFIER,
          {}
        );
      }
    }

    if (context.parent) {
      this.createEdge(context.parent, cpgNode, RelationshipType.CONTAINS, context.graph);
    }

    return cpgNode;
  }
}
