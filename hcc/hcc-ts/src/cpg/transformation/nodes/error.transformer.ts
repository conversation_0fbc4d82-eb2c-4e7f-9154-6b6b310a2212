import { ASTNode, ErrorDefinition, ParameterList, VariableDeclaration } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class ErrorTransformer extends BaseTransformer {
    canTransform(node: ASTNode): node is ErrorDefinition {
        return node.nodeType === 'ErrorDefinition';
    }

    transform(node: ErrorDefinition, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;

        const cpgNode = this.createCpgNode(node, {
            type: 'ErrorDefinition',
            src: node.src,
            attributes: {
                name: node.name,
                parameters: this.getParameterDetails(node.parameters)
            }
        });

        if (context.parent) {
            this.createEdge(
                context.parent,
                cpgNode,
                RelationshipType.CONTAINS,
                context.graph
            );
        }

        return cpgNode;
    }

    private getParameterDetails(parameters?: ParameterList): Array<{name: string, type: string}> {
        if (!parameters?.parameters) return [];
        
        return parameters.parameters.map((param: VariableDeclaration) => ({
            name: param.name || 'unnamed',
            type: param.typeName?.name || 'unknown'
        }));
    }
}
