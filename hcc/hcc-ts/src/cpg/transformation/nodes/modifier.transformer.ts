import { ModifierDefinition } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class ModifierTransformer extends BaseTransformer {
  canTransform(node: any): node is ModifierDefinition {
    return node.nodeType === 'ModifierDefinition';
  }

  transform(node: ModifierDefinition, context: TransformContext): CPGNode | undefined {
    // Handle undefined node case
    if (!node) return undefined;
    
    // Safely get parameter count
    let parameterCount = 0;
    if (node.parameters && 
        typeof node.parameters === 'object' && 
        'nodeType' in node.parameters && 
        node.parameters.nodeType === 'ParameterList') {
      parameterCount = Array.isArray(node.parameters.parameters) 
        ? node.parameters.parameters.length 
        : 0;
    }
    
    const cpgNode: CPGNode = {
      id: this.generateId(node),
      type: 'ModifierDefinition',
      src: node.src,
      attributes: {
        name: node.name || 'anonymous',
        parameters: parameterCount,
        virtual: node.virtual || false
      }
    };

    if (context.parent) {
      this.createEdge(context.parent, cpgNode, RelationshipType.CONTAINS, context.graph);
    }

    return cpgNode;
  }
}
