import { ASTNode } from '../../../ast/types';
import { TransformContext } from '../core/transformer';
import { CPGNode } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';

type StatementNode = ASTNode & { expression?: { operator?: string } };

export class UncheckedBlockTransformer extends BaseTransformer {
    canTransform(node: ASTNode): boolean {
        return node.nodeType === 'UncheckedBlock';
    }

    transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;
        
        const containsArithmetic = this.hasArithmeticOperations(node);
        const nestedUnchecked = this.countNestedUnchecked(node);
        
        return this.createCpgNode(node, {
            type: 'UncheckedBlock',
            src: node.src,
            attributes: {
                containsArithmetic,
                nestedUncheckedBlocks: nestedUnchecked
            }
        });
    }

    private hasArithmeticOperations(node: ASTNode): boolean {
        if (!('statements' in node)) return false;
        const statements = node.statements as StatementNode[];
        
        const arithmeticOps = ['+', '-', '*', '/', '%'];
        return statements.some((stmt: StatementNode) => 
            stmt.expression?.operator && 
            arithmeticOps.includes(stmt.expression.operator)
        );
    }

    private countNestedUnchecked(node: ASTNode): number {
        if (!('statements' in node)) return 0;
        const statements = node.statements as ASTNode[];
        
        return statements.reduce((count: number, stmt: ASTNode) => {
            if (stmt.nodeType === 'UncheckedBlock') {
                return count + 1 + this.countNestedUnchecked(stmt);
            }
            return count;
        }, 0);
    }
}
