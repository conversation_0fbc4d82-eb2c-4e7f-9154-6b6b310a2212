import { ASTNode, IfStatement } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class IfStatementTransformer extends BaseTransformer {
    canTransform(node: ASTNode): node is IfStatement {
        return node.nodeType === 'IfStatement';
    }

    transform(node: IfStatement, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;

        const cpgNode = this.createCpgNode(node, {
            type: 'IfStatement',
            src: node.src,
            attributes: {
                conditionSrc: node.condition.src
            }
        });

        // Transform condition
        if (node.condition) {
            const conditionNode = context.transformChild(node.condition);
            this.createEdge(conditionNode, cpgNode, RelationshipType.CONTAINS, context.graph);
        }
        
        // Transform true branch
        if (node.trueBody) {
            const trueBodyNode = context.transformChild(node.trueBody);
            this.createEdge(trueBodyNode, cpgNode, RelationshipType.CONTAINS, context.graph);
        }
        
        // Transform false branch if exists
        if (node.falseBody) {
            const falseBodyNode = context.transformChild(node.falseBody);
            this.createEdge(falseBodyNode, cpgNode, RelationshipType.CONTAINS, context.graph);
        }

        if (context.parent) {
            this.createEdge(
                context.parent,
                cpgNode,
                RelationshipType.CONTAINS,
                context.graph
            );
        }

        return cpgNode;
    }
}
