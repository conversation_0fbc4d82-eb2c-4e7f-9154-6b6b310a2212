import { ASTNode } from '../../../ast/types';
import { TransformContext } from '../core/transformer';
import { CPGNode } from '../../cpg';

export class ArrayTypeTransformer {
    transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;
        
        const typeString = node.typeDescriptions?.typeString || '';
        const sizeMatch = typeString.match(/\[(\d+)\]/);
        const isDynamic = !sizeMatch; // No fixed size
        const inMemory = typeString.includes(' memory');
        const inCalldata = typeString.includes(' calldata');
        
        return {
            id: `${node.nodeType}_${node.src}`,
            type: 'ArrayType',
            src: node.src,
            attributes: {
                arrayKind: inCalldata ? 'calldata' : inMemory ? 'memory' : 'storage',
                size: isDynamic ? undefined : parseInt(sizeMatch![1]),
                baseType: typeString.split('[')[0].trim()
            }
        } as CPGNode;
    }

    private canTransform(node: ASTNode): boolean {
        const typeString = node.typeDescriptions?.typeString;
        return typeString?.includes('[') && typeString?.includes(']'); 
    }
}
