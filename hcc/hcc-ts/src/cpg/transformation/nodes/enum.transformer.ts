import { ASTNode, EnumDefinition } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class EnumTransformer extends BaseTransformer {
  canTransform(node: ASTNode): node is EnumDefinition {
    return node.nodeType === 'EnumDefinition';
  }

  transform(node: EnumDefinition, context: TransformContext): CPGNode | undefined {
    if (!this.canTransform(node)) return undefined;

    const cpgNode = this.createCpgNode(node, {
      type: 'Enum',
      src: node.src,
      attributes: {
        name: node.name,
        members: node.members.map((member: {name: string}) => member.name)
      }
    });
    
    // Explicitly add node to graph
    context.graph.addNode(cpgNode);

    if (context.parent) {
      context.graph.addEdge({
        source: context.parent.id,
        target: cpgNode.id,
        type: RelationshipType.CONTAINS
      });
    }

    return cpgNode;
  }
}
