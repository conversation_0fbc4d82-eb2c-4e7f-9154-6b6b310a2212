import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';
import { CPGNode, RelationshipType, CPCEdge } from '../../cpg';

export class InheritanceTransformer extends BaseTransformer {
  canTransform(node: any): boolean {
    return node.nodeType === 'ContractDefinition';
  }

  transform(node: any, context: TransformContext): CPGNode | undefined {
    if (!this.canTransform(node)) {
      return undefined;
    }

    // Initialize graph if needed
    if (!context.graph.nodes) context.graph.nodes = [];
    if (!context.graph.edges) context.graph.edges = [];
    
    const nodeId = node.id.toString();
    
    // Create or get contract node
    let result = context.graph.nodes.find(n => n.id === nodeId);
    
    if (!result) {
      result = {
        id: nodeId,
        type: 'ContractDefinition',
        src: node.src,
        attributes: {
          name: node.name,
          abstract: node.abstract || false
        }
      };
      context.graph.nodes.push(result);
    }
    
    return result;
  }
  
  processInheritanceRelationships(ast: any, context: TransformContext): void {
    if (!ast?.nodes) return;
    
    
    // Initialize graph if needed
    if (!context.graph.edges) context.graph.edges = [];
    
    // Process each contract's inheritance
    ast.nodes.forEach((node: any) => {
      if (node.nodeType === 'ContractDefinition' && node.baseContracts?.length) {
        console.log(`Processing inheritance for contract: ${node.name}`);
        this.processContractInheritance(node, context, ast);
      }
    });
    
  }
  
  private processContractInheritance(
    node: any,
    context: TransformContext,
    ast: any,
    visited: Set<string> = new Set()
  ): void {
    const nodeId = node.id.toString();
    
    if (visited.has(nodeId)) {
      console.log(`Already processed contract: ${node.name} (${nodeId})`);
      return;
    }
    visited.add(nodeId);
    
    if (!node.baseContracts?.length) return;
    
    node.baseContracts.forEach((baseContract: any) => {
      // Get base name from baseName.namePath or baseName.name
      const baseName = baseContract.baseName.namePath || baseContract.baseName.name;
      
      if (!baseName) {
        console.warn(`Invalid base contract reference in ${node.name}:`, baseContract);
        return;
      }
      
      
      const baseNode = this.findContractDefinition(baseName, ast);
      if (!baseNode) {
        console.warn(`Base contract not found: ${baseName}`);
        return;
      }
      
      const baseNodeId = baseNode.id.toString();
      
      // Check if base contract node exists
      if (!context.graph.nodes.some(n => n.id === baseNodeId)) {
        console.warn(`Base contract node missing: ${baseName} (${baseNodeId})`);
        return;
      }
      
      // Check if edge already exists
      const edgeExists = context.graph.edges.some(e => 
        e.source === nodeId && 
        e.target === baseNodeId && 
        e.type === RelationshipType.INHERITS
      );
      
      if (edgeExists) {
        console.log(`Edge already exists: ${node.name} -> ${baseName}`);
        return;
      }
      
      // Create new edge
      const newEdge = {
        source: nodeId,
        target: baseNodeId,
        type: RelationshipType.INHERITS,
        properties: {
          src: baseContract.src
        }
      };
      
      context.graph.edges.push(newEdge);
      
      // Process the base contract's inheritance
      this.processContractInheritance(baseNode, context, ast, visited);
    });
  }
  
  private getSourceUnit(node: any): any {
    let current = node;
    while (current && current.nodeType !== 'SourceUnit') {
      current = current.parent;
    }
    return current;
  }
  
  private findContractDefinition(name: string, ast: any): any | undefined {
    if (!ast || ast.nodeType !== 'SourceUnit') {
      console.warn(`Invalid AST when looking for contract: ${name}`);
      return undefined;
    }
    
    const contract = ast.nodes?.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === name
    );
    
    if (!contract) {
      console.warn(`Contract not found in AST: ${name}`);
    }
    
    return contract;
  }
}
