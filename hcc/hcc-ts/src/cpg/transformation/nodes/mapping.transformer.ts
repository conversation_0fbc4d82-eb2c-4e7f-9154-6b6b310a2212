import { ASTNode, Mapping } from '../../../ast/types';
import { CPGNode, CPGGraph, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

type ValueType = {
  type: string;
  keyType?: string;
  valueType?: ValueType;
};

export class MappingTransformer extends BaseTransformer {
  canTransform(node: ASTNode): node is Mapping {
    return node.nodeType === 'Mapping';
  }

  private transformValueType(valueTypeNode: Mapping['valueType']): ValueType {
    if (this.isMapping(valueTypeNode)) {
      return {
        type: 'Mapping',
        keyType: valueTypeNode.keyType.name,
        valueType: this.transformValueType(valueTypeNode.valueType)
      };
    }
    
    // Handle ElementaryTypeName and other cases
    return {
      type: 'name' in valueTypeNode ? valueTypeNode.name : 'Unknown'
    };
  }

  private isMapping(node: any): node is Mapping {
    return node?.nodeType === 'Mapping';
  }

  transform(node: Mapping, context: TransformContext): CPGNode {
    if (!this.canTransform(node)) {
      throw new Error('Invalid node type for MappingTransformer');
    }

    const cpgNode = this.createCpgNode(node, {
      type: 'Mapping',
      src: node.src,
      attributes: {
        keyType: node.keyType.name,
        valueType: this.transformValueType(node.valueType)
      }
    });

    if (context.graph) {
      context.graph.addEdge({
        source: context.parent!.id,
        target: cpgNode.id,
        type: RelationshipType.CONTAINS,
        properties: {}
      });
    }

    return cpgNode;
  }
}
