import { BaseTransformer } from '../core/base-transformer';
import type { ASTNode } from '../../../ast/types';
import type { TransformContext } from '../core/transformer';
import type { CPGNode } from '../../cpg';

export class TernaryTransformer extends BaseTransformer {
    protected createCpgNode(astNode: ASTNode, properties: any): CPGNode {
        return {
            id: `${astNode.id}`,
            astNodeId: `${astNode.id}`,
            ...properties
        };
    }

    canTransform(node: ASTNode): boolean {
        return node.nodeType === 'Conditional';
    }

    transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) {
            return undefined;
        }

        const nestedCount = this.countNestedConditionals(node);
        const hasSideEffects = this.hasSideEffects(node);
        
        return this.createCpgNode(node, {
            type: 'TernaryExpression',
            src: node.src,
            attributes: {
                nestedConditionals: nestedCount,
                hasSideEffects
            }
        });
    }

    private countNestedConditionals(node: ASTNode): number {
        if (node.nodeType !== 'Conditional') return 0;
        
        let count = 0;
        
        // Count nested ternaries in true/false expressions
        const countInExpression = (expr: ASTNode | undefined): number => {
          if (!expr) return 0;
          
          // Direct nested ternary
          if (expr.nodeType === 'Conditional') {
            return 1 + this.countNestedConditionals(expr);
          }
          
          // Ternaries inside TupleExpressions (common in return statements)
          if (expr.nodeType === 'TupleExpression') {
            return (expr as any).components
              ?.filter((c: ASTNode) => c?.nodeType === 'Conditional')
              .reduce((sum: number, c: ASTNode) => sum + 1 + this.countNestedConditionals(c), 0) || 0;
          }
          
          return 0;
        };
        
        count += countInExpression(node.trueExpression);
        count += countInExpression(node.falseExpression);
        
        return count;
    }

    private hasSideEffects(node: ASTNode): boolean {
        return [node.trueExpression, node.falseExpression].some(
            expr => {
                if (!expr) return false;
                return [
                    'FunctionCall',
                    'Assignment',
                    'UnaryOperation',
                    'BinaryOperation'
                ].includes(expr.nodeType);
            }
        );
    }
}
