import { ASTNode } from '../../../ast/types';
import { TransformContext } from '../core/transformer';
import { CPGNode } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';

export class ExpressionTransformer extends BaseTransformer {
    canTransform(node: ASTNode): boolean {
        return [
            'BinaryOperation',
            'UnaryOperation',
            'FunctionCall',
            'Conditional'
        ].includes(node.nodeType);
    }

    transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;
        
        return this.createCpgNode(node, {
            type: node.nodeType,
            src: node.src,
            attributes: {}
        });
    }
}
