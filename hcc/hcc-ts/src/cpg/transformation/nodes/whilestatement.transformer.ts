import { ASTNode } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

interface WhileStatement extends ASTNode {
    nodeType: 'WhileStatement';
    condition: ASTNode;
    body: ASTNode;
}

export class WhileStatementTransformer extends BaseTransformer {
    canTransform(node: ASTNode): node is WhileStatement {
        return node.nodeType === 'WhileStatement';
    }

    transform(node: WhileStatement, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;

        const cpgNode = this.createCpgNode(node, {
            type: 'WhileStatement',
            src: node.src,
            attributes: {
                conditionSrc: node.condition.src
            }
        });

        // Transform condition and body
        if (node.condition) {
            const conditionNode = context.transformChild(node.condition);
            this.createEdge(conditionNode, cpgNode, RelationshipType.CONTAINS);
        }

        if (node.body) {
            const bodyNode = context.transformChild(node.body);
            this.createEdge(bodyNode, cpgNode, RelationshipType.CONTAINS);
        }

        // Create CONTAINS edge if parent exists
        if (context.parent) {
            this.createEdge(
                context.parent,
                cpgNode,
                RelationshipType.CONTAINS,
                context.graph
            );
        }

        return cpgNode;
    }
}
