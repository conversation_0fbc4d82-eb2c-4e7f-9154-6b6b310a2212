import { StructDefinition } from '../../../ast/types';
import { CPGNode } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class StructTransformer extends BaseTransformer {
  canTransform(node: any): node is StructDefinition {
    return node.nodeType === 'StructDefinition';
  }

  transform(node: StructDefinition, context: TransformContext): CPGNode | undefined {
    return {
      id: this.generateId(node),
      type: node.nodeType,
      src: node.src,
      attributes: {
        name: node.name,
        members: node.members?.length || 0
      }
    };
  }
}
