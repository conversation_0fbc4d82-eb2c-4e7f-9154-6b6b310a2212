import { ASTNode, UserDefinedValueTypeDefinition } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class UserDefinedValueTypeTransformer extends BaseTransformer {
    canTransform(node: ASTNode): node is UserDefinedValueTypeDefinition {
        return node.nodeType === 'UserDefinedValueTypeDefinition';
    }

    transform(node: UserDefinedValueTypeDefinition, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;

        const cpgNode = this.createCpgNode(node, {
            type: 'UserDefinedValueTypeDefinition',
            src: node.src,
            attributes: {
                name: node.name,
                underlyingType: node.underlyingType.name
            }
        });

        if (context.parent) {
            this.createEdge(
                context.parent,
                cpgNode,
                RelationshipType.CONTAINS,
                context.graph
            );
        }

        return cpgNode;
    }
}
