import { ASTNode } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class ConstructorTransformer extends BaseTransformer {
  canTransform(node: ASTNode): boolean {
    return node.nodeType === 'FunctionDefinition' && (node as any).kind === 'constructor';
  }

  transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
    if (!this.canTransform(node)) return undefined;
    
    const constructorNode = node as any;

    const cpgNode = this.createCpgNode(node, {
      type: 'Constructor',
      src: node.src,
      attributes: {
        stateMutability: constructorNode.stateMutability || 'nonpayable'
      }
    });

    if (context.parent) {
      this.createEdge(
        context.parent,
        cpgNode,
        RelationshipType.CONTAINS,
        context.graph
      );
    }

    return cpgNode;
  }
}
