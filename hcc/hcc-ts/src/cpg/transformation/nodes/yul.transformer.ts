import { ASTNode } from '../../../ast/types';
import { TransformContext } from '../core/transformer';
import { CPGNode } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';

type YulNode = ASTNode & {
    operations?: any[];
    AST?: {
        body?: {
            statements?: any[];
        };
    };
};

export class YulTransformer extends BaseTransformer {
    canTransform(node: ASTNode): boolean {
        return node.nodeType === 'InlineAssembly';
    }

    transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) {
            return undefined;
        }

        const yulNode = node as YulNode;
        
        const cpgNode = this.createCpgNode(node, {
          type: 'YulBlock',
          src: node.src,
          attributes: {}
        });

        const containsStorageAccess = this.detectStorageAccess(yulNode);
        
        return {
          ...cpgNode,
          attributes: {
            ...cpgNode.attributes,
            containsStorageAccess
          }
        };
    }

    private detectStorageAccess(node: any): boolean {
      if (!node) return false;
      
      // Check for direct storage operations
      if (node.nodeType === 'YulFunctionCall' && 
          (node.functionName?.name === 'sload' || node.functionName?.name === 'sstore')) {
        return true;
      }
      
      // Check for storage operations in YulAssignment
      if (node.nodeType === 'YulAssignment' && 
          node.value?.nodeType === 'YulFunctionCall' &&
          (node.value.functionName?.name === 'sload' || node.value.functionName?.name === 'sstore')) {
        return true;
      }
      
      // Recursively check children
      for (const key in node) {
        if (typeof node[key] === 'object' && node[key] !== null) {
          if (Array.isArray(node[key])) {
            if (node[key].some((item: any) => this.detectStorageAccess(item))) {
              return true;
            }
          } else if (this.detectStorageAccess(node[key])) {
            return true;
          }
        }
      }
      
      return false;
    }

    private hasStorageOperations(node: YulNode): boolean {
        // Check operations array if present
        if (node.operations?.some(op => 
            op.nodeType === 'YulFunctionCall' && 
            ['sstore', 'sload'].includes(op.functionName?.name)
        )) {
            return true;
        }
        
        // Check AST structure if present
        if (node.AST?.body?.statements) {
            return this.checkYulStatements(node.AST.body.statements);
        }
        
        return false;
    }

    private checkYulStatements(statements: any[]): boolean {
        return statements.some(stmt => {
            if (stmt.nodeType === 'YulFunctionCall' && stmt.functionName?.name) {
                return ['sstore', 'sload'].includes(stmt.functionName.name);
            }
            
            if (stmt.body?.statements) {
                return this.checkYulStatements(stmt.body.statements);
            }
            
            return false;
        });
    }
}
