import { ASTNode, StateVariableDeclaration, VariableDeclaration } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class VariableTransformer extends BaseTransformer {
  canTransform(node: ASTNode): node is StateVariableDeclaration | VariableDeclaration {
    return node.nodeType === 'StateVariableDeclaration' || node.nodeType === 'VariableDeclaration';
  }

  transform(node: StateVariableDeclaration | VariableDeclaration, context: TransformContext): CPGNode | undefined {
    if (!this.canTransform(node)) return undefined;

    // Enhanced scope detection with descriptive names
    let scope = 'local_variable';
    if (node.nodeType === 'StateVariableDeclaration') {
      scope = 'state_variable';
    } else if (!context.parent || context.parent.type !== 'FunctionDefinition') {
      scope = 'global_variable';
    }
    
    const cpgNode = this.createCpgNode(node, {
      type: 'Variable',
      src: node.src,
      attributes: {
        name: node.name,
        type: node.typeName?.name || 'unknown',
        constant: node.constant || false,
        visibility: node.visibility || 'default',
        storageLocation: (node as VariableDeclaration).storageLocation,
        scope,
        isStateVariable: node.nodeType === 'StateVariableDeclaration',
        // Enhanced taint analysis attributes
        taintStatus: 'untainted', // 'untainted' | 'tainted' | 'sanitized'
        taintSources: [],
        lastModifiedAt: null,
        lastReadAt: null,
        potentialVulnerabilities: []
      }
    });

    if (context.parent) {
      const edgeType = scope === 'state_variable' 
        ? RelationshipType.CONTAINS_STATE_VAR 
        : scope === 'global_variable'
          ? RelationshipType.DECLARES_GLOBAL_VAR
          : RelationshipType.REFERENCES_LOCAL_VAR;
      
      this.createEdge(
        context.parent,
        cpgNode,
        edgeType,
        context.graph
      );
    }

    return cpgNode;
  }
}
