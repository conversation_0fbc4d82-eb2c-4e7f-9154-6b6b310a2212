import { UsingForDirective } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class UsingForTransformer extends BaseTransformer {
  canTransform(node: any): boolean {
    return node.nodeType === 'UsingForDirective';
  }

  transform(node: UsingForDirective, context: TransformContext): CPGNode | undefined {
    const cpgNode = this.createCpgNode(node, {
      type: 'UsingForDirective',
      src: node.src,
      attributes: {
        libraryName: node.libraryName.name,
        typeName: node.typeName.name
      }
    });

    if (context.parent) {
      this.createEdge(context.parent, cpgNode, RelationshipType.CONTAINS, context.graph);
      
      // Create edge to the library being used
      const libraryNode: CPGNode = {
        id: `lib_${node.libraryName.name}`,
        type: 'LibraryDefinition',
        src: node.src,
        attributes: {
          name: node.libraryName.name
        }
      };
      this.createEdge(cpgNode, libraryNode, RelationshipType.REFERS_TO, context.graph);
    }

    return cpgNode;
  }
}
