import { EventDefinition } from '../../../ast/types';
import { CPGNode } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class EventTransformer extends BaseTransformer {
  canTransform(node: any): node is EventDefinition {
    return node.nodeType === 'EventDefinition';
  }

  transform(node: EventDefinition, context: TransformContext): CPGNode | undefined {
    // Event parameters are in node.parameters.parameters array
    const parameters = node.parameters as {parameters?: any[]};
    const parameterCount = parameters?.parameters?.length || 0;
    
    return {
      id: this.generateId(node),
      type: 'EventDefinition',
      src: node.src,
      attributes: {
        name: node.name,
        parameters: parameterCount,
        anonymous: node.anonymous || false
      }
    };
  }
}
