import { ASTNode } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

interface ForStatement extends ASTNode {
    nodeType: 'ForStatement';
    initializationExpression?: ASTNode;
    condition?: ASTNode;
    loopExpression?: ASTNode;
    body: ASTNode;
}

export class ForStatementTransformer extends BaseTransformer {
    canTransform(node: ASTNode): node is ForStatement {
        return node.nodeType === 'ForStatement';
    }

    transform(node: ForStatement, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;

        const cpgNode = this.createCpgNode(node, {
            type: 'ForStatement',
            src: node.src,
            attributes: {}
        });

        // Transform initialization if exists
        if (node.initializationExpression) {
            const initNode = context.transformChild(node.initializationExpression);
            this.createEdge(initNode, cpgNode, RelationshipType.CONTAINS, context.graph);
        }
        
        // Transform condition if exists
        if (node.condition) {
            const conditionNode = context.transformChild(node.condition);
            this.createEdge(conditionNode, cpgNode, RelationshipType.CONTAINS, context.graph);
        }
        
        // Transform loop expression if exists
        if (node.loopExpression) {
            const loopNode = context.transformChild(node.loopExpression);
            this.createEdge(loopNode, cpgNode, RelationshipType.CONTAINS, context.graph);
        }
        
        // Transform body
        if (node.body) {
            const bodyNode = context.transformChild(node.body);
            this.createEdge(bodyNode, cpgNode, RelationshipType.CONTAINS, context.graph);
        }

        if (context.parent) {
            this.createEdge(
                context.parent,
                cpgNode,
                RelationshipType.CONTAINS,
                context.graph
            );
        }

        return cpgNode;
    }
}
