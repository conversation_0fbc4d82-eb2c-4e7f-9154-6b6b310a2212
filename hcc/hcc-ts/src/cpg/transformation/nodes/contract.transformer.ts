import { ContractDefinition } from '../../../ast/types';
import { CPGNode } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class ContractTransformer extends BaseTransformer {
  canTransform(node: any): node is ContractDefinition {
    return node.nodeType === 'ContractDefinition';
  }

  transform(node: ContractDefinition, context: TransformContext): CPGNode | undefined {
    return {
      id: this.generateId(node),
      type: node.nodeType,
      src: node.src,
      attributes: {
        name: node.name,
        abstract: node.abstract,
        contractKind: node.contractKind
      }
    };
  }
}
