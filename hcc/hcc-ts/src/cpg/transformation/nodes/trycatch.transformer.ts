import { ASTNode, TryStatement, CatchClause } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

export class TryCatchTransformer extends BaseTransformer {
    canTransform(node: ASTNode): boolean {
        return node && (node.nodeType === 'TryStatement' || 
                       node.nodeType === 'CatchClause' ||
                       node.nodeType === 'TryCatchClause');
    }

    transform(node: ASTNode, context: TransformContext): CPGNode | undefined {
        if (!node || !this.canTransform(node)) return undefined;

        if (node.nodeType === 'TryStatement') {
            return this.transformTryStatement(node as TryStatement, context);
        } else {
            return this.transformCatchClause(node, context);
        }
    }

    private transformTryStatement(node: TryStatement, context: TransformContext): CPGNode {
        const cpgNode = this.createCpgNode(node, {
            type: 'TryStatement',
            src: node.src,
            attributes: {}
        });
        context.graph.addNode(cpgNode);

        // Process try block
        if (node.body) {
            const bodyNode = context.transformChild(node.body);
            if (bodyNode) {
                context.graph.addEdge({
                    source: bodyNode.id,
                    target: cpgNode.id,
                    type: RelationshipType.CONTAINS
                });
            }
        }

        // Process catch blocks
        if (node.clauses) {
            for (const catchClause of node.clauses) {
                if (catchClause.nodeType === 'TryCatchClause') {
                    const catchNode = this.transformCatchClause(catchClause, context);
                    
                    // Create HAS_CATCH edge from try to catch
                    context.graph.addEdge({
                        source: cpgNode.id,
                        target: catchNode.id,
                        type: RelationshipType.HAS_CATCH
                    });
                    
                    // Create CONTAINS edge from catch to try
                    context.graph.addEdge({
                        source: catchNode.id,
                        target: cpgNode.id,
                        type: RelationshipType.CONTAINS
                    });
                }
            }
        }

        return cpgNode;
    }

    private transformCatchClause(node: any, context: TransformContext): CPGNode {
        const catchNode = this.createCpgNode(node, {
            type: 'CatchClause',
            src: node.src,
            attributes: {}
        });
        context.graph.addNode(catchNode);
        
        // Handle parameters
        if (node.parameters) {
            for (const param of node.parameters.parameters || []) {
                const paramNode = context.transformChild(param);
                if (paramNode) {
                    context.graph.addEdge({
                        source: paramNode.id,
                        target: catchNode.id,
                        type: RelationshipType.CONTAINS
                    });
                }
            }
        }
        
        if (node.block) {
            const blockNode = context.transformChild(node.block);
            if (blockNode) {
                context.graph.addEdge({
                    source: blockNode.id,
                    target: catchNode.id,
                    type: RelationshipType.CONTAINS
                });
            }
        }

        return catchNode;
    }
}
