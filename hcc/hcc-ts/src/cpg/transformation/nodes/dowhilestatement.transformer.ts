import { ASTNode } from '../../../ast/types';
import { CPGNode, RelationshipType } from '../../cpg';
import { BaseTransformer } from '../core/base-transformer';
import { TransformContext } from '../core/transformer';

interface DoWhileStatement extends ASTNode {
    nodeType: 'DoWhileStatement';
    condition: ASTNode;
    body: ASTNode;
}

export class DoWhileStatementTransformer extends BaseTransformer {
    canTransform(node: ASTNode): node is DoWhileStatement {
        return node.nodeType === 'DoWhileStatement';
    }

    transform(node: DoWhileStatement, context: TransformContext): CPGNode | undefined {
        if (!this.canTransform(node)) return undefined;

        const cpgNode = this.createCpgNode(node, {
            type: 'DoWhileStatement',
            src: node.src,
            attributes: {
                conditionSrc: node.condition.src
            }
        });

        // Transform body first (since do-while executes body before condition)
        if (node.body) {
            const bodyNode = context.transformChild(node.body);
            this.createEdge(bodyNode, cpgNode, RelationshipType.CONTAINS);
        }

        if (node.condition) {
            const conditionNode = context.transformChild(node.condition);
            this.createEdge(conditionNode, cpgNode, RelationshipType.CONTAINS);
        }

        // Create CONTAINS edge if parent exists
        if (context.parent) {
            this.createEdge(
                context.parent,
                cpgNode,
                RelationshipType.CONTAINS,
                context.graph
            );
        }

        return cpgNode;
    }
}
