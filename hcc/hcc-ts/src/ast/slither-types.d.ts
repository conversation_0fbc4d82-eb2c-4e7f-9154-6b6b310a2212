export interface SlitherResult {
  detectors: {
    check: string;
    impact: string;
    confidence: string;
    description: string;
    elements: {
      type: string;
      name?: string;
      source_mapping: {
        filename_relative: string;
        lines: number[];
      };
    }[];
  }[];
}

export interface SlitherOutput {
  success: boolean;
  error: string | null;
  results: SlitherResult;
}
