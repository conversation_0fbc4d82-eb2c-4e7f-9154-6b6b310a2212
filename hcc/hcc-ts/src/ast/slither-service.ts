import { spawnSync } from 'child_process';
import { existsSync, readFileSync, unlinkSync } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';

export interface SlitherResult {
  success: boolean;
  results?: {
    success: boolean;
    error?: string;
    results: SlitherOutput;
  };
  error?: string;
}

export interface SlitherOutput {
  success: boolean;
  ast: any;
  detectors: any[];
  taintAnalysis?: any;
  error?: string;
}

interface Contract {
  name: string;
  // ... contract fields
}

export class SlitherService {
  static analyze(path: string): SlitherResult {
    const tempOutputPath = join(tmpdir(), `slither-output-${Date.now()}.json`);
    
    try {
      const result = spawnSync('slither', [
        path,
        '--json',
        tempOutputPath
      ], {
        encoding: 'utf-8',
        stdio: ['ignore', 'pipe', 'pipe']
      });
      
      if (!existsSync(tempOutputPath)) {
        return {
          success: false,
          error: result.stderr || 'Slither failed to produce output'
        };
      }
      
      const output = JSON.parse(readFileSync(tempOutputPath, 'utf-8'));
      return { 
        success: true, 
        results: output 
      };
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown analysis error'
      };
    } finally {
      if (existsSync(tempOutputPath)) {
        unlinkSync(tempOutputPath);
      }
    }
  }

  static getVersion(): string {
    try {
      const result = spawnSync('slither', ['--version'], { encoding: 'utf-8' });
      return result.stdout?.trim() || 'Unknown version';
    } catch {
      return 'Unknown version';
    }
  }

  static async storeInCPG(results: any, dbClient: any): Promise<void> {
    throw new Error('CPG storage not implemented yet');
  }
}
