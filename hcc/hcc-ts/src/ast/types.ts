export interface ASTNode {
  nodeType: string;
  id?: number;
  src: string;
  [key: string]: any;
}

export interface EventDefinition extends ASTNode {
  nodeType: 'EventDefinition';
  name: string;
  parameters: ASTNode[];
  anonymous: boolean;
}

export interface ContractDefinition extends ASTNode {
  nodeType: 'ContractDefinition';
  name: string;
  contractKind: 'contract' | 'interface' | 'library';
  abstract: boolean;
  fullyImplemented: boolean;
  baseContracts: Array<{
    baseName: {
      namePath: string;
    };
  }>;
}

export interface FunctionDefinition extends ASTNode {
  nodeType: 'FunctionDefinition';
  name: string;
  parameters: ParameterList | undefined;
  returnParameters: ParameterList | undefined;
  visibility: 'public' | 'private' | 'internal' | 'external';
  stateMutability: 'pure' | 'view' | 'payable' | 'nonpayable';
  virtual?: boolean;
  modifiers?: Array<{
    modifierName: {
      name: string;
    };
    src: string;
  }>;
}

export interface ModifierDefinition extends ASTNode {
  nodeType: 'ModifierDefinition';
  name: string;
  parameters: ParameterList | undefined;
  virtual: boolean;
  src: string;
}

export interface StateVariableDeclaration extends ASTNode {
  nodeType: 'StateVariableDeclaration';
  name: string;
}

export interface StructDefinition extends ASTNode {
  nodeType: 'StructDefinition';
  name: string;
  members: Array<{
    name: string;
    type: string;
    src: string;
  }>;
}

export interface ErrorDefinition extends ASTNode {
  nodeType: 'ErrorDefinition';
  name: string;
  parameters?: ParameterList;
}

export interface EnumDefinition extends ASTNode {
  nodeType: 'EnumDefinition';
  name: string;
  members: Array<{
    name: string;
    src: string;
  }>;
}

export interface VariableDeclaration extends ASTNode {
  nodeType: 'VariableDeclaration';
  name: string;
  typeName?: { name: string };
  visibility?: 'public' | 'private' | 'internal' | 'external';
  constant?: boolean;
  storageLocation?: 'memory' | 'storage' | 'calldata';
}

export interface Mapping extends ASTNode {
  nodeType: 'Mapping';
  keyType: { name: string };
  valueType: { name: string };
  src: string;
}

export interface Parameter extends ASTNode {
  nodeType: 'Parameter';
  name: string;
  typeName: {
    name: string;
  };
  storageLocation?: 'memory' | 'storage' | 'calldata';
}

export interface ParameterList extends ASTNode {
  nodeType: 'ParameterList';
  parameters: VariableDeclaration[];
}

export interface Block extends ASTNode {
  nodeType: 'Block';
  statements: Statement[];
}

export interface ConstructorDefinition extends Omit<ASTNode, 'parameters'> {
  nodeType: 'ConstructorDefinition';
  parameters?: ParameterList;
  stateMutability?: 'payable' | 'nonpayable';
}

export interface UsingForDirective extends ASTNode {
  nodeType: 'UsingForDirective';
  libraryName: {
    name: string;
  };
  typeName: {
    name: string;
  };
  src: string;
}

export interface TryStatement extends ASTNode {
  nodeType: 'TryStatement';
  body: Block;
  catches: CatchClause[];
}

export interface CatchClause extends ASTNode {
  nodeType: 'CatchClause';
  block: Block;
  kind?: string; // 'Error' for catch Error(...)
  parameters?: ParameterList;
}

export interface ExpressionStatement extends ASTNode {
  nodeType: 'ExpressionStatement';
  expression: Expression;
}

export interface VariableDeclarationStatement extends ASTNode {
  nodeType: 'VariableDeclarationStatement';
  declarations: VariableDeclaration[];
}

export interface IfStatement extends ASTNode {
  nodeType: 'IfStatement';
  condition: Expression;
  trueBody: Statement;
  falseBody?: Statement;
}

export type Expression = ASTNode & {
  nodeType: string;
  typeDescriptions: {
    typeString: string;
    typeIdentifier: string;
  };
};

export interface FunctionCall extends Expression {
  nodeType: 'FunctionCall';
  expression: Expression;
  arguments: Expression[];
}

export interface Identifier extends Expression {
  nodeType: 'Identifier';
  name: string;
}

export interface Literal extends Expression {
  nodeType: 'Literal';
  value: string | number | boolean;
}

export interface MemberAccess extends Expression {
  nodeType: 'MemberAccess';
  expression: Expression;
  memberName: string;
}

export interface BinaryOperation extends Expression {
  nodeType: 'BinaryOperation';
  left: Expression;
  right: Expression;
  operator: string;
}

export type Statement = 
  | ExpressionStatement 
  | VariableDeclarationStatement 
  | Block 
  | IfStatement 
  | TryStatement;

export interface UserDefinedValueTypeDefinition extends ASTNode {
  nodeType: 'UserDefinedValueTypeDefinition';
  name: string;
  underlyingType: {
    name: string;
    nodeType: string;
    src: string;
  };
}

export interface SlitherOutput {
  success: boolean;
  results: {
    ast: ASTNode;
    detectors?: Array<{
      check: string;
      impact: string;
      confidence: string;
    }>;
  };
}
