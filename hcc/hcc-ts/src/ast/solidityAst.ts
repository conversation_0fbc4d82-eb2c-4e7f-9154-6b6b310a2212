import solc from "solc"
import fs from "fs"
import path from "path"
import logger from "../logger/logger"

/**
 * Parse Solidity file and return AST using solc.
 * @param filePath Path to the Solidity file
 */
export function parseSolidityToAst(filePath: string): any {
  logger.info(`Parsing Solidity file: ${filePath}`)
  const sourceCode = fs.readFileSync(filePath, "utf8")
  const input = {
    language: "Solidity",
    sources: {
      [path.basename(filePath)]: {
        content: sourceCode,
      },
    },
    settings: {
      outputSelection: {
        "*": {
          "*": ["*"],
          "": ["ast"],
        },
      },
    },
  }

  const output = JSON.parse(solc.compile(JSON.stringify(input)))
  if (output.errors) {
    output.errors.forEach((err: any) => {
      logger.warn(err.formattedMessage)
    })
  }
  
  logger.debug('Compiler output:', JSON.stringify(output, null, 2));
  
  const ast = output.sources[path.basename(filePath)]?.ast;
  if (!ast) {
    throw new Error(`No AST generated - compiler output: ${JSON.stringify(output, null, 2)}`);
  }
  
  logger.info("AST successfully generated")
  return ast
}
