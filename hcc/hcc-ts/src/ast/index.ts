import { SlitherService, SlitherResult, SlitherOutput } from './slither-service';
import { parseSolidityToAst } from './solidityAst';
import logger from '../logger/logger';

export interface ASTResult {
ast: any;
  detectors?: any[];
  taintAnalysis?: any;
  errors?: string[];
}

export async function parseSolidity(filePath: string): Promise<ASTResult> {
  try {
    const result = await SlitherService.analyze(filePath);
    if (result.success && result.results?.results) {
      const slitherOutput = result.results.results;
      return {
        ast: slitherOutput.ast,
        detectors: slitherOutput.detectors,
        taintAnalysis: slitherOutput.taintAnalysis
      };
    }
    throw new Error(result.error || 'Slither analysis failed');
  } catch (error) {
    logger.warn(`Falling back to solc: ${error instanceof Error ? error.message : String(error)}`);
    return {
      ast: parseSolidityToAst(filePath),
      errors: [error instanceof Error ? error.message : String(error)]
    };
  }
}

export * from './slither-service';
export * from './solidityAst';
