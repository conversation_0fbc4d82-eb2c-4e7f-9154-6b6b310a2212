// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

// hcc-ts/src/__tests__/fixtures/flattening/ImportedTestContract.sol

struct TestStruct {
    uint256 count;
    string label;
}

// hcc-ts/src/__tests__/fixtures/flattening/TestContract.sol

contract TestContract {
    TestStruct public data;
    
    function update(uint256 _count, string calldata _label) external {
        data = TestStruct(_count, _label);
    }
}

// hcc-ts/src/__tests__/fixtures/flattening/Wrapper.sol

