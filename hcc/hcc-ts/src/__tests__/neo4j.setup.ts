import { Neo4jClient, Neo4jConfig } from '../db/neo4j';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.test' });

export const neo4jConfig: Neo4jConfig = {
  uri: process.env.NEO4J_URI || 'bolt://localhost:7687',
  user: process.env.NEO4J_USER || 'neo4j',
  password: process.env.NEO4J_PASSWORD || 'new_secure_password',
  trust: 'TRUST_ALL_CERTIFICATES' as const
};

export async function setupNeo4j(client: any) {
  await client.query('MATCH (n) DETACH DELETE n');
}

export function getTestClient(): Neo4jClient {
  const client = new Neo4jClient(neo4jConfig);
  return client;
}

describe('Neo4j Setup', () => {
  let client: Neo4jClient;

  beforeAll(() => {
    client = getTestClient();
  });

  afterAll(async () => {
    try {
      // First try normal disconnect
      await client.disconnect();
      
      // Additional cleanup as fallback
      const driver = (client as any).driver;
      if (driver) {
        // Close all sessions
        if (driver._sessionPool) {
          await driver._sessionPool.close().catch(() => {});
        }
        
        // Close driver if still open
        if (!driver._closed) {
          await driver.close().catch(() => {});
        }
      }
    } catch (err) {
      console.error('Error during Neo4j cleanup:', err);
    } finally {
      jest.clearAllTimers();
      jest.restoreAllMocks();
      
      if (global.gc) {
        global.gc();
      }
    }
  });

  it('should establish connection', async () => {
    await client.verifyConnection();
    expect(client.isConnected()).toBe(true);
  });
});
