import { SlitherService } from '../ast/slither-service';
import { existsSync } from 'fs';
import { join } from 'path';
import { execSync, exec } from 'child_process';

describe('Slither Service', () => {
  let slitherAvailable = false;
  let slitherProcess: any;
  
  beforeAll(() => {
    try {
      execSync('slither --version');
      slitherAvailable = true;
    } catch {
      console.warn('Slither not available, skipping tests');
    }
  });

  afterAll(() => {
    if (slitherProcess) {
      slitherProcess.kill();
    }
    jest.clearAllTimers();
  });

  it('should return analysis results for valid contract', () => {
    if (!slitherAvailable) return;
    
    const testContractPath = join(__dirname, 'testdata', 'TestContract.sol');
    if (!existsSync(testContractPath)) {
      console.warn('Test contract not found, skipping test');
      return;
    }

    const result = SlitherService.analyze(testContractPath);
    
    if (!result.success) {
      console.warn(`Slither analysis failed: ${result.error}`);
    }
    
    expect(result.success).toBe(true);
    expect(result.results).toBeDefined();
  });
});
