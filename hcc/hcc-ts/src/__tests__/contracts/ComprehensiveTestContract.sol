// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract ComprehensiveTestContract {
    // State variables
    uint256 public simpleVar;
    string private textVar = "test";
    address internal owner;
    
    // Custom error
    error Unauthorized(address caller);
    
    // Struct
    struct User {
        address addr;
        uint256 balance;
    }
    
    // Mapping
    mapping(address => User) public users;
    
    // Modifier
    modifier onlyOwner() {
        if (msg.sender != owner) revert Unauthorized(msg.sender);
        _;
    }
    
    constructor() {
        owner = msg.sender;
    }
    
    // Functions with different features
    function setVars(uint256 _val, string memory _text) external onlyOwner {
        simpleVar = _val;
        textVar = _text;
    }
    
    function conditionalFunc(bool condition) public {
        if (condition) {
            simpleVar = 1;
        } else {
            simpleVar = 0;
        }
    }
    
    function createUser(uint256 _balance) external {
        users[msg.sender] = User({
            addr: msg.sender,
            balance: _balance
        });
    }
}
