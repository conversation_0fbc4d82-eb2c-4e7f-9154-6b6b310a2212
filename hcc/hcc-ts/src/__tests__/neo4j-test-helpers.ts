import { Neo4jConfig } from '../db/neo4j';
import type { RelationshipType } from '../cpg';
import type { CPGGraph, CPGNode, CPCEdge } from '../cpg';

export interface TestNeo4jConfig extends Neo4jConfig {
  testRunId: string;
}

export const getTestNeo4jConfig = (): TestNeo4jConfig => {
  const uri = process.env.NEO4J_URI || 'bolt://localhost:7687';
  const user = process.env.NEO4J_USER || 'neo4j';
  const password = process.env.NEO4J_PASSWORD || 'new_secure_password';
  
  const testRunId = `test_${Date.now()}`;
  
  return { uri, user, password, testRunId };
};

export const createTestCPG = (testRunId: string): CPGGraph => {
  const contractId = `${testRunId}_contract`;
  const functionId = `${testRunId}_function`;
  
  const nodes: CPGNode[] = [
    {
      id: contractId,
      type: 'Contract',
      src: '0:0:0',
      attributes: {
        name: 'TestContract',
        testData: true,
        testRunId,
        address: '0x123'
      }
    },
    {
      id: functionId,
      type: 'Function',
      src: '0:0:0',
      attributes: {
        name: 'testFunction',
        testData: true,
        testRunId,
        signature: 'function()'
      }
    }
  ];
  const edges = [
    createTestEdge(contractId, functionId, 'CONTAINS' as RelationshipType, testRunId)
  ];

  return {
    nodes,
    edges,
    addNode: (node: CPGNode) => {
      nodes.push(node);
      return node;
    },
    addEdge: (edge: CPCEdge) => {
      edges.push(edge);
    },
    getNode: (id: string) => {
      return nodes.find(n => n.id === id);
    },
    getNodeChildren: (id: string) => {
      return edges
        .filter(e => e.source === id)
        .map(e => nodes.find(n => n.id === e.target))
        .filter(Boolean) as CPGNode[];
    }
  };
};

export function createTestEdge(source: string, target: string, type: RelationshipType, testRunId?: string): CPCEdge {
  return {
    source,
    target,
    type,
    properties: {
      testData: true,
      testRunId: testRunId || ''
    }
  };
}
