import { DoWhileStatementTransformer } from '../../../../src/cpg/transformation/nodes/dowhilestatement.transformer';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import { mockTransformContext } from '../test-utils';
import path from 'path';

describe('DoWhileStatementTransformer', () => {
  let transformer: DoWhileStatementTransformer;
  let context: any;
  let doWhileNode: any;

  beforeAll(() => {
    try {
      const contractPath = path.join(__dirname, '../../testdata/ControlFlowTestContract.sol');
      const ast = parseSolidityToAst(contractPath);
      
      if (!ast?.nodes) {
        throw new Error('AST nodes not found');
      }
      
      const contractDef = ast.nodes.find(
        (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'ControlFlowTest'
      );
      
      if (!contractDef) {
        throw new Error(`Contract ControlFlowTest not found in AST nodes`);
      }
      
      if (!contractDef.nodes) {
        throw new Error(`Contract definition has no nodes property`);
      }
      
      // Find the function containing do-while loops
      const functionDef = contractDef.nodes.find(
        (n: any) => n.nodeType === 'FunctionDefinition' && n.name === 'nestedDoWhileLoops'
      );
      
      if (!functionDef) {
        throw new Error(`Function nestedDoWhileLoops not found in contract nodes`);
      }
      
      // Get the do-while statement
      doWhileNode = functionDef.body?.statements?.find(
        (s: any) => s.nodeType === 'DoWhileStatement'
      );
    } catch (err) {
      console.error('Failed to setup test:', err);
      throw err; // Re-throw to fail the test
    }
  });

  beforeEach(() => {
    transformer = new DoWhileStatementTransformer();
    context = mockTransformContext();
  });

  it('should transform do-while statement from contract', () => {
    if (!doWhileNode) {
      console.warn('Skipping test - no do-while node found');
      return;
    }
    
    const result = transformer.transform(doWhileNode, context);
    expect(result).toBeDefined();
    expect(result?.type).toBe('DoWhileStatement');
    expect(result?.src).toBe(doWhileNode.src);
  });
});
