import { ForStatementTransformer } from '../../../../src/cpg/transformation/nodes/forstatement.transformer';
import { mockTransformContext } from '../test-utils';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import path from 'path';
import type { ASTNode, ContractDefinition, FunctionDefinition } from '../../../../src/ast/types';
import logger from '../../../../src/logger/logger';

type ParsedContract = {
    ast: {
        nodes: ASTNode[];
    };
};

describe('ForStatementTransformer', () => {
    let transformer: ForStatementTransformer;
    let mockContext: ReturnType<typeof mockTransformContext>;
    let testContract: ParsedContract;

    beforeAll(() => {
        const filePath = path.join(__dirname, '../../testdata/ControlFlowTestContract.sol');
        const ast = parseSolidityToAst(filePath);
        
        // The AST is already the parsed output, no need to access sources
        testContract = { ast };
    });

    beforeEach(() => {
        transformer = new ForStatementTransformer();
        mockContext = mockTransformContext();
    });

    it('should transform simple for-loop from contract', () => {
        const contractNode = testContract.ast.nodes
            .find((n: ASTNode): n is ContractDefinition => n.nodeType === 'ContractDefinition' && n.name === 'ControlFlowTest');
            
        const funcNode = contractNode?.nodes
            .find((n: ASTNode): n is FunctionDefinition => n.nodeType === 'FunctionDefinition' && n.name === 'simpleForLoop');
            
        const forStmt = funcNode?.body?.statements
            .find((n: ASTNode) => n.nodeType === 'ForStatement');
            
        if (!forStmt) return;
        
        const result = transformer.transform(forStmt, mockContext);
        expect(result?.type).toBe('ForStatement');
        expect(result?.src).toBe(forStmt.src);
    });

    it('should transform nested for-loops from contract', () => {
        const contractNode = testContract.ast.nodes
            .find((n: ASTNode): n is ContractDefinition => n.nodeType === 'ContractDefinition' && n.name === 'ControlFlowTest');
            
        const funcNode = contractNode?.nodes
            .find((n: ASTNode): n is FunctionDefinition => n.nodeType === 'FunctionDefinition' && n.name === 'nestedForLoops');
            
        const outerForStmt = funcNode?.body?.statements
            .find((n: ASTNode) => n.nodeType === 'ForStatement');
            
        if (!outerForStmt?.body?.statements) return;
        
        const innerForStmt = outerForStmt.body.statements
            .find((n: ASTNode) => n.nodeType === 'ForStatement');
            
        if (!innerForStmt) return;
        
        const outerResult = transformer.transform(outerForStmt, mockContext);
        const innerResult = transformer.transform(innerForStmt, mockContext);
        
        expect(outerResult?.type).toBe('ForStatement');
        expect(innerResult?.type).toBe('ForStatement');
    });
});
