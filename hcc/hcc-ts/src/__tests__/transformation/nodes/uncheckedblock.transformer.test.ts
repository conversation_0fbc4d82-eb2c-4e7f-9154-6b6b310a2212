import { UncheckedBlockTransformer } from '../../../../src/cpg/transformation/nodes/uncheckedblock.transformer';
import { compileSolidityContract } from '../../test-helpers';
import { mockTransformContext } from '../../transformation/test-utils';

describe('UncheckedBlockTransformer', () => {
  let transformer: UncheckedBlockTransformer;
  let context: any;
  let output: any;
  let ast: any;
  let contractNode: any;
  let uncheckedNode: any;

  beforeAll(async () => {
    output = await compileSolidityContract('UncheckedTestContract');
    const sourceKey = Object.keys(output.sources)[0];
    ast = output.sources[sourceKey].ast;
    
    contractNode = ast.nodes.find((n: any) => n.nodeType === 'ContractDefinition');
    
    // Find the first function with an unchecked block
    const funcWithUnchecked = contractNode.nodes.find(
      (n: any) => n.nodeType === 'FunctionDefinition' && 
                 n.body?.statements?.some((s: any) => s.nodeType === 'UncheckedBlock')
    );
    
    uncheckedNode = funcWithUnchecked?.body?.statements?.find(
      (s: any) => s.nodeType === 'UncheckedBlock'
    );
    
    if (!uncheckedNode) {
      throw new Error('No UncheckedBlock found in test contract');
    }
  });

  beforeEach(() => {
    transformer = new UncheckedBlockTransformer();
    context = mockTransformContext();
  });

  it('should transform UncheckedBlock to CPGNode', () => {
    const result = transformer.transform(uncheckedNode, context);
    expect(result?.type).toBe('UncheckedBlock');
  });
});
