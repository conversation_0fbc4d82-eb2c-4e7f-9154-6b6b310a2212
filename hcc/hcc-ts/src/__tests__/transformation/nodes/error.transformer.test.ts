import { ErrorTransformer } from '../../../../src/cpg/transformation/nodes/error.transformer';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import { mockTransformContext } from '../test-utils';
import path from 'path';

describe('ErrorTransformer', () => {
  let transformer: ErrorTransformer;
  let context: any;
  let errorNodes: any[];

  beforeAll(() => {
    try {
      // Parse ErrorTestContract
      const errorPath = path.join(__dirname, '../../testdata/ErrorTestContract.sol');
      const errorParse = parseSolidityToAst(errorPath);
      
      if (!errorParse || errorParse.errors?.length) {
        throw new Error(`Failed to parse error contract: ${errorParse?.errors?.map((e: {message: string}) => e.message).join(', ')}`);
      }
      
      // Find the contract
      const errorContract = errorParse.nodes.find(
        (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'ErrorTestContract'
      );
      
      if (!errorContract) {
        throw new Error('ErrorTestContract not found in parsed nodes');
      }
      
      // Then find error definitions within the contract
      errorNodes = errorContract.nodes.filter(
        (n: any) => n.nodeType === 'ErrorDefinition'
      );
      
      if (errorNodes.length === 0) {
        throw new Error('No error definitions found in ErrorTestContract');
      }
    } catch (err) {
      console.error('Test setup failed:', err);
      throw err;
    }
  });

  beforeEach(() => {
    transformer = new ErrorTransformer();
    context = mockTransformContext();
  });

  it('should transform error definitions', () => {
    errorNodes.forEach((errorNode) => {
      const result = transformer.transform(errorNode, context);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('type', 'ErrorDefinition');
      expect(result).toHaveProperty('src', errorNode.src);
      expect(result?.attributes?.name).toBe(errorNode.name);
    });
  });
});
