import { UserDefinedValueTypeTransformer } from '../../../../src/cpg/transformation/nodes/userdefinedvaluetype.transformer';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import { mockTransformContext } from '../test-utils';
import path from 'path';

describe('UserDefinedValueTypeTransformer', () => {
  let transformer: UserDefinedValueTypeTransformer;
  let context: any;
  let userDefinedTypeNode: any;

  beforeAll(() => {
    try {
      const contractPath = path.join(__dirname, '../../testdata/UserDefinedValueTypeTestContract.sol');
      const ast = parseSolidityToAst(contractPath);
      
      if (!ast) {
        throw new Error('No AST returned from parser');
      }
      
      if (!ast.nodes) {
        throw new Error('AST has no nodes property');
      }
      
      const contractDef = ast.nodes.find(
        (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'UDVTTest'
      );
      
      if (!contractDef) {
        throw new Error('Contract UDVTTest not found in AST nodes');
      }
      
      if (!contractDef.nodes) {
        throw new Error('Contract definition has no nodes');
      }
      
      // Get user-defined value types
      userDefinedTypeNode = contractDef.nodes.find(
        (n: any) => n.nodeType === 'UserDefinedValueTypeDefinition'
      );
      
      if (!userDefinedTypeNode) {
        throw new Error('No user-defined value types found in contract');
      }
    } catch (err) {
      console.error('Test setup failed:', err);
      throw err;
    }
  });

  beforeEach(() => {
    transformer = new UserDefinedValueTypeTransformer();
    context = mockTransformContext();
  });

  it('should transform user-defined value type from contract', () => {
    if (!userDefinedTypeNode) {
      console.warn('Skipping test - no user-defined value type node found');
      return;
    }
    
    const result = transformer.transform(userDefinedTypeNode, context);
    expect(result).toBeDefined();
    expect(result?.type).toBe('UserDefinedValueTypeDefinition');
    expect(result?.src).toBe(userDefinedTypeNode.src);
  });
});
