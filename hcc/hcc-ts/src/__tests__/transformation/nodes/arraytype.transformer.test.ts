import { ArrayTypeTransformer } from '../../../../src/cpg/transformation/nodes/arraytype.transformer';
import { mockTransformContext } from '../test-utils';
import { CPGNode } from '../../../../src/cpg/cpg';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import path from 'path';
import type { ASTNode, VariableDeclaration, ContractDefinition } from '../../../../src/ast/types';

type ParsedContract = {
    ast: {
        nodes: ASTNode[];
    };
};

describe('ArrayTypeTransformer', () => {
    let transformer: ArrayTypeTransformer;
    let mockContext: ReturnType<typeof mockTransformContext>;
    let testContract: ParsedContract;

    beforeAll(() => {
        const filePath = path.join(__dirname, '../../testdata/ArrayTestContract.sol');
        testContract = { ast: parseSolidityToAst(filePath) };
    });

    beforeEach(() => {
        transformer = new ArrayTypeTransformer();
        mockContext = mockTransformContext();
    });

    it('should transform dynamic storage array from contract', () => {
        const contractNode = testContract.ast.nodes
            .find((n: ASTNode): n is ContractDefinition => n.nodeType === 'ContractDefinition' && n.name === 'ArrayTestContract');
            
        const varDecl = contractNode?.nodes
            .find((n: ASTNode): n is VariableDeclaration => n.nodeType === 'VariableDeclaration' && n.name === 'dynamicArray');
            
        if (!varDecl) return;
        
        const result = transformer.transform(varDecl, mockContext);
        expect(result?.type).toBe('ArrayType');
        expect(result?.attributes.arrayKind).toBe('storage');
        expect(result?.attributes.size).toBeUndefined();
    });

    it('should transform fixed-size memory array from contract', () => {
        const contractNode = testContract.ast.nodes
            .find((n: ASTNode): n is ContractDefinition => n.nodeType === 'ContractDefinition' && n.name === 'ArrayTestContract');
            
        const varDecl = contractNode?.nodes
            .find((n: ASTNode): n is VariableDeclaration => n.nodeType === 'VariableDeclaration' && n.name === 'fixedArray');
            
        if (!varDecl) return;
        
        const result = transformer.transform(varDecl, mockContext);
        expect(result?.type).toBe('ArrayType');
        expect(result?.attributes.arrayKind).toBe('memory');
        expect(result?.attributes.size).toBe(10);
    });
});
