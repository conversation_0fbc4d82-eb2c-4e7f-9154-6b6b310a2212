import { EnumTransformer } from '../../../../src/cpg/transformation/nodes/enum.transformer';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import { mockTransformContext } from '../test-utils';
import path from 'path';
import { expect } from '@jest/globals';
import { CPGNode, CPCEdge } from '../../../../src/cpg/cpg';

describe('EnumTransformer', () => {
  let transformer: EnumTransformer;
  let context: any;
  let contractDef: any;

  beforeAll(() => {
    const contractPath = path.join(__dirname, '../../testdata/EnumTestContract.sol');
    const ast = parseSolidityToAst(contractPath);
    contractDef = ast.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'EnumTestContract'
    );
  });

  beforeEach(() => {
    transformer = new EnumTransformer();
    context = mockTransformContext();
    
    // Set up parent context for the enum
    context.parent = {
      id: 'contract_node',
      type: 'ContractDefinition',
      src: contractDef.src
    };
    context.graph.addNode(context.parent);
  });

  it('should transform enum definitions', () => {
    const enumDef = contractDef.nodes.find(
      (n: any) => n.nodeType === 'EnumDefinition'
    );
    
    const result = transformer.transform(enumDef, context);
    
    // Verify enum node was created
    expect(result).toBeDefined();
    expect(result?.type).toBe('Enum');
    expect(result?.src).toBe(enumDef.src);
    expect(result?.attributes.name).toBe(enumDef.name);
    
    // Verify members
    expect(result?.attributes.members).toEqual(
      enumDef.members.map((m: any) => m.name)
    );
    
    // Get all edges from the graph
    const allEdges: CPCEdge[] = Array.from(context.graph.edges.values()) as CPCEdge[];
    
    // Verify CONTAINS edge exists from parent to enum
    const containsEdges = allEdges.filter(
      (e: CPCEdge) => e.target === result?.id && e.type === 'CONTAINS'
    );
    expect(containsEdges.length).toBe(1);
    
    // Verify edge source is the parent contract
    expect(containsEdges[0].source).toBe(context.parent.id);
  });
});
