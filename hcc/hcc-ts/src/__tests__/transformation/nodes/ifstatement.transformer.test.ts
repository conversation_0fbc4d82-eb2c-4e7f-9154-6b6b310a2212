import { IfStatementTransformer } from "../../../../src/cpg/transformation/nodes/ifstatement.transformer";
import { parseSolidityToAst } from "../../../../src/ast/solidityAst";
import { mockTransformContext } from "../test-utils";
import path from "path";

describe("IfStatementTransformer", () => {
  let transformer: IfStatementTransformer;
  let context: any;
  let ifNode: any;

  beforeAll(() => {
    const contractPath = path.join(__dirname, '../../testdata/ControlFlowTestContract.sol');
    const parseResult = parseSolidityToAst(contractPath);
    
    if (!parseResult) {
      throw new Error('Failed to parse Solidity file');
    }
    
    const contractDef = parseResult.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'ControlFlowTest'
    ) || {};
    
    const functionDef = contractDef.nodes?.find(
      (n: any) => n.nodeType === 'FunctionDefinition' && n.name === 'simpleIf'
    );
    
    ifNode = functionDef?.body?.statements?.find(
      (s: any) => s.nodeType === 'IfStatement'
    );
    
    if (!ifNode) {
      throw new Error('No if statement found in simpleIf function');
    }
  });

  beforeEach(() => {
    transformer = new IfStatementTransformer();
    context = mockTransformContext();
  });

  it("should transform if statement from contract", () => {
    const result = transformer.transform(ifNode, context);
    expect(result).toBeDefined();
    expect(result?.type).toBe("IfStatement");
    expect(result?.src).toBe(ifNode.src);
  });
});
