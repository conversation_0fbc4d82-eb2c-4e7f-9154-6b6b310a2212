import { YulTransformer } from '../../../../src/cpg/transformation/nodes/yul.transformer';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import { mockTransformContext } from '../test-utils';
import path from 'path';

describe('YulTransformer', () => {
  let transformer: YulTransformer;
  let context: any;
  let yulNode: any;

  beforeAll(() => {
    const contractPath = path.join(__dirname, '../../testdata/YulTestContract.sol');
    const parseResult = parseSolidityToAst(contractPath);
    
    if (!parseResult) {
      throw new Error('Failed to parse contract');
    }
    
    const contractDef = parseResult.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'YulTestContract'
    ) || {};
    
    const functionDef = contractDef.nodes?.find(
      (n: any) => n.nodeType === 'FunctionDefinition' && n.name === 'storeYul'
    );
    
    yulNode = functionDef?.body?.statements?.find(
      (s: any) => s.nodeType === 'InlineAssembly'
    );
    
    if (!yulNode) {
      throw new Error('YUL block not found');
    }
  });

  beforeEach(() => {
    transformer = new YulTransformer();
    context = mockTransformContext();
    context.parent = { name: 'storeYul' };
  });

  it('should transform YUL block from contract', () => {
    const result = transformer.transform(yulNode, context);
    expect(result).toBeDefined();
    expect(result?.type).toBe('YulBlock');
    expect(result?.src).toBe(yulNode.src);
  });

  it('should detect storage access in YUL', () => {
    const result = transformer.transform(yulNode, context);
    expect(result?.attributes.containsStorageAccess).toBe(true);
  });
});
