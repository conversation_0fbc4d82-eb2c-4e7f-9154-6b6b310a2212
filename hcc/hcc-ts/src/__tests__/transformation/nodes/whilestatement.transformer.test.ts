import { WhileStatementTransformer } from '../../../../src/cpg/transformation/nodes/whilestatement.transformer';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import { mockTransformContext } from '../test-utils';
import path from 'path';

describe('WhileStatementTransformer', () => {
  let transformer: WhileStatementTransformer;
  let context: any;
  let whileNode: any;

  beforeAll(() => {
    try {
      const contractPath = path.join(__dirname, '../../testdata/ControlFlowTestContract.sol');
      const ast = parseSolidityToAst(contractPath);
      
      if (!ast?.nodes) {
        throw new Error('AST nodes not found');
      }
      
      const contractDef = ast.nodes.find(
        (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'ControlFlowTest'
      );
      
      if (!contractDef) {
        throw new Error(`Contract ControlFlowTest not found in AST nodes: ${JSON.stringify(ast.nodes.map((n: any) => ({nodeType: n.nodeType, name: n.name})), null, 2)}`);
      }
      
      if (!contractDef.nodes) {
        throw new Error(`Contract definition has no nodes property: ${JSON.stringify(contractDef, null, 2)}`);
      }
      
      // Find the function containing while loops
      const functionDef = contractDef.nodes.find(
        (n: any) => n.nodeType === 'FunctionDefinition' && n.name === 'nestedWhileLoops'
      );
      
      if (!functionDef) {
        throw new Error(`Function nestedWhileLoops not found in contract nodes: ${JSON.stringify(contractDef.nodes.map((n: any) => ({nodeType: n.nodeType, name: n.name})), null, 2)}`);
      }
      
      // Get the while statement
      whileNode = functionDef.body?.statements?.find(
        (s: any) => s.nodeType === 'WhileStatement'
      );
    } catch (err) {
      console.error('Failed to setup test:', err);
      throw err; // Re-throw to fail the test
    }
  });

  beforeEach(() => {
    transformer = new WhileStatementTransformer();
    context = mockTransformContext();
  });

  it('should transform while statement from contract', () => {
    if (!whileNode) {
      console.warn('Skipping test - no while node found');
      return;
    }
    
    const result = transformer.transform(whileNode, context);
    expect(result).toBeDefined();
    expect(result?.type).toBe('WhileStatement');
    expect(result?.src).toBe(whileNode.src);
  });
});
