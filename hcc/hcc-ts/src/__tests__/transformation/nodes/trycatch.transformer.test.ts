import { TryCatchTransformer } from '../../../../src/cpg/transformation/nodes/trycatch.transformer';
import { parseSolidityToAst } from '../../../../src/ast/solidityAst';
import { mockTransformContext } from '../test-utils';
import path from 'path';
import { expect } from '@jest/globals';
import { CPCEdge, CPGNode } from '../../../../src/cpg/cpg';

describe('TryCatchTransformer', () => {
  let transformer: TryCatchTransformer;
  let context: any;
  let contractDef: any;

  beforeAll(() => {
    const contractPath = path.join(__dirname, '../../testdata/TryCatchTestContract.sol');
    const ast = parseSolidityToAst(contractPath);
    contractDef = ast.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'TryCatchTestContract'
    );
  });

  beforeEach(() => {
    transformer = new TryCatchTransformer();
    context = mockTransformContext();
  });

  function testTryCatchFunction(functionName: string) {
    const functionDef = contractDef.nodes.find(
      (n: any) => n.nodeType === 'FunctionDefinition' && n.name === functionName
    );
    const tryNode = functionDef.body.statements.find(
      (s: any) => s.nodeType === 'TryStatement'
    );
    
    // Transform the try statement
    const tryResult = transformer.transform(tryNode, context);
    
    // Verify try node was created
    expect(tryResult).toBeDefined();
    expect(tryResult?.type).toBe('TryStatement');
    expect(tryResult?.src).toBe(tryNode.src);
    
    // Get all nodes from the graph
    const allNodes: CPGNode[] = Array.from(context.graph.nodes.values()) as CPGNode[];
    
    // Find all catch nodes in the graph
    const catchNodes = allNodes.filter(
      (n: CPGNode) => n.type === 'CatchClause'
    );
    
    // Should have at least one catch node
    expect(catchNodes.length).toBeGreaterThan(0);
    
    // Get all edges from the graph
    const allEdges: CPCEdge[] = Array.from(context.graph.edges.values()) as CPCEdge[];
    
    // Verify HAS_CATCH edges exist
    const hasCatchEdges = allEdges.filter(
      (e: CPCEdge) => e.type === 'HAS_CATCH'
    );
    
    expect(hasCatchEdges.length).toBeGreaterThan(0);
    
    // Verify CONTAINS edges exist for each catch node
    catchNodes.forEach((catchNode: CPGNode) => {
      const containsEdges = allEdges.filter(
        (e: CPCEdge) => e.source === catchNode.id && e.type === 'CONTAINS'
      );
      expect(containsEdges.length).toBeGreaterThan(0);
    });
  }

  it('should properly transform nestedTryCatch', () => {
    testTryCatchFunction('nestedTryCatch');
  });

  it('should properly transform simpleTryCatch', () => {
    testTryCatchFunction('simpleTryCatch');
  });

  it('should properly transform multipleCatches', () => {
    testTryCatchFunction('multipleCatches');
  });
});
