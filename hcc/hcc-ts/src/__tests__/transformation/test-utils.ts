import { TransformContext } from '../../cpg/transformation/core/transformer';
import { CPGNode, CPGGraph, RelationshipType, CPCEdge } from '../../cpg';
import { ASTNode } from '../../ast/types';

interface MockCPGGraph extends CPGGraph {
  nodes: CPGNode[];
  edges: CPCEdge[];
  addNode(node: CPGNode): CPGNode;
  addEdge(edge: CPCEdge): void;
  getNode(id: string): CPGNode | undefined;
  getNodeChildren(id: string): CPGNode[];
}

export function mockTransformContext(
  parent?: CPGNode,
  initialGraph: {nodes?: CPGNode[]; edges?: CPCEdge[]} = {}
): TransformContext {
  const nodes = initialGraph.nodes || [];
  const edges = initialGraph.edges || [];
  
  const graph: CPGGraph = {
    nodes,
    edges,
    addNode: (node: CPGNode) => {
      nodes.push(node);
      return node;
    },
    addEdge: (edge: CPCEdge) => {
      edges.push(edge);
    },
    getNode: (id: string) => {
      return nodes.find(n => n.id === id);
    },
    getNodeChildren: (id: string) => {
      return edges
        .filter(e => e.source === id)
        .map(e => nodes.find(n => n.id === e.target))
        .filter(Boolean) as CPGNode[];
    }
  };
  
  return {
    graph,
    parent,
    transformChild: (node: ASTNode) => {
      const childNode: CPGNode = {
        id: `mock-${node.nodeType}-${Math.random().toString(36).substring(2, 8)}`,
        type: node.nodeType,
        src: node.src || '0:0:0',
        attributes: {}
      };
      graph.addNode(childNode);
      if (parent) {
        graph.addEdge({
          source: parent.id,
          target: childNode.id,
          type: RelationshipType.CONTAINS,
          properties: {}
        });
      }
      return childNode;
    },
    generateId: () => `mock-id-${Math.random().toString(36).substring(2, 8)}`
  };
}
