import { readFileSync, existsSync } from 'fs';
import { join, isAbsolute } from 'path';
import solc from 'solc';
import { StructDefinition } from '../ast/types';
import { CPGGraph } from '../cpg/cpg';

export function mockStructDefinition(name: string, members: Array<{name: string, type: string}>): StructDefinition {
  return {
    nodeType: 'StructDefinition',
    name,
    src: '0:0:0',
    members: members.map(m => ({
      nodeType: 'VariableDeclaration',
      name: m.name,
      type: m.type,
      src: '0:0:0'
    }))
  };
}

export function compileSolidityContract(contractName: string) {
  const contractPath = contractName.endsWith('.sol') ? contractName : `${contractName}.sol`;
  
  // Try multiple possible locations
  const searchPaths = [
    contractPath,
    join(__dirname, 'testdata', contractPath),
    join(__dirname, '../testdata', contractPath),
    join(__dirname, '../../testdata', contractPath),
    join(process.cwd(), 'src/__tests__/testdata', contractPath),
    join(process.cwd(), 'dist/__tests__/testdata', contractPath)
  ];

  const finalPath = searchPaths.find(p => existsSync(p));
  if (!finalPath) {
    throw new Error(`Contract file not found. Searched:\n${searchPaths.map(p => `- ${p}`).join('\n')}`);
  }

  const source = readFileSync(finalPath, 'utf8');
  
  const input = {
    language: 'Solidity',
    sources: { [finalPath]: { content: source } },
    settings: {
      outputSelection: {
        "*": { "*": ["ast"], "": ["ast"] }
      }
    }
  };

  const output = JSON.parse(solc.compile(JSON.stringify(input)));
  if (output.errors) {
    const errors = output.errors.filter((e: any) => e.severity === 'error');
    if (errors.length > 0) throw new Error(errors.map((e: any) => e.formattedMessage).join('\n'));
  }

  return output;
}

export function findASTNode(ast: any, nodeType: string, name?: string): any {
  if (!ast?.nodes) return null;
  
  // First look in contract nodes
  const contractNode = ast.nodes.find((n: any) => n.nodeType === 'ContractDefinition');
  if (!contractNode?.nodes) return null;
  
  // Search all nodes recursively
  const searchNodes = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.nodeType === nodeType && (!name || node.name === name)) {
        return node;
      }
      
      // Recursively search child nodes
      if (node.nodes) {
        const found = searchNodes(node.nodes);
        if (found) return found;
      }
      
      // Check parameters for functions/events/etc
      if (node.parameters?.parameters) {
        const found = searchNodes(node.parameters.parameters);
        if (found) return found;
      }
    }
    return null;
  };
  
  return searchNodes(contractNode.nodes);
}

export function findNodes(ast: any, predicate: (node: any) => boolean): any[] {
  const results: any[] = [];
  
  function traverse(node: any) {
    if (predicate(node)) {
      results.push(node);
    }
    
    // Traverse all object properties that might contain AST nodes
    for (const key in node) {
      if (key === 'parent' || key === 'id') continue; // Skip non-child references
      
      const value = node[key];
      if (value && typeof value === 'object') {
        if (Array.isArray(value)) {
          value.forEach(child => {
            if (child && typeof child === 'object') {
              traverse(child);
            }
          });
        } else {
          traverse(value);
        }
      }
    }
  }
  
  traverse(ast);
  return results;
}

export function findPotentialOverflows(ast: any, cpgGraph?: CPGGraph): any[] {
  return findNodes(ast, (node) => {
    if (node.nodeType === 'BinaryOperation' && ['+', '*'].includes(node.operator)) {
      // Check if operation involves tainted values
      const leftTainted = isNodeTainted(node.leftExpression, cpgGraph);
      const rightTainted = isNodeTainted(node.rightExpression, cpgGraph);
      
      // Check for overflow protection
      let hasProtection = false;
      let parent = node.parent;
      while (parent) {
        if (parent.nodeType === 'FunctionCall' && 
            ['require', 'assert', 'safeMathOp'].includes(parent.expression?.name)) {
          hasProtection = true;
          break;
        }
        parent = parent.parent;
      }
      
      return (leftTainted || rightTainted) && !hasProtection;
    }
    return false;
  });
}

function isNodeTainted(node: any, cpgGraph?: CPGGraph): boolean {
  if (!node || !cpgGraph) return false;
  
  // For identifiers, check their variable declaration
  if (node.nodeType === 'Identifier') {
    const varNode = cpgGraph.getNode(`Variable_${node.src}`);
    return varNode?.attributes?.taintStatus === 'tainted';
  }
  
  // For literals, they're never tainted
  if (node.nodeType === 'Literal') return false;
  
  // For other expressions, check recursively
  return isNodeTainted(node.leftExpression, cpgGraph) || 
         isNodeTainted(node.rightExpression, cpgGraph);
}
