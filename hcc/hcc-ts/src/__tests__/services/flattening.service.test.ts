import { flattenFolder } from '../../services/flattening.service';
import { existsSync, readFileSync } from 'fs';

describe('FlatteningService', () => {
  it('should flatten folder and save output', () => {
    const outputPath = flattenFolder('src/__tests__/fixtures/flattening');
    
    expect(existsSync(outputPath)).toBeTruthy();
    
    const content = readFileSync(outputPath, 'utf-8');
    expect(content).toContain('TestContract');
    expect(content).toContain('ImportedTestContract');
  });

  it('should throw error for empty contracts folder', () => {
    expect(() => flattenFolder('src/__tests__/fixtures/empty-contracts'))
      .toThrow('No Solidity files found in src/__tests__/fixtures/empty-contracts');
  });
});
