// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

library MathLib {
    function add(uint a, uint b) internal pure returns (uint) {
        return a + b;
    }
}

// Comprehensive test contract for all transformer tests
contract FullTestContract {
    // Add this directive
    using Math<PERSON>ib for uint;
    
    // Contract-level elements
    string public name = "TestContract";
    address public owner;
    
    // Variables
    uint256 public value;
    mapping(address => uint256) public balances;
    
    // Additional state variables with different types
    string public constant CONTRACT_NAME = "FullTestContract";
    bytes32 private secretHash;
    uint256[3] public fixedArray;
    
    // Custom types
    struct User {
        address addr;
        uint256 score;
    }
    
    enum Status { Pending, Active, Inactive }
    
    User[] public users;
    
    // Events
    event ValueChanged(uint256 newValue);
    event AnonymousEvent() anonymous;
    
    // Errors
    error Unauthorized(address caller);
    
    // Modifiers
    modifier onlyOwner() {
        if (msg.sender != owner) revert Unauthorized(msg.sender);
        _;
    }
    
    modifier whenNotPaused() {
        require(!paused);
        _;
    }
    
    bool public paused = false;
    
    // Constructor
    constructor() {
        owner = msg.sender;
        secretHash = keccak256(abi.encodePacked(block.timestamp));
    }
    
    // Functions
    function setValue(uint256 _value) public onlyOwner {
        value = _value;
        emit ValueChanged(_value);
    }
    
    function getUser(address _addr) public view returns (User memory) {
        return User(_addr, balances[_addr]);
    }
    
    function complexFunction(uint256 param) public {
        // Local variables with different storage locations
        uint256 localValue = param * 2;
        User memory tempUser = User(msg.sender, balances[msg.sender]);
        User storage storedUser = users[0];
        
        // Complex local variable
        mapping(address => uint256) storage userBalances = balances;
        
        // Array and struct operations
        fixedArray[0] = localValue;
        storedUser.score += param;
        
        // Function call with local variable
        uint256 result = innerFunction(localValue);
        
        // State variable update
        value = result;
    }
    
    function innerFunction(uint256 input) private pure returns (uint256) {
        // Additional local variables
        uint256[] memory dynamicArray = new uint256[](3);
        dynamicArray[0] = input;
        
        return dynamicArray[0] * 3;
    }
    
    function setValueWithMultipleModifiers(uint256 newValue) public onlyOwner whenNotPaused {
        value = newValue;
    }
}
