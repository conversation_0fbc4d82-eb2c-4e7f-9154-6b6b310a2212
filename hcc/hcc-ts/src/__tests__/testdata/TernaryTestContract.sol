// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract TernaryTestContract {
    function basicTernary(uint x) public pure returns (uint) {
        return x > 0 ? x : 1;
    }
    
    function nestedTernary(uint x, uint y) public pure returns (uint) {
        return x > y ? (y > 0 ? y : 1) : (x > 0 ? x : 1);
    }
    
    function withSideEffects(uint x) public pure returns (uint) {
        return x > 0 ? x + 1 : x - 1;
    }
}
