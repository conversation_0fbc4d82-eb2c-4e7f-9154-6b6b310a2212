// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract TryCatchTestContract {
    error CustomError();
    error AnotherError();

    function simpleTryCatch(address target) public view {
        try this.externalCall(target) {
            // Success case
        } catch Error(string memory) /*reason*/ {
            // Catch revert("reason")
        } catch (bytes memory) /*lowLevelData*/ {
            // Catch other errors
        }
    }

    function nestedTryCatch(address target) public view {
        try this.externalCall(target) {
            try this.anotherCall(target) {
                // Nested success
            } catch {
                // Empty catch
            }
        } catch (bytes memory) {
            // Outer catch
        }
    }

    function multipleCatches(address target) public view {
        try this.externalCall(target) {
            // Success
        } catch (bytes memory reason) {
            if (reason.length == 0) {
                // Empty revert
            } else if (bytes4(reason) == CustomError.selector) {
                // Custom error 1
            } else if (bytes4(reason) == AnotherError.selector) {
                // Custom error 2
            } else {
                // Generic catch
            }
        }
    }

    function externalCall(address target) external pure {
        if (target == address(0)) revert CustomError();
        if (target == address(1)) revert AnotherError();
    }

    function anotherCall(address target) external pure {
        // May revert
    }
}
