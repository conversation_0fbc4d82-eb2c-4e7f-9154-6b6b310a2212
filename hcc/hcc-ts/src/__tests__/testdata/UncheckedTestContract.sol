// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract UncheckedTestContract {
    function safeDecrement(uint x) public pure returns (uint) {
        return x - 1; // Overflow checked
    }
    
    function unsafeDecrement(uint x) public pure returns (uint) {
        unchecked { return x - 1; } // No overflow check
    }
    
    function nestedUnchecked(uint x) public pure returns (uint) {
        unchecked {
            return x - 2; // Removed double unchecked
        }
    }
    
    function complexUncheckedMath(uint x, uint y) public pure returns (uint) {
        unchecked {
            uint z = x * y;
            return z + (x - y); // Multiple operations in one unchecked block
        }
    }
    
    function mixedCheckedUnchecked(uint x) public pure returns (uint) {
        uint a = x + 1; // Checked
        unchecked {
            uint b = a - x; // Unchecked
            return b * 2; // Still unchecked
        }
    }
    
    function uncheckedWithOtherStatements(uint x) public pure {
        unchecked {
            if (x > 10) {
                x -= 10;
            } else {
                x += 10;
            }
        }
    }
    
    function safeAdd(uint256 a, uint256 b) public pure returns (uint256) {
        unchecked {
            return a + b;
        }
    }

    function safeSub(uint256 a, uint256 b) public pure returns (uint256) {
        unchecked {
            return a - b;
        }
    }
}
