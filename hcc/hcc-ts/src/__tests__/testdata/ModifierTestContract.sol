// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract ModifierTestContract {
    address public owner;
    uint256 public value;
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Not owner");
        _;
    }
    
    modifier validValue(uint256 _value) {
        require(_value > 0, "Value must be positive");
        _;
    }
    
    constructor() {
        owner = msg.sender;
    }
    
    function setValue(uint256 _value) public onlyOwner validValue(_value) {
        value = _value;
    }
}
