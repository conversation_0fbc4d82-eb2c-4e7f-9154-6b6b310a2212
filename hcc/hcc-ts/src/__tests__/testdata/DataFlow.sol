// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract DataFlowTest {
    struct User {
        address addr;
        uint balance;
    }
    
    mapping(address => User) private users;
    
    function updateUser(address userAddr, uint newBalance) public {
        User storage user = users[userAddr];
        user.addr = userAddr;
        user.balance = newBalance;
    }
    
    function transferBalance(address from, address to, uint amount) public {
        require(users[from].balance >= amount, "Insufficient balance");
        users[from].balance -= amount;
        users[to].balance += amount;
    }
}
