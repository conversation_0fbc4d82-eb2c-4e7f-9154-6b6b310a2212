// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Sample {
    uint256 public count;

    event Increment(address indexed user, uint256 value);
    error Unauthorized(address caller);

    modifier onlyOwner() {
        if (msg.sender != owner) revert Unauthorized(msg.sender);
        _;
    }

    address public owner;

    constructor() {
        owner = msg.sender;
    }

    function increment(uint256 value) public onlyOwner {
        count += value;
        emit Increment(msg.sender, value);
    }

    function unsafeAdd(uint256 a, uint256 b) public pure returns (uint256) {
        unchecked {
            return a + b;
        }
    }
}
