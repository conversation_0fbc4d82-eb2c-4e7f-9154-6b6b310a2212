// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract ForLoopTestContract {
    function simpleForLoop(uint n) public pure returns (uint) {
        uint sum = 0;
        for (uint i = 0; i < n; i++) {
            sum += i;
        }
        return sum;
    }
    
    function nestedForLoops(uint n, uint m) public pure returns (uint) {
        uint product = 0;
        for (uint i = 0; i < n; i++) {
            for (uint j = 0; j < m; j++) {
                product += i * j;
            }
        }
        return product;
    }
}
