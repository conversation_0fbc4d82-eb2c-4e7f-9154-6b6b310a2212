// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract UDVTTest {
    // Basic UDVTs
    type Balance is uint256;
    type AccountID is address;
    
    // UDVT with operations
    type Price is uint128;
    
    // Usage examples
    Balance private balance;
    AccountID private account;
    
    function setBalance(Balance newBalance) public {
        balance = newBalance;
    }
    
    function getBalance() public view returns (Balance) {
        return balance;
    }
    
    function transfer(AccountID to, Price amount) public {
        // Function using multiple UDVTs
    }
}
