// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract D {
    function funcD() public pure returns (uint) {
        return 1;
    }
}

contract C is D {
    function funcC() public pure returns (uint) {
        return 2;
    }
}

contract B is D {
    function funcB() public pure returns (uint) {
        return 3;
    }
}

contract A is B, C {
    function funcA() public pure returns (uint) {
        return 4;
    }
}
