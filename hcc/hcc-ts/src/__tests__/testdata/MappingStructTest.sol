// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract MappingStructTest {
    struct Person {
        string name;
        uint256 age;
    }

    mapping(address => Person) public people;
    mapping(uint256 => uint256) public balances;

    function set<PERSON>erson(address addr, string memory name, uint256 age) public {
        people[addr] = Person(name, age);
    }

    function setBalance(uint256 key, uint256 value) public {
        balances[key] = value;
    }

    function getPerson(address addr) public view returns (string memory, uint256) {
        Person storage p = people[addr];
        return (p.name, p.age);
    }
}
