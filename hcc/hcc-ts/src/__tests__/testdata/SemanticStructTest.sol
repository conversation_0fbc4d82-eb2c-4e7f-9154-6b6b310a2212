// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

library MathLib {
    function add(uint256 a, uint256 b) internal pure returns (uint256) {
        return a + b;
    }
}

contract SemanticStructTest {
    event TestEvent(address indexed sender, uint256 value);
    
    modifier onlyEven(uint256 x) {
        require(x % 2 == 0, "Not even");
        _;
    }
    
    uint256 public value;
    
    constructor(uint256 _value) onlyEven(_value) {
        value = _value;
    }
    
    function setValue(uint256 _value) public onlyEven(_value) {
        value = MathLib.add(value, _value);
        emit TestEvent(msg.sender, value);
    }
    
    fallback() external payable {}
    receive() external payable {}
    
    using MathLib for uint256;
}
