// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract ArrayTestContract {
    // Storage arrays
    uint[] public dynamicStorageArray;
    address[10] public fixedStorageArray;
    
    // Memory arrays in functions
    function memoryArrayOps(uint size) public pure returns (uint[] memory) {
        uint[] memory memArray = new uint[](size);
        memArray[0] = 1; // Should flag if size == 0
        return memArray;
    }
    
    // Calldata arrays
    function sum(uint[] calldata nums) external pure returns (uint) {
        uint total;
        for (uint i = 0; i < nums.length; i++) {
            total += nums[i]; // Should check bounds
        }
        return total;
    }
    
    // Dangerous operations
    function unsafeAccess(uint index) public view returns (uint) {
        return dynamicStorageArray[index]; // No bounds check
    }
}
