// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract EnumTestContract {
    enum OrderStatus {
        Pending,
        Shipped,
        Delivered,
        Cancelled
    }
    
    enum UserRole {
        Guest,
        Member,
        Admin
    }
    
    OrderStatus public status;
    UserRole public role;
    
    function updateStatus(OrderStatus newStatus) public {
        status = newStatus;
    }
    
    function updateRole(UserRole newRole) public {
        role = newRole;
    }
}
