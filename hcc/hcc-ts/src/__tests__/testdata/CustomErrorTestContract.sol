// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract CustomErrorTest {
    // Custom errors with different parameter types
    error SimpleError();
    error WithParams(uint code, string message);
    error ComplexError(address sender, uint value, bytes data);

    function testRevertSimple() public pure {
        revert SimpleError();
    }

    function testRevertWithParams(uint code, string calldata message) public pure {
        revert WithParams(code, message);
    }

    function testRevertComplex(address sender, uint value, bytes calldata data) public pure {
        revert ComplexError(sender, value, data);
    }
}
