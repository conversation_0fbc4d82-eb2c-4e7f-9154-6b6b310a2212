// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract EventTestContract {
    event Deposit(
        address indexed user,
        uint256 amount,
        uint256 timestamp
    );
    
    event Withdrawal(
        address indexed user,
        uint256 amount,
        uint256 timestamp
    );
    
    function deposit() public payable {
        emit Deposit(msg.sender, msg.value, block.timestamp);
    }
    
    function withdraw(uint256 amount) public {
        emit Withdrawal(msg.sender, amount, block.timestamp);
    }
}
