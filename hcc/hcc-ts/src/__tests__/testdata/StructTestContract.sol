// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract StructTestContract {
    struct User {
        address wallet;
        uint256 balance;
        bool isActive;
    }
    
    struct Order {
        uint256 id;
        uint256 amount;
        address buyer;
    }
    
    User public admin;
    Order[] public orders;
    
    constructor(address _admin) {
        admin = User(_admin, 0, true);
    }
    
    function createOrder(uint256 _amount) public {
        orders.push(Order({
            id: orders.length,
            amount: _amount,
            buyer: msg.sender
        }));
    }
}
