// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

contract OverflowTest {
    // State variables
    uint256 public totalSupply;
    mapping(address => uint256) public balances;
    
    // Global constant
    uint256 public constant MAX_UINT = type(uint256).max;
    
    // Function with tainted argument
    function deposit(uint256 amount) public {
        require(amount > 0, "Amount must be positive");
        
        // Local variables
        uint256 newBalance = balances[msg.sender] + amount;
        
        // Potential overflow check
        require(newBalance > balances[msg.sender], "Overflow detected");
        
        balances[msg.sender] = newBalance;
        totalSupply += amount;
    }
    
    // Function with potential overflow
    function unsafeTransfer(address to, uint256 amount) public {
        // No overflow check
        balances[to] += amount;
        balances[msg.sender] -= amount;
    }
}
