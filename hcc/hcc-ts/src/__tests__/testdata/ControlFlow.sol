// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract ControlFlowTest {
    function complexFlow(uint x) public pure returns (uint) {
        uint result = 0;
        
        if (x > 10) {
            for (uint i = 0; i < x; i++) {
                if (i % 2 == 0) {
                    result += i;
                } else {
                    result -= i;
                }
            }
        } else {
            while (x > 0) {
                result += x;
                x--;
            }
        }
        
        return result;
    }
}
