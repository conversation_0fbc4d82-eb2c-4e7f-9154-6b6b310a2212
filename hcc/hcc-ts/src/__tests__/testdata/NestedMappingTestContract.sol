// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract TokenApprovals {
    // Nested mapping structure:
    // owner => (operator => amount)
    mapping(address => mapping(address => uint256)) private _approvals;
    
    // Simple token balances
    mapping(address => uint256) private _balances;
    
    // Events
    event Approval(address indexed owner, address indexed operator, uint256 amount);
    
    // Approve an operator to spend tokens on behalf of owner
    function approve(address operator, uint256 amount) external {
        _approvals[msg.sender][operator] = amount;
        emit Approval(msg.sender, operator, amount);
    }
    
    // Get approval amount for operator on behalf of owner
    function allowance(address owner, address operator) external view returns (uint256) {
        return _approvals[owner][operator];
    }
    
    // Transfer tokens using approval
    function transferFrom(
        address from,
        address to,
        uint256 amount
    ) external {
        require(_approvals[from][msg.sender] >= amount, "Insufficient allowance");
        require(_balances[from] >= amount, "Insufficient balance");
        
        _approvals[from][msg.sender] -= amount;
        _balances[from] -= amount;
        _balances[to] += amount;
    }
}
