// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract ErrorTestContract {
    error InsufficientBalance(uint256 available, uint256 required);
    error Unauthorized(address caller);
    
    function transfer(uint256 amount) public view {
        if (amount > address(this).balance) {
            revert InsufficientBalance(address(this).balance, amount);
        }
    }
    
    function adminAction() public view {
        if (msg.sender != address(0x123)) {
            revert Unauthorized(msg.sender);
        }
    }
}
