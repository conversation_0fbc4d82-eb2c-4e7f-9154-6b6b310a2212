// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

contract ControlFlowTest {
    function simpleIf(uint x) public pure returns (uint) {
        if (x > 10) {
            return x;
        }
        return 0;
    }
    
    function ifElse(uint x) public pure returns (uint) {
        if (x > 100) {
            return x * 2;
        } else {
            return x / 2;
        }
    }
    
    function nestedIf(uint x, bool flag) public pure returns (uint) {
        if (x > 50) {
            if (flag) {
                return x + 10;
            } else {
                return x - 10;
            }
        }
        return x;
    }
    
    function complexCondition(address addr, uint balance) public view returns (bool) {
        if (addr != address(0) && balance > 100 ether || balance == 0) {
            return true;
        }
        return false;
    }
    
    function simpleForLoop(uint n) public pure returns (uint) {
        uint sum = 0;
        for (uint i = 0; i < n; i++) {
            sum += i;
        }
        return sum;
    }
    
    function nestedForLoops(uint n, uint m) public pure returns (uint) {
        uint product = 0;
        for (uint i = 0; i < n; i++) {
            for (uint j = 0; j < m; j++) {
                product += i * j;
            }
        }
        return product;
    }
    
    function complexForLoop(uint[] memory arr) public pure returns (uint) {
        uint sum = 0;
        for (uint i = 0; i < arr.length; i++) {
            if (arr[i] % 2 == 0) {
                continue;
            }
            sum += arr[i];
            
            for (uint j = 0; j < arr[i]; j++) {
                if (j > 5) {
                    break;
                }
                sum += j;
            }
        }
        return sum;
    }
    
    function emptyForLoop() public pure {
        for (;;) {
            break;
        }
    }
    
    function simpleWhileLoop(uint n) public pure returns (uint) {
        uint sum = 0;
        uint i = 0;
        while (i < n) {
            sum += i;
            i++;
        }
        return sum;
    }
    
    function nestedWhileLoops(uint n, uint m) public pure returns (uint) {
        uint product = 0;
        uint i = 0;
        while (i < n) {
            uint j = 0;
            while (j < m) {
                product += i * j;
                j++;
            }
            i++;
        }
        return product;
    }
    
    function whileWithBreakContinue(uint[] memory arr) public pure returns (uint) {
        uint sum = 0;
        uint i = 0;
        while (i < arr.length) {
            if (arr[i] % 2 == 0) {
                i++;
                continue;
            }
            
            sum += arr[i];
            
            if (sum > 100) {
                break;
            }
            
            i++;
        }
        return sum;
    }
    
    function infiniteWhileLoop() public pure {
        while (true) {
            // In real code this would need a break
        }
    }
    
    function simpleDoWhileLoop(uint n) public pure returns (uint) {
        uint sum = 0;
        uint i = 0;
        do {
            sum += i;
            i++;
        } while (i < n);
        return sum;
    }
    
    function nestedDoWhileLoops(uint n, uint m) public pure returns (uint) {
        uint product = 0;
        uint i = 0;
        do {
            uint j = 0;
            do {
                product += i * j;
                j++;
            } while (j < m);
            i++;
        } while (i < n);
        return product;
    }
    
    function doWhileWithBreakContinue(uint[] memory arr) public pure returns (uint) {
        uint sum = 0;
        uint i = 0;
        do {
            if (i >= arr.length) {
                break;
            }
            
            if (arr[i] % 2 == 0) {
                i++;
                continue;
            }
            
            sum += arr[i];
            i++;
        } while (true);
        return sum;
    }
}
