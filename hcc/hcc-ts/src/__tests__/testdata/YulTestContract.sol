// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract YulTestContract {
    // Simple Yul addition
    function addYul(uint x, uint y) public pure returns (uint) {
        assembly {
            let result := add(x, y)
            mstore(0x0, result)
            return(0x0, 32)
        }
    }
    
    // Storage pointer in Yul
    function storeYul(uint value) public {
        assembly {
            sstore(0, value)
        }
    }
    
    // Dangerous low-level call
    function unsafeCall(address target) public {
        assembly {
            pop(call(gas(), target, 0, 0, 0, 0, 0))
        }
    }
}
