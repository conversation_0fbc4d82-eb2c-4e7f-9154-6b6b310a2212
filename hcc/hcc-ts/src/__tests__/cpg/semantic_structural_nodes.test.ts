import { astToCpg } from '../../cpg/astToCpg';

// Complete test AST data with contract definition
const TEST_AST = {
  nodes: [
    {
      nodeType: 'ContractDefinition',
      id: 1,
      name: 'TestStructs',
      nodes: [
        {
          nodeType: 'StructDefinition',
          id: 2,
          name: 'Person',
          members: []
        },
        {
          nodeType: 'ArrayTypeName',
          id: 3,
          baseType: {
            nodeType: 'UserDefinedTypeName',
            name: 'Person'
          }
        }
      ]
    }
  ]
};

describe('Semantic/Structural Node CPG', () => {
  let cpg: any;
  
  beforeAll(() => {
    cpg = astToCpg({
      success: true,
      results: {
        ast: {
          nodeType: 'SourceUnit',
          src: '0:0:0',
          children: [
            {
              nodeType: 'Contract',
              name: 'TestContract',
              src: '1:1:0',
              children: [
                {
                  nodeType: 'Function',
                  name: 'testFunction',
                  src: '2:2:0'
                },
                {
                  nodeType: 'Variable',
                  name: 'testVar',
                  src: '3:3:0'
                }
              ]
            }
          ]
        }
      }
    }, true);
  });

  it('should contain test nodes', () => {
    const nodeTypes = cpg.nodes.map((n: any) => n.type);
    expect(nodeTypes).toContain('Contract');
    expect(nodeTypes).toContain('Function');
    expect(nodeTypes).toContain('Variable');
  });

  it('should maintain node relationships', () => {
    const contractNode = cpg.nodes.find((n: any) => n.type === 'Contract');
    const functionNode = cpg.nodes.find((n: any) => n.type === 'Function');
    
    expect(
      cpg.edges.some(
        (e: any) =>
          e.source === contractNode.id &&
          e.target === functionNode.id &&
          e.type === 'CONTAINS'
      )
    ).toBe(true);
  });
});
