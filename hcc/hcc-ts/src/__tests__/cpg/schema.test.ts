import { NodeType, RelationshipType, Neo4jSchema } from '../../cpg/schema/neo4j-schema';
import neo4j, { Driver } from 'neo4j-driver';

describe('Neo4j Schema', () => {
  let driver: Driver;

  beforeAll(async () => {
    driver = neo4j.driver(
      'bolt://localhost:7687',
      neo4j.auth.basic('neo4j', 'new_secure_password'),
      {
        disableLosslessIntegers: true,
        encrypted: 'ENCRYPTION_OFF'
      }
    );
    
    await driver.verifyConnectivity();
  });

  afterAll(async () => {
    await driver.close();
  });

  test('should create and verify indexes', async () => {
    await Neo4jSchema.createIndexes(driver);
    
    const session = driver.session();
    try {
      // Neo4j 5+ index verification
      const result = await session.run(
        `SHOW INDEXES 
         WHERE type = 'RANGE' AND entityType = 'NODE' AND labelsOrTypes = [$type]`,
        { type: NodeType.CONTRACT }
      );
      
      expect(result.records.length).toBeGreaterThan(0);
      
      // Log index details for debugging
      console.log('Index verification:', 
        result.records.map(r => r.toObject()));
    } catch (error) {
      console.error('Index verification failed:', error);
      throw error;
    } finally {
      await session.close();
    }
  });

  test('should have all expected node types', () => {
    const expectedNodeTypes = [
      NodeType.CONTRACT,
      NodeType.FUNCTION,
      NodeType.VARIABLE,
      NodeType.PARAMETER,
      NodeType.MODIFIER,
      NodeType.STRUCT,
      NodeType.ERROR,
      NodeType.EVENT,
      NodeType.BLOCK,
      NodeType.IF_STATEMENT,
      NodeType.LOOP,
      NodeType.ASSIGNMENT,
      NodeType.BINARY_OPERATION,
      NodeType.FUNCTION_CALL,
      NodeType.ACCESS_CONTROL,
      NodeType.REENTRANCY,
      NodeType.ARITHMETIC
    ];
    
    expectedNodeTypes.forEach(type => {
      expect(Object.values(NodeType)).toContain(type);
    });
  });

  test('should have all expected relationship types', () => {
    const expectedRelTypes = [
      RelationshipType.CONTAINS,
      RelationshipType.HAS_FUNCTION,
      RelationshipType.HAS_VARIABLE,
      RelationshipType.HAS_PARAMETER,
      RelationshipType.NEXT,
      RelationshipType.BRANCH,
      RelationshipType.READS,
      RelationshipType.WRITES,
      RelationshipType.POTENTIAL_VULNERABILITY,
      RelationshipType.DATA_DEPENDENCY,
      RelationshipType.CONTROL_DEPENDENCY
    ];
    
    expectedRelTypes.forEach(type => {
      expect(Object.values(RelationshipType)).toContain(type);
    });
  });

  test('should maintain backward compatibility with CPGNode label', () => {
    const labels = Neo4jSchema.getNodeLabels(NodeType.FUNCTION);
    expect(labels).toEqual([NodeType.FUNCTION, 'CPGNode']);
  });
});
