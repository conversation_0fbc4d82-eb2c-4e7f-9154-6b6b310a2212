import { Neo4jClient } from '../../db/neo4j';
import { neo4jConfig } from '../neo4j.setup';
import { afterAll, beforeAll, describe, expect, it } from '@jest/globals';

describe('Neo4j Schema Validation', () => {
  let client: Neo4jClient;

  beforeAll(async () => {
    client = new Neo4jClient(neo4jConfig);
    await client.verifyConnection();
    
    // Create test data with all required schema elements
    await client.query(`
      CREATE (c:Contract:CPGNode {id: 'test', name: 'Test', address: '0x0'})
      CREATE (f:Function:CPGNode {id: 'test-fn'})
      CREATE (v:Variable:CPGNode {id: 'test-var'})
      CREATE (m:Modifier:CPGNode {id: 'test-mod'})
      CREATE (e:Expression:CPGNode {id: 'test-expr'})
      CREATE (c)-[:CONTAINS]->(f)
      CREATE (c)-[:INHERITS]->(:Contract)
      CREATE (f)-[:CALLS]->(:Function)
      CREATE (f)-[:REFERENCES]->(v)
    `);
  });

  afterAll(async () => {
    await client?.close();
  });

  it('should have all required node labels', async () => {
    const result = await client.query(`
      MATCH (n)
      UNWIND labels(n) AS label
      RETURN COLLECT(DISTINCT label) AS allLabels
    `);
    
    const allLabels = result.records[0].get('allLabels');
    expect(allLabels).toEqual(expect.arrayContaining([
      'Contract', 'Function', 'Variable', 'Modifier', 'Expression', 'CPGNode'
    ]));
  });

  it('should have all required relationship types', async () => {
    const result = await client.query(`
      MATCH ()-[r]->()
      RETURN DISTINCT type(r) as type
    `);
    
    const types = result.records.map(r => r.get('type'));
    expect(types).toEqual(expect.arrayContaining([
      'CONTAINS', 'INHERITS', 'CALLS', 'REFERENCES'
    ]));
  });

  it('should have required indexes', async () => {
    const result = await client.query(`
      SHOW INDEXES WHERE 
        type = 'RANGE' AND
        entityType = 'NODE' AND
        labelsOrTypes = ['Contract'] AND
        properties = ['name', 'address']
    `);
    
    expect(result.records.length).toBeGreaterThan(0);
  });

  it('should have required constraints', async () => {
    // First create the constraint if it doesn't exist
    try {
      await client.query(`
        CREATE CONSTRAINT contract_id_unique IF NOT EXISTS
        FOR (c:Contract)
        REQUIRE c.id IS UNIQUE
      `);
    } catch (err) {
      // Skip if constraints aren't supported
      return;
    }
    
    const result = await client.query(`
      SHOW CONSTRAINTS WHERE 
        entityType = 'NODE' AND
        labelsOrTypes = ['Contract'] AND
        properties = ['id']
    `);
    
    expect(result.records.length).toBeGreaterThan(0);
  });

  it('should handle variable nodes', async () => {
    // Use a unique identifier for our test data
    const testId = `test-var-${Date.now()}`;
    
    await client.query(`
      CREATE (v:StateVariableDeclaration:CPGNode {id: $testId, name: 'testVar', type: 'uint256'})
      CREATE (c:ContractDefinition:CPGNode {id: 'test-contract2', name: 'TestContract'})
      CREATE (v)-[:CONTAINS]->(c)
    `, { testId });

    const result = await client.query(`
      MATCH (v:StateVariableDeclaration)-[:CONTAINS]->(c:ContractDefinition)
      WHERE v.id = $testId AND c.name = 'TestContract'
      RETURN v, c
    `, { testId });

    expect(result.records.length).toBe(1);
    const variable = result.records[0].get('v').properties;
    expect(variable).toEqual(
      expect.objectContaining({
        name: 'testVar',
        type: 'uint256',
      })
    );
  });
});
