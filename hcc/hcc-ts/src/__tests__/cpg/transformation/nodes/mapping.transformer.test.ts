import { MappingTransformer } from '../../../../cpg/transformation/nodes/mapping.transformer';
import { mockTransformContext } from '../../../transformation/test-utils';
import { compileSolidityContract } from '../../../test-helpers';

describe('MappingTransformer', () => {
  let transformer: MappingTransformer;
  let context: any;
  
  beforeEach(() => {
    transformer = new MappingTransformer();
    context = mockTransformContext(undefined, { nodes: [], edges: [] });
  });

  it('should transform Mapping node', async () => {
    const output = await compileSolidityContract('NestedMappingTestContract');
    const sourceKey = Object.keys(output.sources)[0];
    const ast = output.sources[sourceKey].ast;
    
    const contractNode = ast.nodes.find((n: any) => n.nodeType === 'ContractDefinition');
    if (!contractNode?.nodes) {
      throw new Error('No contract nodes found');
    }
    
    const mappingNode = contractNode.nodes.find(
      (n: any) => n.nodeType === 'VariableDeclaration' && n.typeName?.nodeType === 'Mapping'
    )?.typeName;

    if (!mappingNode) {
      throw new Error('No Mapping node found in test contract');
    }
    
    const parentNode = {
      id: 'test-parent',
      type: 'Contract',
      src: '0:0:0',
      attributes: {}
    };

    const contextWithParent = mockTransformContext(parentNode);

    const result = transformer.transform(mappingNode, contextWithParent);
    
    expect(result).toBeDefined();
    expect(result?.type).toBe('Mapping');
    
    const edges = contextWithParent.graph.edges.filter(
      (e: {source: string; target: string; type: string}) => 
        e.source === result?.id || e.target === result?.id
    );
    
    expect(edges.length).toBeGreaterThan(0);
  });
});
