import { ConstructorTransformer } from '../../../../cpg/transformation/nodes/constructor.transformer';
import { TransformContext } from '../../../../cpg/transformation/core/transformer';
import { compileSolidityContract } from '../../../test-helpers';
import { mockTransformContext } from '../../../transformation/test-utils';

describe('ConstructorTransformer', () => {
  let transformer: ConstructorTransformer;
  let context: TransformContext;
  let constructorNode: any;

  beforeAll(() => {
    const output = compileSolidityContract('FullTestContract');
    const sourceKey = Object.keys(output.sources)[0];
    const ast = output.sources[sourceKey].ast;
    
    if (!ast?.nodes) throw new Error('No AST nodes found');
    
    // Find the contract definition
    const contractDef = ast.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'FullTestContract'
    );
    
    if (!contractDef?.nodes) throw new Error('No nodes found in contract definition');
    
    // Find constructor with detailed error reporting
    constructorNode = contractDef.nodes.find(
      (n: any) => n.nodeType === 'FunctionDefinition' && n.kind === 'constructor'
    );
    
    if (!constructorNode) {
      const availableNodes = contractDef.nodes.map((n: any) => ({
        nodeType: n.nodeType, 
        name: n.name, 
        kind: n.kind
      }));
      throw new Error(`Could not find constructor in AST. Contract nodes: ${JSON.stringify(availableNodes, null, 2)}`);
    }
  });

  beforeEach(() => {
    transformer = new ConstructorTransformer();
    context = mockTransformContext(undefined, { nodes: [], edges: [] });
  });

  it('should transform constructor FunctionDefinition to CPGNode with full validation', () => {
    const result = transformer.transform(constructorNode, context);
    
    expect(result).toBeDefined();
    expect(result?.type).toBe('Constructor');
    
    // Validate required attributes
    expect(result?.attributes?.stateMutability).toBeDefined();
    
    // Verify source properties are preserved in original node
    expect(constructorNode.kind).toBe('constructor');
    expect(constructorNode.visibility).toBe('public');
    
    // If constructor has parameters, verify they're processed
    if (constructorNode.parameters?.length > 0) {
      expect(constructorNode.parameters).toBeDefined();
    }
  });
});
