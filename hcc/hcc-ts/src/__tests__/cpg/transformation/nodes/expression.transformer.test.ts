import { ExpressionTransformer } from '../../../../cpg/transformation/nodes/expression.transformer';
import { parseSolidityToAst } from '../../../../ast/solidityAst';
import { mockTransformContext } from '../../../transformation/test-utils';
import path from 'path';
import { expect } from '@jest/globals';
import { CPGNode } from '../../../../cpg/cpg';

describe('ExpressionTransformer', () => {
  let transformer: ExpressionTransformer;
  let context: any;
  let contractNode: any;

  beforeAll(() => {
    const contractPath = path.join(__dirname, '../../../testdata/ExpressionTestContract.sol');
    const ast = parseSolidityToAst(contractPath);
    contractNode = ast.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'ExpressionTestContract'
    );
  });

  beforeEach(() => {
    transformer = new ExpressionTransformer();
    context = mockTransformContext();
  });

  it('should transform ternary expressions', () => {
    // Find the function containing the ternary expression
    const functionNode = contractNode.nodes.find(
      (n: any) => n.nodeType === 'FunctionDefinition' && n.name === 'testTernary'
    );
    
    // Get the return statement
    const returnStatement = functionNode.body?.statements[0];
    
    // Get the ternary expression (Conditional node)
    const ternaryNode = returnStatement?.expression;
    
    const result = transformer.transform(ternaryNode, context);
    
    expect(result).toBeDefined();
    expect(result?.type).toBe('Conditional');
    expect(result?.src).toBe(ternaryNode.src);
  });
});
