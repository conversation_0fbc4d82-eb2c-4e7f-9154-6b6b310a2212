import { ContractTransformer } from '../../../../cpg/transformation/nodes/contract.transformer';
import { TransformContext } from '../../../../cpg/transformation/core/transformer';
import { compileSolidityContract } from '../../../test-helpers';
import { mockTransformContext } from '../../../transformation/test-utils';

describe('ContractTransformer', () => {
  let transformer: ContractTransformer;
  let context: TransformContext;
  let contractDef: any;

  beforeAll(() => {
    const output = compileSolidityContract('FullTestContract');
    
    // Get the AST from the compiled output
    const sourceKey = Object.keys(output.sources)[0];
    const ast = output.sources[sourceKey].ast;
    
    // Find the contract definition in the root nodes
    contractDef = ast.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'FullTestContract'
    );
    
    if (!contractDef) {
      console.log('AST nodes:', ast.nodes.map((n: any) => `${n.nodeType}:${n.name || 'unnamed'}`));
      throw new Error('Could not find FullTestContract in AST');
    }
  });

  beforeEach(() => {
    // Reset context for each test
    transformer = new ContractTransformer();
    context = mockTransformContext(undefined, { nodes: [], edges: [] });
  });

  afterAll(() => {
    // Clean up any shared resources
    jest.clearAllMocks();
  });

  it('should transform ContractDefinition to CPGNode', () => {
    const result = transformer.transform(contractDef, context);
    expect(result).toBeDefined();
    expect(result?.type).toBe('ContractDefinition');
    expect(result?.attributes?.name).toBe('FullTestContract');
    expect(result?.attributes?.abstract).toBe(false);
  });
});
