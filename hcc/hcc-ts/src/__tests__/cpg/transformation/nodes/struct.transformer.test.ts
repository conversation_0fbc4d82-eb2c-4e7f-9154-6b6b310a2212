import { StructTransformer } from '../../../../cpg/transformation/nodes/struct.transformer';
import { mockTransformContext } from '../../../transformation/test-utils';
import { compileSolidityContract } from '../../../test-helpers';
import { StructDefinition } from '../../../../ast/types';

describe('StructTransformer', () => {
  let transformer: StructTransformer;
  let context: any;
  let structDef: StructDefinition;

  beforeAll(() => {
    const output = compileSolidityContract('StructTestContract');
    // console.log('Full solc output structure:', JSON.stringify(output, null, 2));
    
    // Try to find struct in the AST
    const sourceKey = Object.keys(output.sources)[0];
    const ast = output.sources[sourceKey].ast;
    
    if (!ast?.nodes) {
      throw new Error('No AST nodes found');
    }
    
    // Struct should be in the first contract's nodes
    const contractNode = ast.nodes.find((n: any) => n.nodeType === 'ContractDefinition');
    if (!contractNode?.nodes) {
      throw new Error('No contract nodes found');
    }
    
    structDef = contractNode.nodes.find(
      (n: any) => n.nodeType === 'StructDefinition' && n.name === 'User'
    ) as StructDefinition;
    
    if (!structDef) {
      throw new Error('Could not find User struct in AST');
    }
  });

  beforeEach(() => {
    transformer = new StructTransformer();
    context = mockTransformContext(undefined, { nodes: [], edges: [] });
  });

  it('should transform StructDefinition to CPGNode', () => {
    const result = transformer.transform(structDef, context);
    expect(result).toBeDefined();
    expect(result?.type).toBe('StructDefinition');
    expect(result?.attributes?.name).toBe('User');
    expect(result?.attributes?.members).toBe(3);
  });
});
