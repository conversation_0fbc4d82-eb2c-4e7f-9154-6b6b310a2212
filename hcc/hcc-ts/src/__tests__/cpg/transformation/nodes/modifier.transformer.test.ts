import { ModifierTransformer } from '../../../../cpg/transformation/nodes/modifier.transformer';
import { compileSolidityContract } from '../../../test-helpers';
import { findNodes } from '../../../test-helpers';
import { mockTransformContext } from '../../../transformation/test-utils';
import * as path from 'path';

describe('ModifierTransformer', () => {
  let contractDef: any;
  let transformer: ModifierTransformer;
  let context: any;

  beforeAll(async () => {
    const output = await compileSolidityContract(
      path.join(__dirname, '../../../testdata/FullTestContract.sol')
    );
    
    const sourceKey = Object.keys(output.sources)[0];
    const ast = output.sources[sourceKey].ast;
    
    contractDef = ast.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'FullTestContract'
    );
  });

  beforeEach(() => {
    transformer = new ModifierTransformer();
    context = mockTransformContext(undefined, { nodes: [], edges: [] });
  });

  it('should transform ModifierDefinition to CPGNode', () => {
    const modifiers = findNodes(contractDef, 
      (n) => n.nodeType === 'ModifierDefinition'
    );
    
    expect(modifiers.length).toBeGreaterThan(0);
    
    const result = transformer.transform(modifiers[0], context);
    expect(result).toBeDefined();
    expect(result?.type).toBe('ModifierDefinition');
  });

  it('should find and count all modifiers in contract', () => {
    const modifiers = findNodes(contractDef, 
      (n) => n.nodeType === 'ModifierDefinition'
    );
    
    expect(modifiers.length).toBeGreaterThan(0);
    
    modifiers.forEach(modifier => {
      const result = transformer.transform(modifier, context);
      expect(result).toBeDefined();
    });
  });
});
