import { FunctionTransformer } from '../../../../cpg/transformation/nodes/function.transformer';
import { parseSolidityToAst } from '../../../../ast/solidityAst';
import { mockTransformContext } from '../../../transformation/test-utils';
import path from 'path';
import { expect } from '@jest/globals';
import { CPGNode } from '../../../../cpg/cpg';

describe('FunctionTransformer', () => {
  let transformer: FunctionTransformer;
  let context: any;
  let contractNode: any;

  beforeAll(() => {
    const contractPath = path.join(__dirname, '../../../testdata/FunctionTestContract.sol');
    const ast = parseSolidityToAst(contractPath);
    contractNode = ast.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'FunctionTestContract'
    );
  });

  beforeEach(() => {
    transformer = new FunctionTransformer();
    context = mockTransformContext();
  });

  it('should transform function definitions', () => {
    const functionNode = contractNode.nodes.find(
      (n: any) => n.nodeType === 'FunctionDefinition' && n.name === 'testFunction'
    );
    const result = transformer.transform(functionNode, context);
    
    expect(result).toBeDefined();
    expect(result?.type).toBe('FunctionDefinition');
    expect(result?.src).toBe(functionNode.src);
  });
});
