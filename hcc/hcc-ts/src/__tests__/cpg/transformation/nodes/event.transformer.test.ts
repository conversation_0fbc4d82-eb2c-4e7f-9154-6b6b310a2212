import { EventTransformer } from '../../../../cpg/transformation/nodes/event.transformer';
import { TransformContext } from '../../../../cpg/transformation/core/transformer';
import { compileSolidityContract } from '../../../test-helpers';
import { EventDefinition } from '../../../../ast/types';
import { mockTransformContext } from '../../../transformation/test-utils';

describe('EventTransformer', () => {
  let transformer: EventTransformer;
  let context: TransformContext;
  let eventDef: EventDefinition;

  beforeAll(() => {
    const output = compileSolidityContract('EventTestContract');
    const sourceKey = Object.keys(output.sources)[0];
    const ast = output.sources[sourceKey].ast;
    
    if (!ast?.nodes) {
      throw new Error('No AST nodes found');
    }
    
    // Find the contract definition
    const contractNode = ast.nodes.find((n: any) => n.nodeType === 'ContractDefinition');
    if (!contractNode?.nodes) {
      throw new Error('No contract nodes found');
    }
    
    // Find the Deposit event
    eventDef = contractNode.nodes.find(
      (n: any) => n.nodeType === 'EventDefinition' && n.name === 'Deposit'
    ) as EventDefinition;
    
    if (!eventDef) {
      throw new Error('Could not find Deposit event in AST');
    }
  });

  beforeEach(() => {
    transformer = new EventTransformer();
    context = mockTransformContext(undefined, { nodes: [], edges: [] });
  });

  it('should transform EventDefinition to CPGNode', () => {
    const result = transformer.transform(eventDef, context);
    expect(result).toBeDefined();
    expect(result?.type).toBe('EventDefinition');
    expect(result?.attributes?.name).toBe('Deposit');
    expect(result?.attributes?.parameters).toBe(3);
    expect(result?.attributes?.anonymous).toBe(false);
  });
});
