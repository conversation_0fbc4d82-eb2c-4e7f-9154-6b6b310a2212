import { InheritanceTransformer } from '../../../../cpg/transformation/nodes/inheritance.transformer';
import { compileSolidityContract } from '../../../test-helpers';
import { mockTransformContext } from '../../../transformation/test-utils';
import { RelationshipType, CPGNode, CPCEdge } from '../../../../cpg';

describe('InheritanceTransformer', () => {
  let transformer: InheritanceTransformer;
  let context: any;
  
  beforeAll(() => {
    compileSolidityContract('InheritanceTestContract');
  });

  beforeEach(() => {
    transformer = new InheritanceTransformer();
    context = mockTransformContext(undefined, { nodes: [], edges: [] });
  });

  it('should create INHERITS edges for all base contracts', () => {
    const output = compileSolidityContract('InheritanceTestContract');
    const sourceKey = Object.keys(output.sources)[0];
    const ast = output.sources[sourceKey].ast;
    
    // First create all contract nodes
    const contracts = ast.nodes?.filter(
      (n: any) => n.nodeType === 'ContractDefinition'
    ) || [];
    
    contracts.forEach((contract: any) => {
      transformer.transform(contract, context);
    });
    
    // Process inheritance relationships
    transformer.processInheritanceRelationships(ast, context);
    
    // Verify all contracts exist in graph
    ['A', 'B', 'C', 'D'].forEach(name => {
      expect(context.graph.nodes).toContainEqual(
        expect.objectContaining({
          attributes: expect.objectContaining({ name }),
          type: 'ContractDefinition'
        })
      );
    });
    
    // Verify inheritance edges
    const inheritsEdges = context.graph.edges.filter(
      (e: CPCEdge) => e.type === RelationshipType.INHERITS
    );
    
    expect(inheritsEdges.length).toBe(4);
    
    // Helper to get node ID by name
    const getIdByName = (name: string) => {
      return context.graph.nodes.find(
        (n: CPGNode) => n.attributes?.name === name
      )?.id;
    };
    
    // Verify specific edges exist
    expect(inheritsEdges).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          source: getIdByName('A'),
          target: getIdByName('B')
        }),
        expect.objectContaining({
          source: getIdByName('A'),
          target: getIdByName('C')
        }),
        expect.objectContaining({
          source: getIdByName('B'),
          target: getIdByName('D')
        }),
        expect.objectContaining({
          source: getIdByName('C'),
          target: getIdByName('D')
        })
      ])
    );
  });
});
