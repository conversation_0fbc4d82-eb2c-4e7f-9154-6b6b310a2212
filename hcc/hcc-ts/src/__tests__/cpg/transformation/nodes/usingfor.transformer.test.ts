import { UsingForTransformer } from '../../../../cpg/transformation/nodes/usingfor.transformer';
import { parseSolidityToAst } from '../../../../ast/solidityAst';
import { mockTransformContext } from '../../../transformation/test-utils';
import path from 'path';
import { expect } from '@jest/globals';
import { CPGNode } from '../../../../cpg/cpg';

describe('UsingForTransformer', () => {
  let transformer: UsingForTransformer;
  let context: any;
  let contractNode: any;

  beforeAll(() => {
    const contractPath = path.join(__dirname, '../../../testdata/UsingForTestContract.sol');
    const ast = parseSolidityToAst(contractPath);
    contractNode = ast.nodes.find(
      (n: any) => n.nodeType === 'ContractDefinition' && n.name === 'UsingForTestContract'
    );
  });

  beforeEach(() => {
    transformer = new UsingForTransformer();
    context = mockTransformContext();
  });

  it('should transform usingFor directives', () => {
    const usingForNode = contractNode.nodes.find(
      (n: any) => n.nodeType === 'UsingForDirective'
    );
    const result = transformer.transform(usingForNode, context);
    
    expect(result).toBeDefined();
    expect(result?.type).toBe('UsingForDirective');
    expect(result?.src).toBe(usingForNode.src);
  });
});
