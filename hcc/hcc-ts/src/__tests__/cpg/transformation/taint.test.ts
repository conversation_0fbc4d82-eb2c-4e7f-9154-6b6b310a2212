import { BaseTransformer } from '../../../cpg/transformation/core/base-transformer';
import { CPGNode, CPGGraph } from '../../../cpg';
import { TransformContext } from '../../../cpg/transformation/core/transformer';

describe('Taint Propagation', () => {
  class TestTransformer extends BaseTransformer {
    canTransform = () => true;
    transform = () => undefined;
    
    // Expose protected methods for testing
    testPropagateTaint = this.propagateTaint.bind(this);
    testMarkAsTainted = this.markAsTainted.bind(this);
  }
  
  let transformer: TestTransformer;
  let graph: CPGGraph;
  
  beforeEach(() => {
    graph = {
      nodes: [],
      edges: [],
      addNode: jest.fn(),
      addEdge: jest.fn(),
      getNode: jest.fn(),
      getNodeChildren: jest.fn()
    };
    transformer = new TestTransformer(graph);
  });

  it('should propagate taint between nodes', () => {
    const source: CPGNode = {
      id: 'source',
      type: 'Variable',
      src: '0:0:0',
      attributes: { 
        taintStatus: 'tainted', 
        taintSources: [] 
      }
    };
    
    const target: CPGNode = {
      id: 'target',  
      type: 'Variable',
      src: '0:0:0',
      attributes: { 
        taintStatus: 'untainted', 
        taintSources: [] 
      }
    };
    
    const context: TransformContext = {
      graph,
      parent: source,
      transformChild: jest.fn(),
      generateId: jest.fn()
    };
    
    transformer.testPropagateTaint(source, target, context);
    
    expect(target.attributes.taintStatus).toBe('tainted');
    expect(target.attributes.taintSources).toContain('source');
    expect(graph.addEdge).toHaveBeenCalledWith(
      expect.objectContaining({
        source: 'source',
        target: 'target',
        type: 'TAINT_FLOW'
      })
    );
  });
});
