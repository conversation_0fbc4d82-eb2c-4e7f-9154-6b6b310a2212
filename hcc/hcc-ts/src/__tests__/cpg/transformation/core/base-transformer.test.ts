import { BaseTransformer } from '../../../../cpg/transformation/core/base-transformer';
import { ASTNode } from '../../../../ast/types';

describe('BaseTransformer', () => {
  class TestTransformer extends BaseTransformer {
    canTransform(node: ASTNode): boolean { return true; }
    transform(node: ASTNode): any { return null; }
    public testGenerateId(node: ASTNode): string {
      return this.generateId(node);
    }
  }

  const transformer = new TestTransformer();

  it('should generate IDs using nodeType and src position', () => {
    const node = { nodeType: 'TestNode', src: '100:20:0' } as ASTNode;
    expect(transformer.testGenerateId(node)).toBe('TestNode_100');
  });

  it('should throw when src is missing', () => {
    const node = { nodeType: 'TestNode' } as ASTNode;
    expect(() => transformer.testGenerateId(node)).toThrow('Missing src property for TestNode node');
  });

  it('should throw when src is invalid', () => {
    const node = { nodeType: 'TestNode', src: 'invalid' } as ASTNode;
    expect(() => transformer.testGenerateId(node)).toThrow('Invalid src format for TestNode node: invalid');
  });
});
