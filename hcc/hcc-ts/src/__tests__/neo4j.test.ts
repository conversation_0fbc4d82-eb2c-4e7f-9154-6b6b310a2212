import { Neo4jClient } from '../db/neo4j';
import { neo4jConfig } from './neo4j.setup';
import { afterAll, beforeAll, describe, expect, it } from '@jest/globals';
import dotenv from "dotenv"

dotenv.config({ path: ".env.test" })

describe('Neo4j Tests', () => {
  let client: Neo4jClient;

  beforeAll(async () => {
    client = new Neo4jClient(neo4jConfig);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for connection
  });

  afterAll(async () => {
    await client?.close();
  });

  it('should connect to Neo4j', async () => {
    expect(client).toBeDefined();
    await expect(client.verifyConnection()).resolves.toBe(true);
  });
});
