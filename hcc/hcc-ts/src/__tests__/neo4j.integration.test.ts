// Module-level reference for cleanup
let neo4j: Neo4jClient | null = null;

// <PERSON>le process exit for CI environments
process.on('exit', () => {
  if (neo4j && neo4j.isConnected()) {
    neo4j.close().catch(() => {});
  }
});

import { Neo4jClient } from '../db/neo4j';
import { afterAll, beforeAll, describe, expect, it } from '@jest/globals';
import { createTestCPG, getTestNeo4jConfig, TestNeo4jConfig } from './neo4j-test-helpers';

describe('Neo4j Integration Tests', () => {
  let testConfig: TestNeo4jConfig;

  beforeAll(async () => {
    testConfig = getTestNeo4jConfig();
    neo4j = new Neo4jClient(testConfig);
    await neo4j.connect();
  }, 15000);

  afterAll(async () => {
    if (neo4j) {
      // Clean up all test data from this run
      const session = neo4j.getSession();
      try {
        await session.run('MATCH (n) WHERE n.testRunId = $testRunId DETACH DELETE n', {
          testRunId: testConfig.testRunId
        });
        await session.run('MATCH ()-[r]->() WHERE r.testRunId = $testRunId DELETE r', {
          testRunId: testConfig.testRunId
        });
      } finally {
        await session.close();
      }
      
      // Force driver to close all connections
      await neo4j.close();
      
      // Add cleanup verification
      const driver = (neo4j as any).driver;
      if (driver) {
        const pool = driver._connectionProvider?._connectionPool;
        if (pool && pool._activeConnections > 0) {
          await new Promise(resolve => pool.on('empty', resolve));
        }
      }
    }
  }, 60000); // 60 second timeout

  beforeEach(async () => {
    if (!neo4j?.isConnected()) return;
    // Clean up any remaining test data before each test
    const session = neo4j.getSession();
    try {
      await session.run('MATCH (n) WHERE n.testRunId = $testRunId DETACH DELETE n', {
        testRunId: testConfig.testRunId
      });
    } finally {
      await session.close();
    }
  });

  it('should import CPG data', async () => {
    if (!neo4j || !neo4j.isConnected()) {
      console.warn('Neo4j import test - client not available');
      return;
    }

    const cpg = createTestCPG(testConfig.testRunId);
    console.log('Test CPG edges:', cpg.edges); // Debug logging
    await neo4j.importCPG(cpg);

    // Verify nodes were imported
    const nodeResult = await neo4j.query(
      'MATCH (n) WHERE n.testRunId = $testRunId RETURN count(n) as count',
      { testRunId: testConfig.testRunId }
    );
    console.log('Imported nodes:', nodeResult.records[0].get('count').toNumber()); // Debug logging

    // Verify edges were imported
    const edgeResult = await neo4j.query(
      'MATCH (n)-[r]->(m) WHERE r.testRunId = $testRunId AND n.testRunId = $testRunId AND m.testRunId = $testRunId RETURN count(r) as count',
      { testRunId: testConfig.testRunId }
    );
    console.log('Imported edges:', edgeResult.records[0].get('count').toNumber()); // Debug logging
    expect(edgeResult.records[0].get('count').toNumber()).toBe(1);
  });

  it('should maintain stable connection', async () => {
    if (!neo4j || !neo4j.isConnected()) {
      return;
    }
    // Test connection persistence
    await expect(neo4j.verifyConnection()).resolves.toBe(true);

    // Add small delay to simulate real usage
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Verify connection still works
    await expect(neo4j.verifyConnection()).resolves.toBe(true);
  }, 10000); // 10s timeout

  it('should handle connection drops', async () => {
    if (!neo4j || !neo4j.isConnected()) return;

    // Verify initial connection
    expect(await neo4j.verifyConnection()).toBe(true);

    // Forcefully close connection
    await neo4j.disconnect();

    // Verify reconnection with retries
    let reconnected = false;
    for (let i = 0; i < 3; i++) {
      reconnected = await neo4j.verifyConnection();
      if (reconnected) break;
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    expect(reconnected).toBe(true);
  }, 15000);

  it('should execute complex queries', async () => {
    if (!neo4j || !neo4j.isConnected()) {
      console.warn('Neo4j complex query test - client not available');
      return;
    }

    const result = await neo4j.query('MATCH (n) RETURN count(n) as count', undefined);
    expect(result.records[0].get('count')).toBeDefined();
  });
});
