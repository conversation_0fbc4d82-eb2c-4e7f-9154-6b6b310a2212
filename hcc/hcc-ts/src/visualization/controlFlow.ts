import { Result } from 'neo4j-driver';
import { Neo4jClient } from '../db/neo4j';

type PathSegment = {
  start: { properties: { id: string; type?: string; name?: string } };
  end: { properties: { id: string; type?: string; name?: string } };
  relationship: { type: string; properties: Record<string, any>; start: string; end: string };
};

type Neo4jRecord = {
  get: (key: string) => any;
};

export class ControlFlowVisualizer {
  constructor(private neo4j: Neo4jClient) {}

  /**
   * Generates DOT format graph for visualization
   */
  async generateDotGraph(functionId?: string): Promise<string> {
    const result = await this.neo4j.query(this.getControlFlowQuery(functionId), { id: functionId });
    
    let dot = 'digraph ControlFlow {\n';
    dot += '  node [shape=box, fontname="Arial"];\n';
    dot += '  edge [fontname="Arial"];\n\n';
    
    const nodes = new Set<string>();
    const edges = new Set<string>();
    
    result.records.forEach((record: Neo4jRecord) => {
      const path = record.get('path');
      path.segments.forEach((segment: PathSegment) => {
        // Add nodes
        nodes.add(`  "${segment.start.properties.id}" [label="${this.getNodeLabel(segment.start)}"];\n`);
        nodes.add(`  "${segment.end.properties.id}" [label="${this.getNodeLabel(segment.end)}"];\n`);
        
        // Add edge
        const edgeLabel = segment.relationship.type;
        edges.add(`  "${segment.start.properties.id}" -> "${segment.end.properties.id}" ` + 
                 `[label="${edgeLabel}", ${this.getEdgeStyle(segment.relationship)}];\n`);
      });
    });
    
    dot += Array.from(nodes).join('');
    dot += '\n';
    dot += Array.from(edges).join('');
    dot += '}\n';
    
    return dot;
  }
  
  private getControlFlowQuery(functionId?: string): string {
    return functionId 
      ? `MATCH path=(start:FunctionEntry {id: $id})-[*]->(end)
         WHERE end:FunctionExit OR NOT (end)-[:NEXT]->()
         RETURN path`
      : `MATCH path=(start:ControlFlowNode)-[*]->(end)
         RETURN path LIMIT 100`;
  }
  
  private getNodeLabel(node: { properties: { id: string; type?: string; name?: string } }): string {
    const props = node.properties;
    let label = props.type || 'Node';
    if (props.name) label += `\\n${props.name}`;
    return label;
  }
  
  private getEdgeStyle(rel: { type: string; properties: Record<string, any>; start: string; end: string }): string {
    const style: Record<string, string> = {
      'NEXT': 'color=black',
      'TRUE_BRANCH': 'color=green',
      'FALSE_BRANCH': 'color=red', 
      'CALLS': 'color=blue,style=dashed'
    };
    return style[rel.type] || 'color=gray';
  }
}
