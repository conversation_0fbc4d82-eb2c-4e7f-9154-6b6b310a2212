import * as neo4j from 'neo4j-driver';
import logger from "../logger/logger"
import { CPGGraph, CPGNode, CPCEdge, CPCEdgeType } from "../cpg/cpg"

export interface Neo4jConfig {
  uri: string
  user: string
  password: string
  encrypted?: boolean
  trust?: 'TRUST_ALL_CERTIFICATES'
  database?: string
}

interface Neo4jNode {
  id: string;
  props: Record<string, any>;
}

interface Neo4jEdge {
  from: string;
  to: string;
  props: Record<string, any>;
}

interface Neo4jQueryResult {
  records: {
    get: (key: string) => any;
  }[];
}

export interface Neo4jClientInterface {
  isConnected(): boolean;
  query(query: string, params?: Record<string, unknown>): Promise<Neo4jQueryResult>;
  importCPG(cpg: CPGGraph, database?: string): Promise<void>;
  close(): Promise<void>;
  getSession(database?: string): import('neo4j-driver').Session;
  connect(database?: string): Promise<void>;
}

export class Neo4jClient implements Neo4jClientInterface {
  private driver: neo4j.Driver | null = null;
  private connected = false;
  private authAttempts = 0;

  constructor(private config: Neo4jConfig) {
    this.connect();
  }

  async close(): Promise<void> {
    if (this.driver) {
      await this.driver.close();
    }
  }

  async connect(database?: string): Promise<void> {
    const testConfig = {
      ...this.config,
      database: database || this.config.database
    };

    this.driver = neo4j.driver(
      testConfig.uri,
      neo4j.auth.basic(testConfig.user, testConfig.password),
      {
        maxConnectionPoolSize: 10,
        connectionTimeout: 30000,
        maxTransactionRetryTime: 30000,
        connectionAcquisitionTimeout: 60000
      }
    );
    await this.verifyConnection(database);
  }

  async verifyConnection(database?: string): Promise<boolean> {
    try {
      if (!this.driver) {
        await this.connect(database);
        return true;
      }

      const session = this.driver.session({
        database: database || this.config.database,
        defaultAccessMode: neo4j.session.READ
      });
      try {
        await session.run("RETURN 1");
        this.connected = true;
        return true;
      } finally {
        await session.close();
      }
    } catch (error) {
      this.connected = false;
      // Attempt reconnection
      await this.connect(database);
      return this.connected;
    }
  }

  isConnected(): boolean {
    return this.connected;
  }

  async disconnect(): Promise<void> {
    if (this.driver) {
      await this.driver.close();
    }
    this.connected = false;
  }

  async importCPG(graph: CPGGraph, database?: string): Promise<void> {
    if (!this.driver) {
      throw new Error('Neo4j driver not initialized');
    }

    const session = this.driver.session({
      database: database || this.config.database,
      defaultAccessMode: neo4j.session.WRITE
    });
    try {
      // Prepare nodes with flattened properties
      const nodes = graph.nodes.map(node => ({
        id: node.id,
        type: node.type,
        src: node.src,
        ...node.attributes // Flatten attributes into top-level properties
      }));

      await session.writeTransaction(async (tx) => {
        // Import nodes
        await tx.run(
          `UNWIND $nodes AS node
           MERGE (n:CPGNode {id: node.id})
           SET n += node
           SET n:${nodes[0]?.type || 'CPGNode'}`,
          { nodes }
        );

        // Import edges
        await tx.run(
          `UNWIND $edges AS edge
           MATCH (a {id: edge.source})
           MATCH (b {id: edge.target})
           MERGE (a)-[r:${graph.edges.map(e => e.type).join('|')}]->(b)
           SET r += edge.properties`,
          { edges: graph.edges }
        );
      });

      logger.info(`Successfully imported ${graph.nodes.length} nodes and ${graph.edges.length} edges`);
    } catch (error) {
      logger.error('Neo4j import failed:', error);
      throw error;
    } finally {
      await session.close();
    }
  }
  
  private async executeWithRetry(
    operation: () => Promise<void>,
    maxRetries: number,
    delayMs: number
  ): Promise<void> {
    let attempt = 0;
    while (attempt <= maxRetries) {
      try {
        await operation();
        return;
      } catch (error) {
        attempt++;
        if (attempt > maxRetries) {
          throw error;
        }
        console.log(`Retrying operation (attempt ${attempt}/${maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }
  }

  async query(cypher: string, params?: Record<string, any>, database?: string): Promise<Neo4jQueryResult> {
    if (!this.driver) {
      throw new Error('Neo4j driver not initialized');
    }
    
    const session = this.driver.session({
      database: database || this.config.database,
      defaultAccessMode: neo4j.session.READ
    });
    try {
      const isWrite = cypher.toLowerCase().includes('create') || 
                     cypher.toLowerCase().includes('merge') ||
                     cypher.toLowerCase().includes('delete') ||
                     cypher.toLowerCase().includes('set');
      
      return isWrite 
        ? await session.writeTransaction(tx => tx.run(cypher, params))
        : await session.readTransaction(tx => tx.run(cypher, params));
    } finally {
      await session.close();
    }
  }

  getSession(database?: string): import('neo4j-driver').Session {
    if (!this.driver) {
      throw new Error('Neo4j driver not initialized');
    }
    return this.driver.session({
      database: database || this.config.database
    });
  }
}
