import neo4j from 'neo4j-driver';

export async function checkNeo4jHealth(uri: string, user: string, password: string): Promise<boolean> {
  const driver = neo4j.driver(uri, neo4j.auth.basic(user, password));
  try {
    const session = driver.session();
    const result = await session.run('RETURN 1 AS db_ready');
    await session.close();
    await driver.close();
    return result.records.length === 1;
  } catch (err) {
    await driver.close();
    return false;
  }
}
