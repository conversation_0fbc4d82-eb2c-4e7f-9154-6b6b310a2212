version: "3.8"
services:
  neo4j:
    image: neo4j:5.19-community
    container_name: hcc-neo4j-db
    environment:
      - NEO4J_AUTH=neo4j/new_secure_password
      - NEO4J_ACCEPT_LICENSE_AGREEMENT=yes
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_connector_bolt_advertised__address=localhost:7687
      - NEO4J_dbms_connector_bolt_tls__level=DISABLED
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*

    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "new_secure_password", "RETURN 1"]
      interval: 10s
      timeout: 10s
      retries: 5
volumes:
  neo4j_data:
