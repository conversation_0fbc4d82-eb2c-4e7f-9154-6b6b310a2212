# Hardening Contract Compiler (TypeScript)

A TypeScript-based toolchain for Solidity contract analysis, CPG generation, and vulnerability detection using Neo4j.

## Features
- Parses Solidity contracts (0.8.0+), builds AST and CPG
- Stores CPG in Neo4j (with browser access)
- Runs Cypher queries for vulnerability detection
- Modular, maintainable codebase

## Quick Start

1. Install dependencies:
   ```bash
   npm install
   ```
2. Start Neo4j:
   ```bash
   docker-compose up -d
   ```
3. Build and run the CPG pipeline:
   ```bash
   npm run build && npm start
   ```
4. Access Neo4j Browser at http://localhost:7474 (user: neo4j, pass: test)

## Query Runner Usage

Run all queries and print results:
```bash
npx ts-node src/runner/runQueries.ts
```

### Flags and Options
- `--format [json|markdown|text]`   Output format for results (default: text)
- `--tag <tag>`                     Only run queries with the specified tag (from their `.meta.json`)
- `--output <file>`                 Write formatted results to a file
- `--concurrency <N>`               Number of queries to run in parallel (default: 8)
- `--check-db`                      Only check if Neo4j is healthy and ready, then exit (returns 0 if healthy, 1 if not)

**Examples:**
```bash
npx ts-node src/runner/runQueries.ts --format markdown --tag demo --output results.md --concurrency 16
npx ts-node src/runner/runQueries.ts --format json
npx ts-node src/runner/runQueries.ts --tag reentrancy --concurrency 4
```

## Adding Queries
1. Place your Cypher query in `src/queries/yourquery.cypher`.
2. (Optional) Add metadata in `src/queries/yourquery.meta.json`:
   ```json
   {
     "description": "Detects reentrancy patterns.",
     "severity": "high",
     "tags": ["reentrancy", "security"]
   }
   ```
3. The runner will automatically discover and run all `.cypher` files.

## Query Metadata
- **description**: Human-readable explanation of the query
- **severity**: Info, low, medium, high, critical
- **tags**: Array of tags for filtering (e.g., "math", "reentrancy", "demo")

## Developer Best Practices
- Add new queries as `.cypher` files, with `.meta.json` metadata for documentation and filtering
- Use tags to organize queries by vulnerability type or feature
- Use the `--concurrency` flag to speed up large query sets
- Use the `--output` flag to save results for CI or dashboards
- Extend the result formatter for custom reporting needs

## Structure
- `contracts/` - Sample Solidity contracts
- `src/ast/` - AST parsing and logic
- `src/cpg/` - CPG construction and schema
- `src/db/` - Neo4j integration
- `src/queries/` - Cypher queries and metadata
- `src/runner/` - Query runner, formatter, CLI
- `src/logger/` - Logging setup

## Why Slither?

We use [Slither](https://github.com/crytic/slither) as our Solidity parser for several key reasons:

1. **Accurate AST Generation**:
   - Handles all modern Solidity features (0.8.0+)
   - Correctly processes complex syntax (inheritance, modifiers, etc.)
   - Maintains semantic relationships between nodes

2. **Built-in Analysis**:
   - Provides taint analysis capabilities
   - Includes vulnerability detection patterns we can leverage
   - Offers call graph and data dependency analysis

3. **Maintenance Benefits**:
   - Actively maintained by Trail of Bits
   - Keeps pace with Solidity compiler updates
   - Reduces our maintenance burden vs a custom parser

4. **Performance**:
   - Written in optimized Python (fast parsing)
   - Battle-tested on large codebases
   - Parallel analysis capabilities

Slither serves as the foundation for our CPG generation, ensuring we start with a complete and accurate representation of the contract's structure and semantics.

## Visualization

Generate control flow graphs in DOT format:

```typescript
import { Neo4jClient } from './src/db/neo4j';
import { ControlFlowVisualizer } from './src/visualization/controlFlow';

const neo4j = new Neo4jClient();
const visualizer = new ControlFlowVisualizer(neo4j);

// Generate DOT graph for a specific function
const dotGraph = await visualizer.generateDotGraph('function-id-123');
console.log(dotGraph);

// Render using Graphviz (requires graphviz installed)
// $ dot -Tpng graph.dot -o graph.png
```

## Example Neo4j Queries

### Control Flow Analysis

1. Get all paths through a function:
```cypher
MATCH path=(start:FunctionEntry {id: $functionId})-[:NEXT|TRUE_BRANCH|FALSE_BRANCH*]->(end)
WHERE end:FunctionExit OR NOT (end)-[:NEXT]->()
RETURN path
```

2. Find all loops in a contract:
```cypher
MATCH (loop:ControlFlowNode {controlFlowType: 'loop'})-[:NEXT]->(body)
RETURN loop, body
```

3. Find unreachable code:
```cypher
MATCH (node:ControlFlowNode)
WHERE NOT (:ControlFlowNode)-[:NEXT|TRUE_BRANCH|FALSE_BRANCH]->(node)
AND node.type <> 'FunctionEntry'
RETURN node
```

### Data Flow Analysis

1. Find variable declarations and their uses:
```cypher
MATCH (decl:VariableDeclaration)-[:HAS_DECLARATION]->(var),
      (var)<-[:REFERENCES]-(usage)
RETURN decl, var, usage
```

2. Track data flow through function calls:
```cypher
MATCH path=(start:Variable)-[:REFERENCES|CALLS*1..5]->(end)
WHERE start.name = $varName
RETURN path
```

3. Find uninitialized variables:
```cypher
MATCH (var:Variable)
WHERE NOT (var)-[:HAS_INITIAL_VALUE]->()
RETURN var
```

4. Detect potential data races:
```cypher
MATCH (var:Variable)<-[:REFERENCES]-(write1),
      (var)<-[:REFERENCES]-(write2)
WHERE write1 <> write2
AND NOT (write1)-[:NEXT*]->(write2)
AND NOT (write2)-[:NEXT*]->(write1)
RETURN var, write1, write2
```

5. Find tainted data flows:
```cypher
MATCH (source:Variable {tainted: true}),
      path=shortestPath((source)-[:REFERENCES|CALLS*1..10]->(sink))
WHERE sink.type IN ['ExternalCall', 'StorageAccess']
RETURN path
```

## Foundry Integration

This project uses [Foundry](https://book.getfoundry.sh/) for contract flattening. You'll need to install Foundry to use this feature.

### Installation
```bash
npm run foundry:install
# or follow manual instructions at: https://book.getfoundry.sh/getting-started/installation
```

### Available Commands
- `npm run flatten`: Flatten test contracts
- `npm run foundry:update`: Update Foundry dependencies

### Usage Example
```typescript
import { flattenContract } from './services/flattening.service';

const flattened = flattenContract('path/to/contracts');
```

## Neo4j Docker Setup

This project uses a Dockerized Neo4j instance with APOC plugin enabled and persistent data storage.

### Start Neo4j
```bash
docker-compose up -d
```
- Neo4j Browser: [http://localhost:7474](http://localhost:7474)
- Username: `neo4j`  Password: `password`

### Stop Neo4j
```bash
docker-compose down
```

### Reset Database (remove all data)
```bash
docker-compose down -v
```

### Configuration
- Custom config: `neo4j.conf` is mounted for advanced settings (APOC, memory, security, etc)
- Data persists in Docker volume `neo4j_data`
- APOC plugin is pre-installed

### Verify Database is Running
After startup, visit the Neo4j browser or run a simple test query:

1. Open [http://localhost:7474](http://localhost:7474)
2. Login with the credentials above
3. Run:
   ```cypher
   RETURN 1 AS db_ready
   ```
   or
   ```cypher
   CALL dbms.components() YIELD name, versions, edition RETURN name, versions, edition;
   ```
   If you get a result, Neo4j is ready.

---

## License
MIT
