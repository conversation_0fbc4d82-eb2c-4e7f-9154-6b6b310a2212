/**
 * transports.js: Set of all transports <PERSON> knows about.
 *
 * (C) 2010 <PERSON>
 * MIT LICENCE
 */

'use strict';

/**
 * TODO: add property description.
 * @type {Console}
 */
Object.defineProperty(exports, 'Console', {
  configurable: true,
  enumerable: true,
  get() {
    return require('./console');
  }
});

/**
 * TODO: add property description.
 * @type {File}
 */
Object.defineProperty(exports, 'File', {
  configurable: true,
  enumerable: true,
  get() {
    return require('./file');
  }
});

/**
 * TODO: add property description.
 * @type {Http}
 */
Object.defineProperty(exports, 'Http', {
  configurable: true,
  enumerable: true,
  get() {
    return require('./http');
  }
});

/**
 * TODO: add property description.
 * @type {Stream}
 */
Object.defineProperty(exports, 'Stream', {
  configurable: true,
  enumerable: true,
  get() {
    return require('./stream');
  }
});
