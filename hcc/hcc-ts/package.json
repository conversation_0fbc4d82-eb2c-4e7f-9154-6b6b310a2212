{"name": "hcc-ts", "version": "0.1.0", "description": "Hardening Contract Compiler in TypeScript", "main": "dist/index.js", "scripts": {"start": "ts-node src/index.ts", "analyze": "node src/runner/cli.ts query", "visualize": "node src/runner/cli.ts visualize", "build": "tsc && cp -R src/__tests__/testdata dist/__tests__/", "test": "npm run build && jest", "dev": "node src/index.ts", "test-query": "node src/__tests__/simple-query.ts", "test:slither": "npm run build && node scripts/test-runners/slither.js", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write .", "copy-test-files": "cp -r src/__tests__/testdata dist/__tests__/ && cp -r contracts dist/", "flatten": "node scripts/flatten-folder.js contracts && echo 'Flattened output saved to contracts/output/flattened.sol'", "foundry:install": "echo 'Please install Foundry: https://book.getfoundry.sh/getting-started/installation' && curl -L https://foundry.paradigm.xyz | bash", "foundry:update": "forge update"}, "dependencies": {"solc": "^0.8.21", "winston": "^3.10.0", "yargs": "^17.7.2"}, "devDependencies": {"@jest/globals": "^30.0.0-beta.3", "@types/jest": "^29.5.14", "@types/minimist": "^1.2.5", "@types/node": "^22.15.29", "@types/yargs": "^17.0.24", "dotenv": "^16.4.1", "jest": "^29.7.0", "minimist": "^1.2.8", "neo4j-driver": "^5.28.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}