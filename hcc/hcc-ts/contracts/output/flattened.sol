// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

// hcc-ts/contracts/Meh.sol

contract Meh {
    uint public valueXYZ;
}

// hcc-ts/contracts/TestContract.sol

contract TestContract {
    uint public value;
    
    function setValue(uint _value) public {
        value = _value;
    }
    
    function getValue() public view returns (uint) {
        return value;
    }
}

// hcc-ts/contracts/Wrapper.sol

contract Wrapper {}

