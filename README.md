# Sol Contract 2 Graph DB

A TypeScript application that analyzes Solidity smart contracts using Slither and stores the analysis results in a Neo4j graph database.

## Prerequisites

- Node.js 18 or higher
- Neo4j database instance
- Slither static analysis tool

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy the environment configuration:
   ```bash
   cp .env.example .env
   ```

4. Configure your Neo4j connection and other settings in `.env`

## Scripts

- `npm run dev` - Run the application in development mode
- `npm run build` - Build the TypeScript project
- `npm test` - Run tests with Jest
- `npm run lint` - Lint the codebase with ESLint
- `npm run format` - Format code with Prettier

## Project Structure

```
├── src/
│   └── index.ts          # Main application entry point
├── dist/                 # Compiled JavaScript output
├── coverage/             # Test coverage reports
├── .env.example          # Environment variables template
├── tsconfig.json         # TypeScript configuration
├── jest.config.js        # Jest testing configuration
├── .eslintrc.js          # ESLint configuration
├── .prettierrc.js        # Prettier configuration
└── package.json          # Project dependencies and scripts
```

## Development

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Run tests:
   ```bash
   npm test
   ```

3. Lint and format code:
   ```bash
   npm run lint
   npm run format
   ```

## Features (Planned)

- [ ] Slither API wrapper integration
- [ ] Neo4j graph database connectivity
- [ ] Smart contract analysis pipeline
- [ ] Graph-based vulnerability detection
- [ ] Analysis result visualization

## License

MIT
