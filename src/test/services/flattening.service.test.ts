import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { existsSync, mkdirSync, rmSync } from 'fs';
import path from 'path';
import { FlatteningService } from '../../services/flattening.service';

describe('FlatteningService', () => {
  const testOutputDir = path.join(__dirname, '../test-output');

  beforeEach(() => {
    // Create test output directory
    if (!existsSync(testOutputDir)) {
      mkdirSync(testOutputDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up test output directory
    if (existsSync(testOutputDir)) {
      rmSync(testOutputDir, { recursive: true, force: true });
    }
  });

  describe('flattenFolder', () => {
    it('should fail with descriptive error when folder does not exist', async () => {
      const nonExistentDir = path.join(__dirname, 'non-existent');

      const result = await FlatteningService.flattenFolder(
        nonExistentDir,
        testOutputDir
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Folder does not exist');
    });

    it('should fail with descriptive error when Foundry is not installed', async () => {
      const contractsDir = path.join(__dirname, '../test-contracts');

      // Mock exec to simulate Foundry not being available
      const originalExec = require('child_process').exec;
      const mockExec = jest.fn((command, options, callback) => {
        if (command.includes('forge --version')) {
          callback(
            new Error('forge: command not found'),
            '',
            'forge: command not found'
          );
        } else {
          originalExec(command, options, callback);
        }
      });

      require('child_process').exec = mockExec;

      try {
        const result = await FlatteningService.flattenFolder(
          contractsDir,
          testOutputDir
        );

        expect(result.success).toBe(false);
        expect(result.error).toContain('Foundry (forge) is not installed');
        expect(result.error).toContain('https://book.getfoundry.sh');
      } finally {
        // Restore original exec
        require('child_process').exec = originalExec;
      }
    });

    it('should create output directory if it does not exist', async () => {
      const contractsDir = path.join(__dirname, '../test-contracts');
      const newOutputDir = path.join(testOutputDir, 'new-output');

      // Ensure the directory doesn't exist
      expect(existsSync(newOutputDir)).toBe(false);

      await FlatteningService.flattenFolder(contractsDir, newOutputDir);

      // Directory should be created regardless of whether flattening succeeds
      expect(existsSync(newOutputDir)).toBe(true);

      // Clean up
      if (existsSync(newOutputDir)) {
        rmSync(newOutputDir, { recursive: true, force: true });
      }
    });

    it('should handle empty contract folder', async () => {
      const emptyDir = path.join(testOutputDir, 'empty-contracts');
      mkdirSync(emptyDir, { recursive: true });

      const result = await FlatteningService.flattenFolder(
        emptyDir,
        testOutputDir
      );

      // Should either succeed with empty wrapper or fail gracefully
      expect(typeof result.success).toBe('boolean');
      if (!result.success) {
        expect(result.error).toBeDefined();
      }
    });
  });

  describe('flattenFolderSync', () => {
    it('should return a string message', () => {
      const contractsDir = path.join(__dirname, '../test-contracts');

      const result = FlatteningService.flattenFolderSync(contractsDir);

      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle non-existent directory', () => {
      const nonExistentDir = path.join(__dirname, 'non-existent');

      const result = FlatteningService.flattenFolderSync(nonExistentDir);

      expect(typeof result).toBe('string');
      expect(result).toContain('Error:');
    });
  });
});
