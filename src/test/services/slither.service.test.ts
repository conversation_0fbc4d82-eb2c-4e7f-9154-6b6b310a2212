import { describe, it, expect, beforeAll } from '@jest/globals';
import path from 'path';
import { SlitherService } from '../../services/slither.service';

describe('SlitherService', () => {
  const testContractPath = path.join(__dirname, '../test-contracts/Meh.sol');
  const nonExistentPath = path.join(__dirname, 'non-existent.sol');

  describe('isSlitherAvailable', () => {
    it('should check if Slither is available', async () => {
      const isAvailable = await SlitherService.isSlitherAvailable();

      // This test will pass regardless of whether Slither is installed
      // It just checks that the method returns a boolean
      expect(typeof isAvailable).toBe('boolean');
    });
  });

  describe('parseContract', () => {
    beforeAll(async () => {
      const isAvailable = await SlitherService.isSlitherAvailable();
      if (!isAvailable) {
        console.warn(
          '⚠️  Slither is not available - some tests will be skipped'
        );
      }
    });

    it('should fail with descriptive error when file does not exist', async () => {
      const result = await SlitherService.parseContract(nonExistentPath);

      expect(result.success).toBe(false);
      expect(result.error).toContain('File does not exist');
      expect(result.filePath).toBe(nonExistentPath);
    });

    it('should fail with descriptive error when Slither is not installed', async () => {
      const isAvailable = await SlitherService.isSlitherAvailable();

      if (!isAvailable) {
        const result = await SlitherService.parseContract(testContractPath);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Slither is not installed');
        expect(result.error).toContain('pip install slither-analyzer');
        expect(result.filePath).toBe(testContractPath);
      } else {
        // If Slither is available, test the happy path
        const result = await SlitherService.parseContract(testContractPath);

        if (result.success) {
          expect(result.ast).toBeDefined();
          expect(result.filePath).toBe(testContractPath);
          expect(result.error).toBeUndefined();

          // Check AST structure
          if (result.ast) {
            expect(result.ast.compilation_units).toBeDefined();
            expect(Array.isArray(result.ast.compilation_units)).toBe(true);
          }
        } else {
          // If parsing failed, ensure we have a descriptive error
          expect(result.error).toBeDefined();
          expect(typeof result.error).toBe('string');
          if (result.error) {
            expect(result.error.length).toBeGreaterThan(0);
          }
        }
      }
    });

    it('should handle timeout appropriately', async () => {
      const isAvailable = await SlitherService.isSlitherAvailable();

      if (isAvailable) {
        // Test with very short timeout (1ms) - should fail
        const result = await SlitherService.parseContract(testContractPath, 1);

        // Either succeeds very quickly or times out
        if (!result.success) {
          expect(result.error).toBeDefined();
        }
      }
    }, 10000); // 10 second timeout for this test
  });

  describe('parseContracts', () => {
    it('should parse multiple contracts', async () => {
      const contractPaths = [
        path.join(__dirname, '../test-contracts/Meh.sol'),
        path.join(__dirname, '../test-contracts/TestContract.sol'),
      ];

      const results = await SlitherService.parseContracts(contractPaths);

      expect(results).toHaveLength(2);
      expect(results[0]?.filePath).toBe(contractPaths[0]);
      expect(results[1]?.filePath).toBe(contractPaths[1]);

      // Each result should have either success or error
      results.forEach((result) => {
        expect(typeof result.success).toBe('boolean');
        if (!result.success) {
          expect(result.error).toBeDefined();
        }
      });
    });

    it('should handle mix of valid and invalid files', async () => {
      const contractPaths = [
        path.join(__dirname, '../test-contracts/Meh.sol'),
        nonExistentPath,
      ];

      const results = await SlitherService.parseContracts(contractPaths);

      expect(results).toHaveLength(2);

      // Second result should fail (non-existent file)
      expect(results[1]?.success).toBe(false);
      expect(results[1]?.error).toContain('File does not exist');
    });
  });
});
