/**
 * Solidity AST Service Tests
 * Tests for complete AST parsing using solc compiler
 */

import { SolidityAstService } from '../../services/solidity-ast.service';
import path from 'path';

describe('SolidityAstService', () => {
  const testContractPath = path.join(
    __dirname,
    '../test-contracts/CpgTestContract.sol'
  );
  const mehContractPath = path.join(__dirname, '../test-contracts/Meh.sol');

  beforeAll(() => {
    // Ensure test contracts exist
    expect(require('fs').existsSync(testContractPath)).toBe(true);
    expect(require('fs').existsSync(mehContractPath)).toBe(true);
  });

  describe('isSolcAvailable', () => {
    it('should check if solc compiler is available', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (isAvailable) {
        console.log('✅ solc compiler is available');
      } else {
        console.log(
          '⚠️  solc compiler not available - some tests will be skipped'
        );
      }

      expect(typeof isAvailable).toBe('boolean');
    });
  });

  describe('parseContract', () => {
    it('should parse a simple contract and return complete AST', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping AST parsing test');
        return;
      }

      const result = await SolidityAstService.parseContract(mehContractPath);

      console.log('📊 Solidity AST Result:');
      console.log(`  Success: ${result.success}`);
      console.log(`  Compiler Version: ${result.compilerVersion}`);
      console.log(`  File Path: ${result.filePath}`);

      expect(result.success).toBe(true);
      expect(result.ast).toBeDefined();
      expect(result.compilerVersion).toBeDefined();
      expect(result.filePath).toBe(mehContractPath);

      if (result.ast) {
        console.log(`  AST Node Type: ${result.ast.nodeType}`);
        console.log(`  AST ID: ${result.ast.id}`);
        console.log(`  AST Source: ${result.ast.src}`);

        expect(result.ast.nodeType).toBeDefined();
        expect(result.ast.id).toBeDefined();
        expect(result.ast.src).toBeDefined();
      }
    });

    it('should parse comprehensive contract with modern Solidity features', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping comprehensive AST test');
        return;
      }

      const result = await SolidityAstService.parseContract(testContractPath);

      console.log('📊 Comprehensive Contract AST:');
      console.log(`  Success: ${result.success}`);

      if (!result.success) {
        console.log(`  Error: ${result.error}`);
        return;
      }

      expect(result.success).toBe(true);
      expect(result.ast).toBeDefined();

      if (result.ast) {
        // Find contracts
        const contracts = SolidityAstService.getContracts(result.ast);
        console.log(`  Contracts found: ${contracts.length}`);

        contracts.forEach((contract, index) => {
          console.log(
            `    Contract ${index + 1}: ${contract.name} (${contract.contractKind})`
          );
        });

        expect(contracts.length).toBeGreaterThan(0);

        // Find functions
        const functions = SolidityAstService.getFunctions(result.ast);
        console.log(`  Functions found: ${functions.length}`);

        functions.forEach((func, index) => {
          console.log(`    Function ${index + 1}: ${func.name}`);
          console.log(`      Visibility: ${func.visibility}`);
          console.log(`      State Mutability: ${func.stateMutability}`);
          console.log(`      Selector: ${func.functionSelector || 'N/A'}`);
        });

        expect(functions.length).toBeGreaterThan(0);

        // Find variables
        const variables = SolidityAstService.getVariables(result.ast);
        console.log(`  Variables found: ${variables.length}`);

        variables.forEach((variable, index) => {
          console.log(`    Variable ${index + 1}: ${variable.name}`);
          console.log(
            `      State Variable: ${variable.stateVariable || false}`
          );
          console.log(
            `      Storage Location: ${variable.storageLocation || 'default'}`
          );
          console.log(
            `      Type: ${variable.typeDescriptions?.typeString || 'unknown'}`
          );
        });

        expect(variables.length).toBeGreaterThan(0);
      }
    });

    it('should handle non-existent file gracefully', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping error handling test');
        return;
      }

      const result = await SolidityAstService.parseContract('non-existent.sol');

      expect(result.success).toBe(false);
      expect(result.error).toContain('File not found');
      expect(result.ast).toBeUndefined();
    });
  });

  describe('parseWithSlitherAnnotations', () => {
    it('should combine Solidity AST with Slither detectors', async () => {
      const isSolcAvailable = await SolidityAstService.isSolcAvailable();

      if (!isSolcAvailable) {
        console.log('⚠️  solc not available - skipping hybrid parsing test');
        return;
      }

      const result =
        await SolidityAstService.parseWithSlitherAnnotations(mehContractPath);

      console.log('📊 Hybrid Parsing Result:');
      console.log(`  Solidity AST Success: ${result.solidityAst.success}`);
      console.log(`  Slither Available: ${!!result.slitherDetectors}`);
      console.log(`  Combined: ${result.combined}`);

      expect(result.solidityAst.success).toBe(true);
      expect(result.solidityAst.ast).toBeDefined();

      if (result.slitherDetectors) {
        console.log('✅ Slither detectors also available');
      } else {
        console.log('⚠️  Slither detectors not available');
      }
    });
  });

  describe('utility functions', () => {
    it('should parse source location correctly', () => {
      const sourceLocation = SolidityAstService.parseSourceLocation('123:45:0');

      expect(sourceLocation).toBeDefined();
      expect(sourceLocation?.start).toBe(123);
      expect(sourceLocation?.length).toBe(45);
      expect(sourceLocation?.sourceUnit).toBe(0);
    });

    it('should handle invalid source location', () => {
      const sourceLocation = SolidityAstService.parseSourceLocation('invalid');

      expect(sourceLocation).toBeUndefined();
    });

    it('should find nodes by type', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping node finding test');
        return;
      }

      const result = await SolidityAstService.parseContract(mehContractPath);

      if (!result.success || !result.ast) {
        console.log('⚠️  AST parsing failed - skipping node finding test');
        return;
      }

      const contracts = SolidityAstService.findNodesByType(
        result.ast,
        'ContractDefinition'
      );
      const functions = SolidityAstService.findNodesByType(
        result.ast,
        'FunctionDefinition'
      );
      const variables = SolidityAstService.findNodesByType(
        result.ast,
        'VariableDeclaration'
      );

      console.log('📊 Node Finding Results:');
      console.log(`  ContractDefinition nodes: ${contracts.length}`);
      console.log(`  FunctionDefinition nodes: ${functions.length}`);
      console.log(`  VariableDeclaration nodes: ${variables.length}`);

      expect(contracts.length).toBeGreaterThanOrEqual(0);
      expect(functions.length).toBeGreaterThanOrEqual(0);
      expect(variables.length).toBeGreaterThanOrEqual(0);
    });
  });
});
