import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { existsSync, unlinkSync, mkdirSync, rmSync } from 'fs';
import path from 'path';
import { WrapperGeneratorService } from '../../services/wrapper-generator.service';

describe('WrapperGeneratorService', () => {
  const testDir = path.join(__dirname, '../test-output');
  const wrapperPath = path.join(testDir, 'Wrapper.sol');

  beforeEach(() => {
    // Create test directory
    if (!existsSync(testDir)) {
      mkdirSync(testDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up test files
    if (existsSync(wrapperPath)) {
      unlinkSync(wrapperPath);
    }
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
  });

  describe('generateWrapperContract', () => {
    it('should generate wrapper contract for test contracts', async () => {
      const contractsDir = path.join(__dirname, '../test-contracts');
      
      const result = await WrapperGeneratorService.generateWrapperContract(contractsDir);
      
      expect(result).toBe(path.join(contractsDir, 'Wrapper.sol'));
      expect(existsSync(result)).toBe(true);
      
      // Read the generated wrapper content
      const fs = await import('fs');
      const content = fs.readFileSync(result, 'utf8');
      
      expect(content).toContain('// SPDX-License-Identifier: MIT');
      expect(content).toContain('pragma solidity ^0.8.0;');
      expect(content).toContain('import "./Meh.sol";');
      expect(content).toContain('import "./TestContract.sol";');
      expect(content).toContain('contract Wrapper {}');
      
      // Clean up
      unlinkSync(result);
    });

    it('should handle empty directory', async () => {
      const result = await WrapperGeneratorService.generateWrapperContract(testDir);
      
      expect(result).toBe(wrapperPath);
      expect(existsSync(result)).toBe(true);
      
      const fs = await import('fs');
      const content = fs.readFileSync(result, 'utf8');
      
      expect(content).toContain('// SPDX-License-Identifier: MIT');
      expect(content).toContain('pragma solidity ^0.8.0;');
      expect(content).toContain('contract Wrapper {}');
      expect(content).not.toContain('import');
    });

    it('should throw error for non-existent directory', async () => {
      const nonExistentDir = path.join(__dirname, 'non-existent');
      
      await expect(
        WrapperGeneratorService.generateWrapperContract(nonExistentDir)
      ).rejects.toThrow('Failed to generate wrapper contract');
    });
  });

  describe('generateWrapperContractSync', () => {
    it('should generate wrapper contract synchronously', () => {
      const contractsDir = path.join(__dirname, '../test-contracts');
      
      const result = WrapperGeneratorService.generateWrapperContractSync(contractsDir);
      
      expect(result).toBe(path.join(contractsDir, 'Wrapper.sol'));
      expect(existsSync(result)).toBe(true);
      
      // Clean up
      unlinkSync(result);
    });

    it('should throw error for non-existent directory', () => {
      const nonExistentDir = path.join(__dirname, 'non-existent');
      
      expect(() => {
        WrapperGeneratorService.generateWrapperContractSync(nonExistentDir);
      }).toThrow('Failed to generate wrapper contract');
    });
  });
});
