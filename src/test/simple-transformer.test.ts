/**
 * Simple Transformer Test
 * Basic test to verify transformer is working
 */

import { SolidityCpgTransformer } from '../cpg/solidity-transformer';

describe('Simple Transformer Test', () => {
  it('should create transformer without hanging', () => {
    console.log('🔧 Creating transformer...');
    const transformer = new SolidityCpgTransformer();
    console.log('✅ Transformer created successfully');

    expect(transformer).toBeDefined();
  });

  it('should process simple AST without hanging', () => {
    console.log('🔧 Testing simple AST processing...');
    const transformer = new SolidityCpgTransformer();

    // Create a minimal AST structure
    const simpleAst = {
      success: true,
      ast: {
        id: 1,
        nodeType: 'SourceUnit',
        src: '0:100:0',
        nodes: [
          {
            id: 2,
            nodeType: 'PragmaDirective',
            src: '0:23:0',
            literals: ['solidity', '^0.8.0'],
          },
        ],
      },
      filePath: 'test.sol',
      compilerVersion: '0.8.0',
    };

    console.log('🔄 Processing AST...');
    const result = transformer.astToCpg(simpleAst);
    console.log('✅ AST processed successfully');

    expect(result).toBeDefined();
    expect(result.nodes).toBeDefined();
    expect(result.edges).toBeDefined();
    expect(result.metadata).toBeDefined();

    console.log(
      `📊 Result: ${result.nodes.size} nodes, ${result.edges.size} edges`
    );
  });
});
