/**
 * Enhanced CPG Transformer Tests
 * Comprehensive tests for the modular, extensible CPG transformer
 */

import { SolidityAstService } from '../../services/solidity-ast.service';
import { solidityAstToCpg } from '../../cpg/solidity-transformer';
import path from 'path';

describe('Enhanced CPG Transformer', () => {
  const testContractPath = path.join(
    __dirname,
    '../test-contracts/CpgTestContract.sol'
  );
  const mehContractPath = path.join(__dirname, '../test-contracts/Meh.sol');

  beforeAll(() => {
    // Ensure test contracts exist
    expect(require('fs').existsSync(testContractPath)).toBe(true);
    expect(require('fs').existsSync(mehContractPath)).toBe(true);
  });

  describe('Basic CPG Construction', () => {
    it('should transform simple contract AST to comprehensive CPG', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log(
          '⚠️  solc not available - skipping CPG transformation test'
        );
        return;
      }

      // Parse AST
      const astResult = await SolidityAstService.parseContract(mehContractPath);
      expect(astResult.success).toBe(true);
      expect(astResult.ast).toBeDefined();

      if (!astResult.ast) return;

      // Transform to CPG
      console.log('🔄 Transforming AST to CPG...');
      const cpg = solidityAstToCpg(astResult);

      console.log('📊 CPG Transformation Results:');
      console.log(`  Nodes: ${cpg.nodes.size}`);
      console.log(`  Edges: ${cpg.edges.size}`);
      console.log(`  Contracts: ${cpg.metadata.contractCount}`);
      console.log(`  Functions: ${cpg.metadata.functionCount}`);
      console.log(`  Variables: ${cpg.metadata.variableCount}`);

      // Validate CPG structure
      expect(cpg.nodes.size).toBeGreaterThan(0);
      expect(cpg.edges.size).toBeGreaterThan(0);
      expect(cpg.metadata.contractCount).toBeGreaterThan(0);
      expect(cpg.metadata.functionCount).toBeGreaterThan(0);
      expect(cpg.metadata.variableCount).toBeGreaterThan(0);

      // Validate node types
      const nodeTypes = new Set<string>();
      for (const node of cpg.nodes.values()) {
        nodeTypes.add(node.type);
      }

      console.log(`  Node Types: ${Array.from(nodeTypes).join(', ')}`);
      expect(nodeTypes.has('CONTRACT')).toBe(true);
      expect(nodeTypes.has('FUNCTION')).toBe(true);
      expect(nodeTypes.has('VARIABLE')).toBe(true);

      // Validate edge types
      const edgeTypes = new Set<string>();
      for (const edge of cpg.edges.values()) {
        edgeTypes.add(edge.type);
      }

      console.log(`  Edge Types: ${Array.from(edgeTypes).join(', ')}`);
      expect(edgeTypes.has('CONTAINS')).toBe(true);
    });

    it('should transform comprehensive contract with modern Solidity features', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping comprehensive CPG test');
        return;
      }

      // Parse comprehensive contract
      const astResult =
        await SolidityAstService.parseContract(testContractPath);
      expect(astResult.success).toBe(true);
      expect(astResult.ast).toBeDefined();

      if (!astResult.ast) return;

      // Transform to CPG
      console.log('🔄 Transforming comprehensive contract to CPG...');
      const cpg = solidityAstToCpg(astResult);

      console.log('📊 Comprehensive CPG Results:');
      console.log(`  Total Nodes: ${cpg.nodes.size}`);
      console.log(`  Total Edges: ${cpg.edges.size}`);

      // Analyze node distribution
      const nodeDistribution = new Map<string, number>();
      for (const node of cpg.nodes.values()) {
        nodeDistribution.set(
          node.type,
          (nodeDistribution.get(node.type) || 0) + 1
        );
      }

      console.log('  Node Distribution:');
      for (const [type, count] of nodeDistribution) {
        console.log(`    ${type}: ${count}`);
      }

      // Analyze edge distribution
      const edgeDistribution = new Map<string, number>();
      for (const edge of cpg.edges.values()) {
        edgeDistribution.set(
          edge.type,
          (edgeDistribution.get(edge.type) || 0) + 1
        );
      }

      console.log('  Edge Distribution:');
      for (const [type, count] of edgeDistribution) {
        console.log(`    ${type}: ${count}`);
      }

      // Validate comprehensive structure
      expect(cpg.nodes.size).toBeGreaterThan(50); // Should have many nodes
      expect(cpg.edges.size).toBeGreaterThan(30); // Should have many edges
      expect(nodeDistribution.get('CONTRACT')).toBeGreaterThanOrEqual(1);
      expect(nodeDistribution.get('FUNCTION')).toBeGreaterThan(5);
      expect(nodeDistribution.get('VARIABLE')).toBeGreaterThan(10);
    });
  });

  describe('Node Processing Quality', () => {
    it('should correctly identify and process contract nodes', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping contract node test');
        return;
      }

      const astResult =
        await SolidityAstService.parseContract(testContractPath);
      if (!astResult.success || !astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      // Find contract nodes
      const contractNodes = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'CONTRACT'
      );

      console.log('📊 Contract Nodes Analysis:');
      console.log(`  Found ${contractNodes.length} contract nodes`);

      expect(contractNodes.length).toBeGreaterThan(0);

      for (const contractNode of contractNodes) {
        console.log(`  Contract: ${contractNode.name}`);
        console.log(`    ID: ${contractNode.id}`);
        console.log(
          `    Properties: ${JSON.stringify(contractNode.properties, null, 2)}`
        );

        // Validate contract node structure
        expect(contractNode.name).toBeDefined();
        expect(contractNode.id).toBeDefined();
        expect(contractNode.properties).toBeDefined();
        expect(contractNode.type).toBe('CONTRACT');
      }
    });

    it('should correctly identify and process function nodes', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping function node test');
        return;
      }

      const astResult =
        await SolidityAstService.parseContract(testContractPath);
      if (!astResult.success || !astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      // Find function nodes
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'FUNCTION'
      );

      console.log('📊 Function Nodes Analysis:');
      console.log(`  Found ${functionNodes.length} function nodes`);

      expect(functionNodes.length).toBeGreaterThan(0);

      // Check for specific functions we know should exist
      const functionNames = functionNodes.map((node) => node.name);
      console.log(`  Function Names: ${functionNames.join(', ')}`);

      // Should have constructor and other functions
      expect(
        functionNames.some(
          (name) => name === 'constructor' || name === '' || name === 'unknown'
        )
      ).toBe(true);
      expect(
        functionNames.some(
          (name) =>
            name.includes('test') ||
            name.includes('update') ||
            name.includes('transfer')
        )
      ).toBe(true);

      // Validate function node structure
      for (const funcNode of functionNodes.slice(0, 3)) {
        // Check first 3 functions
        console.log(`  Function: ${funcNode.name}`);
        console.log(`    Signature: ${(funcNode.properties as any).signature}`);
        console.log(
          `    Visibility: ${(funcNode.properties as any).visibility}`
        );
        console.log(
          `    State Mutability: ${(funcNode.properties as any).stateMutability}`
        );

        expect((funcNode.properties as any).signature).toBeDefined();
        expect((funcNode.properties as any).visibility).toBeDefined();
        expect((funcNode.properties as any).stateMutability).toBeDefined();
      }
    });

    it('should correctly identify and process variable nodes', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping variable node test');
        return;
      }

      const astResult =
        await SolidityAstService.parseContract(testContractPath);
      if (!astResult.success || !astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      // Find variable nodes
      const variableNodes = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'VARIABLE'
      );

      console.log('📊 Variable Nodes Analysis:');
      console.log(`  Found ${variableNodes.length} variable nodes`);

      expect(variableNodes.length).toBeGreaterThan(0);

      // Analyze variable scopes
      const scopeDistribution = new Map<string, number>();
      const stateVariables = [];
      const localVariables = [];

      for (const varNode of variableNodes) {
        const scope = (varNode.properties as any).scope as string;
        scopeDistribution.set(scope, (scopeDistribution.get(scope) || 0) + 1);

        if ((varNode.properties as any).isStateVariable) {
          stateVariables.push(varNode.name);
        } else {
          localVariables.push(varNode.name);
        }
      }

      console.log('  Scope Distribution:');
      for (const [scope, count] of scopeDistribution) {
        console.log(`    ${scope}: ${count}`);
      }

      console.log(
        `  State Variables: ${stateVariables.slice(0, 5).join(', ')}${stateVariables.length > 5 ? '...' : ''}`
      );
      console.log(
        `  Local Variables: ${localVariables.slice(0, 5).join(', ')}${localVariables.length > 5 ? '...' : ''}`
      );

      // Should have both state and local variables
      expect(stateVariables.length).toBeGreaterThan(0);
      expect(localVariables.length).toBeGreaterThan(0);
      expect(scopeDistribution.get('STATE')).toBeGreaterThan(0);
      expect(scopeDistribution.get('LOCAL')).toBeGreaterThan(0);
    });
  });

  describe('Edge Processing Quality', () => {
    it('should create proper containment relationships', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping containment test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(mehContractPath);
      if (!astResult.success || !astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      // Find CONTAINS edges
      const containsEdges = Array.from(cpg.edges.values()).filter(
        (edge) => edge.type === 'CONTAINS'
      );

      console.log('📊 Containment Analysis:');
      console.log(`  Found ${containsEdges.length} CONTAINS edges`);

      expect(containsEdges.length).toBeGreaterThan(0);

      // Validate containment structure
      for (const edge of containsEdges.slice(0, 5)) {
        // Check first 5 edges
        const sourceNode = cpg.nodes.get(edge.source);
        const targetNode = cpg.nodes.get(edge.target);

        expect(sourceNode).toBeDefined();
        expect(targetNode).toBeDefined();

        console.log(
          `  ${sourceNode?.type}(${sourceNode?.name}) CONTAINS ${targetNode?.type}(${targetNode?.name})`
        );
      }
    });

    it('should maintain CPG integrity and consistency', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();

      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping integrity test');
        return;
      }

      const astResult =
        await SolidityAstService.parseContract(testContractPath);
      if (!astResult.success || !astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 CPG Integrity Check:');

      // Check that all edge endpoints exist
      let validEdges = 0;
      let invalidEdges = 0;

      for (const edge of cpg.edges.values()) {
        const sourceExists = cpg.nodes.has(edge.source);
        const targetExists = cpg.nodes.has(edge.target);

        if (sourceExists && targetExists) {
          validEdges++;
        } else {
          invalidEdges++;
          console.log(
            `  ⚠️  Invalid edge: ${edge.id} (${edge.source} -> ${edge.target})`
          );
        }
      }

      console.log(`  Valid edges: ${validEdges}`);
      console.log(`  Invalid edges: ${invalidEdges}`);

      // All edges should be valid
      expect(invalidEdges).toBe(0);
      expect(validEdges).toBe(cpg.edges.size);

      // Check metadata consistency
      const actualContractCount = Array.from(cpg.nodes.values()).filter(
        (n) => n.type === 'CONTRACT'
      ).length;
      const actualFunctionCount = Array.from(cpg.nodes.values()).filter(
        (n) => n.type === 'FUNCTION'
      ).length;
      const actualVariableCount = Array.from(cpg.nodes.values()).filter(
        (n) => n.type === 'VARIABLE'
      ).length;

      console.log(`  Metadata vs Actual:`);
      console.log(
        `    Contracts: ${cpg.metadata.contractCount} vs ${actualContractCount}`
      );
      console.log(
        `    Functions: ${cpg.metadata.functionCount} vs ${actualFunctionCount}`
      );
      console.log(
        `    Variables: ${cpg.metadata.variableCount} vs ${actualVariableCount}`
      );

      expect(cpg.metadata.contractCount).toBe(actualContractCount);
      expect(cpg.metadata.functionCount).toBe(actualFunctionCount);
      expect(cpg.metadata.variableCount).toBe(actualVariableCount);
    });
  });
});
