/**
 * Phase 1: Critical Missing Features Test Suite
 * Tests: Transient Storage, Receive/Fallback, Virtual/Override, Abstract Contracts
 */

import { SolidityCpgTransformer } from '../../cpg/solidity-transformer';
import { SolidityAstService } from '../../services/solidity-ast.service';
import { CpgGraph } from '../../types/cpg';
import path from 'path';

describe('Phase 1: Critical Missing Features', () => {
  let transformer: SolidityCpgTransformer;
  let astService: SolidityAstService;

  beforeAll(() => {
    transformer = new SolidityCpgTransformer();
    astService = new SolidityAstService();
  });

  const validateNodeDistribution = (cpg: CpgGraph, testName: string) => {
    console.log(`🔍 Validating ${testName} Node Distribution:`);
    console.log(`  📊 Node Type Distribution:`);

    const nodeTypes = new Map<string, number>();
    for (const [, node] of cpg.nodes) {
      const count = nodeTypes.get(node.type) || 0;
      nodeTypes.set(node.type, count + 1);
    }

    nodeTypes.forEach((count, type) => {
      console.log(`    ${type}: ${count}`);
    });

    return nodeTypes;
  };

  describe('Transient Storage Detection', () => {
    it('should detect transient storage variables', async () => {
      const contractPath = path.join(
        __dirname,
        '../test-contracts/features/Phase1CriticalFeaturesContract.sol'
      );

      const ast = await SolidityAstService.parseContract(contractPath);
      const cpg = await transformer.astToCpg(ast);

      console.log(`🔍 Validating Transient Storage Detection:`);
      validateNodeDistribution(cpg, 'Transient Storage');

      // Find transient storage variables
      const transientVariables = Array.from(cpg.nodes.values()).filter(
        (node) =>
          node.type === 'VARIABLE' && node.properties['isTransient'] === true
      );

      console.log(
        `  ✅ Transient variables detected: ${transientVariables.length}`
      );

      transientVariables.forEach((variable) => {
        console.log(
          `    - ${variable.name}: ${variable.properties['variableType']}`
        );
      });

      // Should detect transient storage variables
      expect(transientVariables.length).toBeGreaterThanOrEqual(3);
      expect(cpg.nodes.size).toBeGreaterThan(1000);
    });
  });

  describe('Receive/Fallback Function Detection', () => {
    it('should detect receive and fallback functions', async () => {
      const contractPath = path.join(
        __dirname,
        '../test-contracts/features/Phase1CriticalFeaturesContract.sol'
      );

      const ast = await SolidityAstService.parseContract(contractPath);
      const cpg = await transformer.astToCpg(ast);

      console.log(`🔍 Validating Receive/Fallback Function Detection:`);

      // Find receive functions
      const receiveFunctions = Array.from(cpg.nodes.values()).filter(
        (node) =>
          node.type === 'FUNCTION' && node.properties['isReceive'] === true
      );

      // Find fallback functions
      const fallbackFunctions = Array.from(cpg.nodes.values()).filter(
        (node) =>
          node.type === 'FUNCTION' && node.properties.isFallback === true
      );

      console.log(
        `  ✅ Receive functions detected: ${receiveFunctions.length}`
      );
      console.log(
        `  ✅ Fallback functions detected: ${fallbackFunctions.length}`
      );

      receiveFunctions.forEach((func) => {
        console.log(
          `    - Receive: ${func.name} (${func.properties.stateMutability})`
        );
      });

      fallbackFunctions.forEach((func) => {
        console.log(
          `    - Fallback: ${func.name} (${func.properties.stateMutability})`
        );
      });

      // Should detect receive and fallback functions
      expect(receiveFunctions.length).toBeGreaterThanOrEqual(2);
      expect(fallbackFunctions.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('Virtual/Override Keywords Detection', () => {
    it('should detect virtual and override function modifiers', async () => {
      const contractPath = path.join(
        __dirname,
        '../test-contracts/features/Phase1CriticalFeaturesContract.sol'
      );

      const ast = await astService.parseContract(contractPath);
      const cpg = await transformer.astToCpg(ast, contractPath);

      console.log(`🔍 Validating Virtual/Override Detection:`);

      // Find virtual functions
      const virtualFunctions = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'FUNCTION' && node.properties.isVirtual === true
      );

      // Find override functions
      const overrideFunctions = Array.from(cpg.nodes.values()).filter(
        (node) =>
          node.type === 'FUNCTION' && node.properties.isOverride === true
      );

      console.log(
        `  ✅ Virtual functions detected: ${virtualFunctions.length}`
      );
      console.log(
        `  ✅ Override functions detected: ${overrideFunctions.length}`
      );

      virtualFunctions.forEach((func) => {
        console.log(`    - Virtual: ${func.name}`);
      });

      overrideFunctions.forEach((func) => {
        console.log(`    - Override: ${func.name}`);
      });

      // Should detect virtual and override functions
      expect(virtualFunctions.length).toBeGreaterThanOrEqual(3);
      expect(overrideFunctions.length).toBeGreaterThanOrEqual(4);
    });
  });

  describe('Abstract Contract Detection', () => {
    it('should detect abstract contracts', async () => {
      const contractPath = path.join(
        __dirname,
        '../test-contracts/features/Phase1CriticalFeaturesContract.sol'
      );

      const ast = await astService.parseContract(contractPath);
      const cpg = await transformer.astToCpg(ast, contractPath);

      console.log(`🔍 Validating Abstract Contract Detection:`);

      // Find abstract contracts
      const abstractContracts = Array.from(cpg.nodes.values()).filter(
        (node) =>
          node.type === 'CONTRACT' && node.properties.isAbstract === true
      );

      console.log(
        `  ✅ Abstract contracts detected: ${abstractContracts.length}`
      );

      abstractContracts.forEach((contract) => {
        console.log(`    - Abstract: ${contract.name}`);
      });

      // Should detect abstract contracts
      expect(abstractContracts.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('Comprehensive Phase 1 Features', () => {
    it('should detect all Phase 1 features in complex contract', async () => {
      const contractPath = path.join(
        __dirname,
        '../test-contracts/features/Phase1CriticalFeaturesContract.sol'
      );

      const ast = await astService.parseContract(contractPath);
      const cpg = await transformer.astToCpg(ast, contractPath);

      console.log(`🔍 Validating Comprehensive Phase 1 Features:`);
      const nodeTypes = validateNodeDistribution(cpg, 'Phase 1 Complete');

      // Validate all Phase 1 features are present
      const transientVars = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'VARIABLE' && node.properties.isTransient
      );

      const receiveFuncs = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'FUNCTION' && node.properties.isReceive
      );

      const fallbackFuncs = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'FUNCTION' && node.properties.isFallback
      );

      const virtualFuncs = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'FUNCTION' && node.properties.isVirtual
      );

      const overrideFuncs = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'FUNCTION' && node.properties.isOverride
      );

      const abstractContracts = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'CONTRACT' && node.properties.isAbstract
      );

      console.log(`  📊 Phase 1 Feature Summary:`);
      console.log(
        `    🔹 Transient Storage Variables: ${transientVars.length}`
      );
      console.log(`    🔹 Receive Functions: ${receiveFuncs.length}`);
      console.log(`    🔹 Fallback Functions: ${fallbackFuncs.length}`);
      console.log(`    🔹 Virtual Functions: ${virtualFuncs.length}`);
      console.log(`    🔹 Override Functions: ${overrideFuncs.length}`);
      console.log(`    🔹 Abstract Contracts: ${abstractContracts.length}`);

      // Comprehensive validation
      expect(transientVars.length).toBeGreaterThanOrEqual(3);
      expect(receiveFuncs.length).toBeGreaterThanOrEqual(2);
      expect(fallbackFuncs.length).toBeGreaterThanOrEqual(2);
      expect(virtualFuncs.length).toBeGreaterThanOrEqual(3);
      expect(overrideFuncs.length).toBeGreaterThanOrEqual(4);
      expect(abstractContracts.length).toBeGreaterThanOrEqual(2);

      // Overall CPG structure validation
      expect(cpg.nodes.size).toBeGreaterThan(2000);
      expect(cpg.edges.size).toBeGreaterThan(2000);
      expect(nodeTypes.get('FUNCTION')).toBeGreaterThan(20);
      expect(nodeTypes.get('VARIABLE')).toBeGreaterThan(50);
      expect(nodeTypes.get('CONTRACT')).toBeGreaterThanOrEqual(6);
    });
  });
});
