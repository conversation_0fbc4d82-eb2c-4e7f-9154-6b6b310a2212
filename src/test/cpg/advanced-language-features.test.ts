import { describe, it, expect } from '@jest/globals';
import path from 'path';
import { SolidityAstService } from '../../services/solidity-ast.service';
import { solidityAstToCpg } from '../../cpg/solidity-transformer';

describe('Advanced Solidity Language Features with Node Validation', () => {
  const featuresDir = path.join(__dirname, '../test-contracts/features');

  // Helper function to validate node distribution
  function validateNodeDistribution(
    cpg: any,
    expectedDistribution: Record<string, number>
  ) {
    const nodeTypes = new Map<string, number>();
    Array.from(cpg.nodes.values()).forEach((node: any) => {
      const count = nodeTypes.get(node.type) || 0;
      nodeTypes.set(node.type, count + 1);
    });

    console.log('  📊 Node Type Distribution:');
    nodeTypes.forEach((count, type) => {
      console.log(`    ${type}: ${count}`);
    });

    // Validate expected distribution
    Object.entries(expectedDistribution).forEach(
      ([nodeType, expectedCount]) => {
        const actualCount = nodeTypes.get(nodeType) || 0;
        expect(actualCount).toBeGreaterThanOrEqual(expectedCount);
      }
    );

    return nodeTypes;
  }

  // Helper function to validate edge distribution
  function validateEdgeDistribution(cpg: any) {
    const edgeTypes = new Map<string, number>();
    Array.from(cpg.edges.values()).forEach((edge: any) => {
      const count = edgeTypes.get(edge.type) || 0;
      edgeTypes.set(edge.type, count + 1);
    });

    console.log('  🔗 Edge Type Distribution:');
    edgeTypes.forEach((count, type) => {
      console.log(`    ${type}: ${count}`);
    });

    return edgeTypes;
  }

  describe('Node Count Validation', () => {
    it('should have correct node distribution for array contract', async () => {
      const contractPath = path.join(featuresDir, 'ArrayTypeContract.sol');
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping validation test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating Array Contract Node Distribution:');
      validateNodeDistribution(cpg, {
        CONTRACT: 1,
        VARIABLE: 14,
        FUNCTION: 10,
        STRUCT: 1,
      });

      validateEdgeDistribution(cpg);

      // Validate specific content
      const arrayVariables = Array.from(cpg.nodes.values()).filter(
        (node: any) =>
          node.type === 'VARIABLE' &&
          ((node.properties as any).variableType?.includes('[]') ||
            (node.properties as any).variableType?.includes('['))
      );

      expect(arrayVariables.length).toBe(14);
      expect(cpg.nodes.size).toBe(99);
      expect(cpg.edges.size).toBe(98);
    });

    it('should have correct node distribution for constants contract', async () => {
      const contractPath = path.join(
        featuresDir,
        'ConstantsImmutablesContract.sol'
      );
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping validation test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating Constants Contract Node Distribution:');
      validateNodeDistribution(cpg, {
        CONTRACT: 1,
        VARIABLE: 25,
        FUNCTION: 15,
        PRAGMA: 2,
        IMPORT: 2,
      });

      // Validate constants and immutables
      const variableNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'VARIABLE'
      );
      const constants = variableNodes.filter(
        (node: any) =>
          (node.properties as any).isConstant === true ||
          (node.properties as any).mutability === 'constant'
      );
      const immutables = variableNodes.filter(
        (node: any) =>
          (node.properties as any).isImmutable === true ||
          (node.properties as any).mutability === 'immutable'
      );

      console.log(`  ✅ Constants detected: ${constants.length}`);
      console.log(`  ✅ Immutables detected: ${immutables.length}`);

      expect(constants.length).toBe(13);
      expect(immutables.length).toBe(12);
      expect(cpg.nodes.size).toBe(207);
    });
  });

  describe('Missing Language Features Implementation', () => {
    it('should detect assembly blocks in assembly operators contract', async () => {
      const contractPath = path.join(
        featuresDir,
        'AssemblyOperatorsContract.sol'
      );
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping assembly test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating Assembly Block Detection:');
      validateNodeDistribution(cpg, {
        CONTRACT: 1,
        FUNCTION: 15,
        ASSEMBLY_BLOCK: 5, // Should detect multiple assembly blocks
      });

      const assemblyNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'ASSEMBLY_BLOCK'
      );

      console.log(`  Assembly Blocks Found: ${assemblyNodes.length}`);
      assemblyNodes.forEach((node: any) => {
        console.log(
          `    ${node.name}: ${(node.properties as any).operations?.length || 0} operations`
        );
      });

      // The contract has multiple assembly functions with assembly blocks
      expect(assemblyNodes.length).toBeGreaterThanOrEqual(5);
    });

    it('should detect type aliases and operator overloading patterns', async () => {
      const contractPath = path.join(
        featuresDir,
        'AssemblyOperatorsContract.sol'
      );
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping type aliases test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating Type Aliases and Operator Overloading:');

      // Find using statements (type aliases)
      const usingNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'USING_FOR'
      );
      console.log(`  Using Statements: ${usingNodes.length}`);

      // Find library nodes (operator overloading)
      const libraryNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'LIBRARY'
      );
      console.log(`  Libraries: ${libraryNodes.length}`);

      expect(usingNodes.length).toBeGreaterThanOrEqual(3);
      expect(libraryNodes.length).toBeGreaterThanOrEqual(3);
    });

    it('should detect bytes and crypto operations', async () => {
      const contractPath = path.join(
        featuresDir,
        'BytesCryptoBlockContract.sol'
      );
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping bytes crypto test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating Bytes and Crypto Operations:');
      validateNodeDistribution(cpg, {
        CONTRACT: 1,
        FUNCTION: 20,
        VARIABLE: 10,
      });

      // Find functions with crypto/bytes operations
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      const cryptoFunctions = functionNodes.filter(
        (node: any) =>
          node.name.includes('hash') ||
          node.name.includes('Hash') ||
          node.name.includes('crypto') ||
          node.name.includes('Crypto') ||
          node.name.includes('signature') ||
          node.name.includes('Signature')
      );

      const bytesFunctions = functionNodes.filter(
        (node: any) =>
          node.name.includes('bytes') || node.name.includes('Bytes')
      );

      console.log(`  Crypto Functions: ${cryptoFunctions.length}`);
      console.log(`  Bytes Functions: ${bytesFunctions.length}`);

      expect(cryptoFunctions.length).toBeGreaterThanOrEqual(5);
      expect(bytesFunctions.length).toBeGreaterThanOrEqual(5);
    });

    it('should detect ABI encoding and Create2 patterns', async () => {
      const contractPath = path.join(
        featuresDir,
        'ABICreate2ProxyContract.sol'
      );
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping ABI Create2 test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating ABI Encoding and Create2 Patterns:');
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );

      const abiFunctions = functionNodes.filter(
        (node: any) =>
          node.name.includes('abi') ||
          node.name.includes('ABI') ||
          node.name.includes('encode') ||
          node.name.includes('decode')
      );

      const create2Functions = functionNodes.filter(
        (node: any) =>
          node.name.includes('create2') ||
          node.name.includes('Create2') ||
          node.name.includes('deploy')
      );

      const proxyFunctions = functionNodes.filter(
        (node: any) =>
          node.name.includes('proxy') ||
          node.name.includes('Proxy') ||
          node.name.includes('upgrade') ||
          node.name.includes('beacon') ||
          node.name.includes('diamond')
      );

      console.log(`  ABI Functions: ${abiFunctions.length}`);
      console.log(`  Create2 Functions: ${create2Functions.length}`);
      console.log(`  Proxy Functions: ${proxyFunctions.length}`);

      expect(abiFunctions.length).toBeGreaterThanOrEqual(6);
      expect(create2Functions.length).toBeGreaterThanOrEqual(3);
      expect(proxyFunctions.length).toBeGreaterThanOrEqual(5);
    });

    it('should detect gas optimization patterns', async () => {
      const contractPath = path.join(
        featuresDir,
        'GasOptimizationContract.sol'
      );
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping gas optimization test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating Gas Optimization Patterns:');
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );

      const optimizationFunctions = functionNodes.filter(
        (node: any) =>
          node.name.includes('efficient') ||
          node.name.includes('Efficient') ||
          node.name.includes('optimiz') ||
          node.name.includes('Optimiz') ||
          node.name.includes('unchecked') ||
          node.name.includes('assembly') ||
          node.name.includes('batch') ||
          node.name.includes('packed')
      );

      console.log(
        `  Gas Optimization Functions: ${optimizationFunctions.length}`
      );
      optimizationFunctions.forEach((func: any) => {
        console.log(`    - ${func.name}`);
      });

      expect(optimizationFunctions.length).toBeGreaterThanOrEqual(8);
    });

    it('should validate nested mapping depth correctly', async () => {
      const contractPath = path.join(featuresDir, 'NestedMappingsContract.sol');
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping nested mapping test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating Nested Mapping Depth:');
      const variableNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'VARIABLE'
      );
      const mappingVariables = variableNodes.filter((node: any) =>
        (node.properties as any).variableType?.includes('mapping')
      );

      // Analyze nesting depth
      const nestingAnalysis = mappingVariables.map((node: any) => {
        const type = (node.properties as any).variableType || '';
        const mappingCount = (type.match(/mapping/g) || []).length;
        return {
          name: node.name,
          type,
          depth: mappingCount,
        };
      });

      console.log('  Mapping Nesting Analysis:');
      nestingAnalysis.forEach((mapping) => {
        console.log(
          `    ${mapping.name}: ${mapping.depth} levels - ${mapping.type}`
        );
      });

      // Validate we have 2-level and 3-level mappings
      const twoLevel = nestingAnalysis.filter((m) => m.depth === 2);
      const threeLevel = nestingAnalysis.filter((m) => m.depth === 3);

      console.log(`  ✅ 2-level mappings: ${twoLevel.length}`);
      console.log(`  ✅ 3-level mappings: ${threeLevel.length}`);

      expect(twoLevel.length).toBeGreaterThanOrEqual(4);
      expect(threeLevel.length).toBeGreaterThanOrEqual(4);
      expect(mappingVariables.length).toBe(14);
    });

    it('should validate DeFi function patterns', async () => {
      const contractPath = path.join(
        featuresDir,
        'DeFiSecurityPatternsContract.sol'
      );
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping DeFi test');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating DeFi Function Patterns:');
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );

      // Categorize DeFi functions
      const defiPatterns = {
        amm: functionNodes.filter(
          (node: any) =>
            node.name.includes('swap') ||
            node.name.includes('liquidity') ||
            node.name.includes('Liquidity')
        ),
        flashLoan: functionNodes.filter(
          (node: any) =>
            node.name.includes('flash') || node.name.includes('Flash')
        ),
        security: functionNodes.filter(
          (node: any) =>
            node.name.includes('verify') ||
            node.name.includes('permit') ||
            node.name.includes('batch')
        ),
        math: functionNodes.filter(
          (node: any) =>
            node.name.includes('safe') ||
            node.name.includes('Safe') ||
            node.name.includes('sqrt') ||
            node.name.includes('min') ||
            node.name.includes('max')
        ),
      };

      console.log('  DeFi Pattern Analysis:');
      Object.entries(defiPatterns).forEach(([pattern, functions]) => {
        console.log(`    ${pattern}: ${functions.length} functions`);
        functions.forEach((func: any) => {
          console.log(`      - ${func.name}`);
        });
      });

      expect(defiPatterns.amm.length).toBeGreaterThanOrEqual(2);
      expect(defiPatterns.flashLoan.length).toBeGreaterThanOrEqual(1);
      expect(defiPatterns.security.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('Edge Relationship Validation', () => {
    it('should have correct CONTAINS relationships', async () => {
      const contractPath = path.join(featuresDir, 'ArrayTypeContract.sol');
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log('⚠️  solc not available - skipping edge validation');
        return;
      }

      const astResult = await SolidityAstService.parseContract(contractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);

      console.log('🔍 Validating Edge Relationships:');
      const containsEdges = Array.from(cpg.edges.values()).filter(
        (edge: any) => edge.type === 'CONTAINS'
      );

      console.log(`  CONTAINS edges: ${containsEdges.length}`);

      // Validate that contract contains variables and functions
      const contractNode = Array.from(cpg.nodes.values()).find(
        (node: any) => node.type === 'CONTRACT'
      );
      if (contractNode) {
        const contractEdges = containsEdges.filter(
          (edge: any) => edge.source === contractNode.id
        );
        console.log(
          `  Contract contains ${contractEdges.length} direct children`
        );

        expect(contractEdges.length).toBeGreaterThanOrEqual(15); // Should contain variables + functions + struct
      }

      // Validate edge count matches node count - 1 (tree structure)
      expect(cpg.edges.size).toBe(cpg.nodes.size - 1);
    });
  });
});
