/**
 * Gradual Complexity Test
 * Test processor routing with increasing complexity
 */

import { SolidityCpgTransformer } from '../../cpg/solidity-transformer';
import { SolidityAstService } from '../../services/solidity-ast.service';
import * as fs from 'fs';
import * as path from 'path';

describe('Gradual Complexity Test', () => {
  let transformer: SolidityCpgTransformer;

  beforeEach(() => {
    transformer = new SolidityCpgTransformer();
  });

  // Helper function to create temporary contract file and parse it
  async function parseContractCode(contractCode: string, fileName: string) {
    const tempDir = path.join(__dirname, '../../temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const filePath = path.join(tempDir, fileName);
    fs.writeFileSync(filePath, contractCode);

    try {
      const result = await SolidityAstService.parseContract(filePath);
      return result;
    } finally {
      // Clean up
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }
  }

  it('Level 1: Simple contract with no functions', async () => {
    console.log('🔧 Level 1: Simple contract');

    const contractCode = `
      pragma solidity ^0.8.0;
      
      contract SimpleContract {
          uint256 public value;
      }
    `;

    const astResult = await parseContractCode(
      contractCode,
      'SimpleContract.sol'
    );
    expect(astResult.success).toBe(true);

    const cpg = transformer.astToCpg(astResult);
    console.log(
      `📊 Level 1 Result: ${cpg.nodes.size} nodes, ${cpg.edges.size} edges`
    );

    expect(cpg.nodes.size).toBeGreaterThan(0);
    expect(cpg.edges.size).toBeGreaterThan(0);
  });

  it('Level 2: Contract with simple function', async () => {
    console.log('🔧 Level 2: Contract with function');

    const contractCode = `
      pragma solidity ^0.8.0;
      
      contract FunctionContract {
          uint256 public value;
          
          function setValue(uint256 _value) public {
              value = _value;
          }
      }
    `;

    const astResult = await parseContractCode(
      contractCode,
      'FunctionContract.sol'
    );
    expect(astResult.success).toBe(true);

    const cpg = transformer.astToCpg(astResult);
    console.log(
      `📊 Level 2 Result: ${cpg.nodes.size} nodes, ${cpg.edges.size} edges`
    );

    expect(cpg.nodes.size).toBeGreaterThan(0);
    expect(cpg.edges.size).toBeGreaterThan(0);
  });

  it('Level 3: Contract with virtual function', async () => {
    console.log('🔧 Level 3: Contract with virtual function');

    const contractCode = `
      pragma solidity ^0.8.0;

      contract VirtualContract {
          uint256 public value;

          function setValue(uint256 _value) public virtual {
              value = _value;
          }
      }
    `;

    const astResult = await parseContractCode(
      contractCode,
      'VirtualContract.sol'
    );
    expect(astResult.success).toBe(true);

    const cpg = transformer.astToCpg(astResult);
    console.log(
      `📊 Level 3 Result: ${cpg.nodes.size} nodes, ${cpg.edges.size} edges`
    );

    // Check for virtual function detection
    const virtualFunctions = Array.from(cpg.nodes.values()).filter(
      (node) =>
        node.type === 'FUNCTION' && node.properties['isVirtual'] === true
    );
    console.log(`✅ Virtual functions detected: ${virtualFunctions.length}`);

    expect(cpg.nodes.size).toBeGreaterThan(0);
    expect(virtualFunctions.length).toBeGreaterThan(0);
  });

  it('Level 4: Contract with transient variable', async () => {
    console.log('🔧 Level 4: Contract with transient variable');

    const contractCode = `
      pragma solidity ^0.8.0;

      contract TransientContract {
          uint256 public value;
          uint256 tempValueTransient;

          function setValue(uint256 _value) public {
              tempValueTransient = _value;
              value = tempValueTransient;
          }
      }
    `;

    const astResult = await parseContractCode(
      contractCode,
      'TransientContract.sol'
    );
    expect(astResult.success).toBe(true);

    const cpg = transformer.astToCpg(astResult);
    console.log(
      `📊 Level 4 Result: ${cpg.nodes.size} nodes, ${cpg.edges.size} edges`
    );

    // Check for transient variable detection
    const transientVariables = Array.from(cpg.nodes.values()).filter(
      (node) =>
        node.type === 'VARIABLE' && node.properties['isTransient'] === true
    );
    console.log(
      `✅ Transient variables detected: ${transientVariables.length}`
    );

    expect(cpg.nodes.size).toBeGreaterThan(0);
    expect(transientVariables.length).toBeGreaterThan(0);
  });

  it('Level 5: Abstract contract with override', async () => {
    console.log('🔧 Level 5: Abstract contract with override');

    const contractCode = `
      pragma solidity ^0.8.0;

      abstract contract AbstractBase {
          function abstractFunction() public virtual;
      }

      contract ConcreteContract is AbstractBase {
          function abstractFunction() public override {
              // Implementation
          }
      }
    `;

    const astResult = await parseContractCode(
      contractCode,
      'AbstractContract.sol'
    );
    expect(astResult.success).toBe(true);

    const cpg = transformer.astToCpg(astResult);
    console.log(
      `📊 Level 5 Result: ${cpg.nodes.size} nodes, ${cpg.edges.size} edges`
    );

    // Check for abstract contract detection
    const abstractContracts = Array.from(cpg.nodes.values()).filter(
      (node) =>
        node.type === 'CONTRACT' && node.properties['isAbstract'] === true
    );
    console.log(`✅ Abstract contracts detected: ${abstractContracts.length}`);

    // Check for override function detection
    const overrideFunctions = Array.from(cpg.nodes.values()).filter(
      (node) =>
        node.type === 'FUNCTION' && node.properties['isOverride'] === true
    );
    console.log(`✅ Override functions detected: ${overrideFunctions.length}`);

    expect(cpg.nodes.size).toBeGreaterThan(0);
    expect(abstractContracts.length).toBeGreaterThan(0);
    expect(overrideFunctions.length).toBeGreaterThan(0);
  });
});
