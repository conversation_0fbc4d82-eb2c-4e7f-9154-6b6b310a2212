digraph{
0[label="Node Type: ENTRY_POINT 0
"];
0->1;
1[label="Node Type: NEW VARIABLE 1

EXPRESSION:
processed = userInput + 10

IRs:
TMP_1(uint256) = userInput (c)+ 10
processed(uint256) := TMP_1(uint256)"];
1->2;
2[label="Node Type: EXPRESSION 2

EXPRESSION:
stateValue = processed

IRs:
stateValue(uint256) := processed(uint256)"];
2->3;
3[label="Node Type: RETURN 3

EXPRESSION:
processed

IRs:
RETURN processed"];
}
