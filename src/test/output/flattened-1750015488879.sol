// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

// src/test/test-contracts/Bruh.sol

contract Bruh {
    uint public valMMM;
    
    
    function setBoom(uint _value) public {
        valMMM = _value;
    }
}

// src/test/test-contracts/Meh.sol

contract Meh {
    uint public valueXYZ;
    
    constructor() {
        valueXYZ = 42;
    }
    
    function setValueXYZ(uint _value) public {
        valueXYZ = _value;
    }
}

// src/test/test-contracts/TestContract.sol

contract TestContract {
    uint public value;
    Meh public mehContract;
    
    constructor() {
        mehContract = new Meh();
    }
    
    function setValue(uint _value) public {
        value = _value;
    }
    
    function getValue() public view returns (uint) {
        return value;
    }
    
    function getMehValue() public view returns (uint) {
        return mehContract.valueXYZ();
    }
}

// src/test/test-contracts/Wrapper.sol

contract Wrapper {}
