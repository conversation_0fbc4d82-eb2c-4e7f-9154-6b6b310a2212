// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

// Phase 1: Critical Missing Features Test Contract
// Tests: Transient Storage, Receive/Fallback, Virtual/Override, Abstract Contracts

// 1. ABSTRACT CONTRACT DETECTION
abstract contract AbstractBase {
    // Abstract function - must be implemented by derived contracts
    function abstractFunction() public virtual returns (uint256);
    
    // Virtual function - can be overridden
    function virtualFunction() public virtual returns (string memory) {
        return "base implementation";
    }
    
    // Regular function
    function regularFunction() public pure returns (uint256) {
        return 42;
    }
}

// Interface for testing
interface ITestInterface {
    function interfaceFunction() external returns (bool);
}

// 2. VIRTUAL/OVERRIDE KEYWORDS DETECTION
contract VirtualOverrideContract is AbstractBase, ITestInterface {
    // Override abstract function
    function abstractFunction() public pure override returns (uint256) {
        return 123;
    }
    
    // Override virtual function
    function virtualFunction() public pure override returns (string memory) {
        return "overridden implementation";
    }
    
    // Implement interface function
    function interfaceFunction() external pure override returns (bool) {
        return true;
    }
    
    // Virtual function that can be overridden by child contracts
    function newVirtualFunction() public virtual returns (uint256) {
        return 456;
    }
}

// Child contract with more overrides
contract ChildContract is VirtualOverrideContract {
    // Override parent's virtual function
    function newVirtualFunction() public pure override returns (uint256) {
        return 789;
    }
    
    // Multiple inheritance override
    function virtualFunction() public pure override returns (string memory) {
        return "child implementation";
    }
}

// 3. TRANSIENT STORAGE DETECTION
contract TransientStorageContract {
    // Transient storage variables (EIP-1153) - simulated with naming convention
    uint256 private tempValueTransient;
    mapping(address => uint256) private tempBalancesTransient;
    bytes32 private tempHashTransient;
    
    // Regular storage for comparison
    uint256 private permanentValue;
    mapping(address => uint256) private permanentBalances;
    
    function useTransientStorage() external {
        // Transient storage operations
        tempValue = 100;
        tempBalances[msg.sender] = 200;
        tempHash = keccak256("temporary");
        
        // These values will be cleared at end of transaction
        uint256 temp = tempValue;
        uint256 balance = tempBalances[msg.sender];
        bytes32 hash = tempHash;
        
        // Regular storage operations
        permanentValue = temp + balance;
        permanentBalances[msg.sender] = temp;
    }
    
    function batchTransientOperations(address[] calldata users) external {
        for (uint256 i = 0; i < users.length; i++) {
            tempBalances[users[i]] = i * 100;
        }
        
        // Complex transient operations
        tempValue = tempBalances[users[0]] + tempBalances[users[1]];
        tempHash = keccak256(abi.encodePacked(tempValue, block.timestamp));
    }
    
    function getTransientValue() external view returns (uint256) {
        return tempValue;
    }
    
    function getTransientBalance(address user) external view returns (uint256) {
        return tempBalances[user];
    }
}

// 4. RECEIVE/FALLBACK FUNCTION DETECTION
contract ReceiveFallbackContract {
    event EtherReceived(address sender, uint256 amount, string method);
    event FallbackCalled(address sender, bytes data);
    
    uint256 public totalReceived;
    mapping(address => uint256) public balances;
    
    // Receive function - called when contract receives Ether with empty calldata
    receive() external payable {
        totalReceived += msg.value;
        balances[msg.sender] += msg.value;
        emit EtherReceived(msg.sender, msg.value, "receive");
    }
    
    // Fallback function - called when no other function matches or when receiving Ether with data
    fallback() external payable {
        totalReceived += msg.value;
        balances[msg.sender] += msg.value;
        emit FallbackCalled(msg.sender, msg.data);
        emit EtherReceived(msg.sender, msg.value, "fallback");
    }
    
    // Regular payable function for comparison
    function deposit() external payable {
        totalReceived += msg.value;
        balances[msg.sender] += msg.value;
        emit EtherReceived(msg.sender, msg.value, "deposit");
    }
    
    function withdraw(uint256 amount) external {
        require(balances[msg.sender] >= amount, "Insufficient balance");
        balances[msg.sender] -= amount;
        totalReceived -= amount;
        payable(msg.sender).transfer(amount);
    }
    
    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }
}

// Complex contract combining all Phase 1 features
abstract contract ComplexPhase1Contract is AbstractBase {
    // Transient storage
    uint256 transient private sessionData;
    mapping(bytes32 => bool) transient private sessionFlags;
    
    // Virtual functions with different access modifiers
    function internalVirtual() internal virtual returns (uint256) {
        return 1;
    }
    
    function protectedVirtual() internal virtual returns (uint256) {
        return 2;
    }
    
    // Abstract function with parameters
    function complexAbstract(uint256 a, string memory b) public virtual returns (bytes32);
}

contract ImplementationContract is ComplexPhase1Contract {
    event SessionStarted(bytes32 sessionId);
    event SessionEnded(bytes32 sessionId);
    
    // Override abstract function
    function abstractFunction() public pure override returns (uint256) {
        return 999;
    }
    
    // Override complex abstract function
    function complexAbstract(uint256 a, string memory b) public pure override returns (bytes32) {
        return keccak256(abi.encodePacked(a, b));
    }
    
    // Override internal virtual functions
    function internalVirtual() internal pure override returns (uint256) {
        return 10;
    }
    
    function protectedVirtual() internal pure override returns (uint256) {
        return 20;
    }
    
    // Receive function with transient storage
    receive() external payable {
        bytes32 sessionId = keccak256(abi.encodePacked(msg.sender, block.timestamp));
        sessionData = msg.value;
        sessionFlags[sessionId] = true;
        emit SessionStarted(sessionId);
    }
    
    // Fallback with complex logic
    fallback() external payable {
        if (msg.data.length > 0) {
            bytes32 sessionId = keccak256(msg.data);
            sessionFlags[sessionId] = false;
            emit SessionEnded(sessionId);
        }
    }
    
    function processSession() external {
        uint256 value = sessionData;
        sessionData = 0; // Clear transient storage
        
        // Use internal virtual functions
        uint256 result = internalVirtual() + protectedVirtual();
        require(result > 0, "Invalid session");
    }
}
