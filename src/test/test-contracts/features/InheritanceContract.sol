// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Inheritance Feature Test Contract
 * Tests all inheritance-related language features
 */

// Base contract
contract BaseContract {
    uint256 public baseValue;
    address public baseOwner;

    event BaseEvent(uint256 value);

    constructor(uint256 _baseValue) {
        baseValue = _baseValue;
        baseOwner = msg.sender;
    }

    function baseFunction() public virtual returns (string memory) {
        return "BaseContract";
    }

    function virtualFunction() public virtual returns (uint256) {
        return baseValue;
    }

    function setBaseValue(uint256 _value) public virtual {
        baseValue = _value;
        emit BaseEvent(_value);
    }
}

// Interface
interface ITestInterface {
    function interfaceFunction() external returns (bool);
    function getValue() external view returns (uint256);
}

// Abstract contract
abstract contract AbstractContract {
    uint256 public abstractValue;

    constructor(uint256 _value) {
        abstractValue = _value;
    }

    function concreteFunction() public view returns (uint256) {
        return abstractValue * 2;
    }

    function abstractFunction() public virtual returns (string memory);
}

// Contract with multiple inheritance
contract MiddleContract is BaseContract, AbstractContract {
    uint256 public middleValue;

    constructor(uint256 _baseValue, uint256 _abstractValue, uint256 _middleValue)
        BaseContract(_baseValue)
        AbstractContract(_abstractValue)
    {
        middleValue = _middleValue;
    }

    function abstractFunction() public pure override returns (string memory) {
        return "MiddleContract implementation";
    }

    function virtualFunction() public view virtual override returns (uint256) {
        return baseValue + abstractValue + middleValue;
    }

    function middleFunction() public virtual returns (string memory) {
        return "MiddleContract";
    }
}

// Contract implementing interface
contract InterfaceImplementation is ITestInterface {
    uint256 private value;

    constructor(uint256 _value) {
        value = _value;
    }

    function interfaceFunction() external pure override returns (bool) {
        return true;
    }

    function getValue() external view override returns (uint256) {
        return value;
    }
}

// Main inheritance contract
contract InheritanceContract is MiddleContract, ITestInterface {
    uint256 public derivedValue;
    bool public interfaceImplemented;

    // Override specifier with multiple parents
    constructor(uint256 _baseValue, uint256 _abstractValue, uint256 _middleValue, uint256 _derivedValue)
        MiddleContract(_baseValue, _abstractValue, _middleValue)
    {
        derivedValue = _derivedValue;
        interfaceImplemented = true;
    }

    // Override function from base contract
    function baseFunction() public pure override returns (string memory) {
        return "InheritanceContract";
    }

    // Override function with super call
    function virtualFunction() public view override returns (uint256) {
        return super.virtualFunction() + derivedValue;
    }

    // Override function from middle contract
    function middleFunction() public pure override returns (string memory) {
        return "InheritanceContract override";
    }

    // Implement interface functions
    function interfaceFunction() external pure override returns (bool) {
        return true;
    }

    function getValue() external view override returns (uint256) {
        return derivedValue;
    }

    // Function that calls parent functions
    function callParentFunctions()
        public
        view
        returns (string memory baseResult, uint256 virtualResult, string memory middleResult, uint256 concreteResult)
    {
        baseResult = super.baseFunction();
        virtualResult = super.virtualFunction();
        middleResult = super.middleFunction();
        concreteResult = concreteFunction();
    }

    // Override with different access modifier
    function setBaseValue(uint256 _value) public override {
        require(_value > 0, "Value must be positive");
        super.setBaseValue(_value);
    }

    // Function that demonstrates method resolution
    function demonstrateMethodResolution()
        public
        view
        returns (uint256 baseVal, uint256 abstractVal, uint256 middleVal, uint256 derivedVal, uint256 totalVal)
    {
        baseVal = baseValue;
        abstractVal = abstractValue;
        middleVal = middleValue;
        derivedVal = derivedValue;
        totalVal = virtualFunction();
    }
}

// Diamond inheritance pattern
contract DiamondBase {
    uint256 public diamondValue;

    constructor(uint256 _value) {
        diamondValue = _value;
    }

    function diamondFunction() public virtual returns (string memory) {
        return "DiamondBase";
    }
}

contract DiamondLeft is DiamondBase {
    constructor(uint256 _value) DiamondBase(_value) {}

    function diamondFunction() public pure virtual override returns (string memory) {
        return "DiamondLeft";
    }

    function leftFunction() public pure returns (string memory) {
        return "Left";
    }
}

contract DiamondRight is DiamondBase {
    constructor(uint256 _value) DiamondBase(_value) {}

    function diamondFunction() public pure virtual override returns (string memory) {
        return "DiamondRight";
    }

    function rightFunction() public pure returns (string memory) {
        return "Right";
    }
}

// Diamond inheritance resolution
contract DiamondInheritance is DiamondLeft, DiamondRight {
    constructor(uint256 _value) DiamondLeft(_value) DiamondRight(0) {}

    // Must override to resolve diamond inheritance
    function diamondFunction() public pure override(DiamondLeft, DiamondRight) returns (string memory) {
        return "DiamondInheritance";
    }

    function callBothSides() public pure returns (string memory left, string memory right) {
        left = leftFunction();
        right = rightFunction();
    }
}

// Contract with virtual and override keywords
contract VirtualOverrideContract is BaseContract {
    constructor(uint256 _value) BaseContract(_value) {}

    // Virtual function that can be overridden
    function newVirtualFunction() public virtual returns (string memory) {
        return "VirtualOverrideContract";
    }

    // Override with virtual (can be overridden again)
    function virtualFunction() public view virtual override returns (uint256) {
        return baseValue * 10;
    }
}

// Final inheritance level
contract FinalInheritanceContract is VirtualOverrideContract {
    constructor(uint256 _value) VirtualOverrideContract(_value) {}

    // Final override
    function newVirtualFunction() public pure override returns (string memory) {
        return "FinalInheritanceContract";
    }

    function virtualFunction() public view override returns (uint256) {
        return super.virtualFunction() + 1;
    }
}

// Contract demonstrating modifier inheritance
contract ModifierBase {
    address public owner;

    constructor() {
        owner = msg.sender;
    }

    modifier onlyOwner() virtual {
        require(msg.sender == owner, "Not owner");
        _;
    }

    function restrictedFunction() public view virtual onlyOwner returns (string memory) {
        return "Base restricted";
    }
}

contract ModifierInheritance is ModifierBase {
    // Override modifier
    modifier onlyOwner() override {
        require(msg.sender == owner, "Not owner - derived");
        _;
    }

    function restrictedFunction() public view override onlyOwner returns (string memory) {
        return "Derived restricted";
    }

    function newRestrictedFunction() public view onlyOwner returns (string memory) {
        return "New restricted";
    }
}
