// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Control Flow Feature Test Contract
 * Tests if/else, for, while, do-while, ternary operator
 */
contract ControlFlowContract {
    uint256 public counter;
    uint256[] public results;
    
    event LoopCompleted(string loopType, uint256 iterations);
    event ConditionalExecuted(string condition, uint256 value);
    
    constructor() {
        counter = 0;
    }
    
    // If-else statements
    function testIfElse(uint256 _value) public returns (string memory) {
        string memory result;
        
        if (_value == 0) {
            result = "zero";
            emit ConditionalExecuted("equals_zero", _value);
        } else if (_value < 10) {
            result = "single_digit";
            emit ConditionalExecuted("single_digit", _value);
        } else if (_value < 100) {
            result = "double_digit";
            emit ConditionalExecuted("double_digit", _value);
        } else {
            result = "large_number";
            emit ConditionalExecuted("large_number", _value);
        }
        
        return result;
    }
    
    // Nested if statements
    function testNestedIf(uint256 _x, uint256 _y) public pure returns (string memory) {
        if (_x > 0) {
            if (_y > 0) {
                if (_x > _y) {
                    return "x_greater_than_y";
                } else {
                    return "y_greater_or_equal_x";
                }
            } else {
                return "x_positive_y_non_positive";
            }
        } else {
            if (_y > 0) {
                return "x_non_positive_y_positive";
            } else {
                return "both_non_positive";
            }
        }
    }
    
    // For loops
    function testForLoop(uint256 _iterations) public {
        delete results; // Clear previous results
        
        for (uint256 i = 0; i < _iterations; i++) {
            results.push(i * 2);
            counter++;
        }
        
        emit LoopCompleted("for_loop", _iterations);
    }
    
    // For loop with break and continue
    function testForLoopWithBreakContinue(uint256 _limit) public {
        delete results;
        
        for (uint256 i = 0; i < _limit; i++) {
            if (i % 2 == 0) {
                continue; // Skip even numbers
            }
            
            if (i > 10) {
                break; // Stop if greater than 10
            }
            
            results.push(i);
        }
        
        emit LoopCompleted("for_loop_break_continue", results.length);
    }
    
    // Nested for loops
    function testNestedForLoops(uint256 _rows, uint256 _cols) public {
        delete results;
        
        for (uint256 i = 0; i < _rows; i++) {
            for (uint256 j = 0; j < _cols; j++) {
                results.push(i * _cols + j);
            }
        }
        
        emit LoopCompleted("nested_for_loops", _rows * _cols);
    }
    
    // While loop
    function testWhileLoop(uint256 _target) public {
        delete results;
        uint256 current = 1;
        
        while (current <= _target) {
            results.push(current);
            current *= 2;
        }
        
        emit LoopCompleted("while_loop", results.length);
    }
    
    // Do-while loop
    function testDoWhileLoop(uint256 _start, uint256 _increment) public {
        delete results;
        uint256 current = _start;
        
        do {
            results.push(current);
            current += _increment;
        } while (current < 100 && results.length < 10);
        
        emit LoopCompleted("do_while_loop", results.length);
    }
    
    // Ternary operator
    function testTernaryOperator(uint256 _a, uint256 _b) public pure returns (uint256) {
        return _a > _b ? _a : _b;
    }
    
    // Complex ternary operator
    function testComplexTernary(uint256 _value) public pure returns (string memory) {
        return _value > 100 
            ? (_value > 1000 ? "very_large" : "large")
            : (_value > 10 ? "medium" : "small");
    }
    
    // Ternary with function calls
    function testTernaryWithFunctionCalls(bool _condition, uint256 _x, uint256 _y) 
        public 
        pure 
        returns (uint256) 
    {
        return _condition ? multiply(_x, _y) : add(_x, _y);
    }
    
    function multiply(uint256 _a, uint256 _b) private pure returns (uint256) {
        return _a * _b;
    }
    
    function add(uint256 _a, uint256 _b) private pure returns (uint256) {
        return _a + _b;
    }
    
    // Complex control flow with multiple conditions
    function testComplexControlFlow(uint256 _value) public returns (uint256) {
        uint256 result = 0;
        
        if (_value > 0) {
            for (uint256 i = 1; i <= _value; i++) {
                if (i % 3 == 0 && i % 5 == 0) {
                    result += 15;
                } else if (i % 3 == 0) {
                    result += 3;
                } else if (i % 5 == 0) {
                    result += 5;
                } else {
                    result += i;
                }
            }
        }
        
        return result;
    }
    
    // Switch-like behavior using if-else
    function testSwitchLikeBehavior(uint256 _option) public pure returns (string memory) {
        if (_option == 1) {
            return "option_one";
        } else if (_option == 2) {
            return "option_two";
        } else if (_option == 3) {
            return "option_three";
        } else if (_option == 4) {
            return "option_four";
        } else {
            return "default_option";
        }
    }
    
    // Early return patterns
    function testEarlyReturn(uint256 _value) public pure returns (uint256) {
        if (_value == 0) {
            return 0;
        }
        
        if (_value == 1) {
            return 1;
        }
        
        if (_value < 10) {
            return _value * 2;
        }
        
        return _value * _value;
    }
    
    // Getter functions
    function getResultsLength() public view returns (uint256) {
        return results.length;
    }
    
    function getResult(uint256 _index) public view returns (uint256) {
        require(_index < results.length, "Index out of bounds");
        return results[_index];
    }
    
    function getAllResults() public view returns (uint256[] memory) {
        return results;
    }
}
