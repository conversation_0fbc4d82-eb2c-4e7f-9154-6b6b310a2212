// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Interface and Library Feature Test Contract
 * Tests interfaces, libraries, and their interactions
 */

// Basic interface
interface IERC20 {
    function totalSupply() external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function transfer(address to, uint256 amount) external returns (bool);
    function allowance(address owner, address spender) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transferFrom(address from, address to, uint256 amount) external returns (bool);

    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
}

// Interface with complex functions
interface IAdvanced {
    struct UserData {
        string name;
        uint256 score;
        bool active;
    }

    function processUser(UserData memory user) external returns (bool);
    function getUsers() external view returns (UserData[] memory);
    function updateScore(address user, uint256 newScore) external;
}

// Library for mathematical operations
library SafeMath {
    function add(uint256 a, uint256 b) internal pure returns (uint256) {
        uint256 c = a + b;
        require(c >= a, "SafeMath: addition overflow");
        return c;
    }

    function sub(uint256 a, uint256 b) internal pure returns (uint256) {
        require(b <= a, "SafeMath: subtraction overflow");
        return a - b;
    }

    function mul(uint256 a, uint256 b) internal pure returns (uint256) {
        if (a == 0) return 0;
        uint256 c = a * b;
        require(c / a == b, "SafeMath: multiplication overflow");
        return c;
    }

    function div(uint256 a, uint256 b) internal pure returns (uint256) {
        require(b > 0, "SafeMath: division by zero");
        return a / b;
    }
}

// Library for address operations
library AddressUtils {
    function isContract(address account) internal view returns (bool) {
        uint256 size;
        assembly {
            size := extcodesize(account)
        }
        return size > 0;
    }

    function sendValue(address payable recipient, uint256 amount) internal {
        require(address(this).balance >= amount, "Address: insufficient balance");
        (bool success,) = recipient.call{value: amount}("");
        require(success, "Address: unable to send value");
    }

    function functionCall(address target, bytes memory data) internal returns (bytes memory) {
        return functionCall(target, data, "Address: low-level call failed");
    }

    function functionCall(address target, bytes memory data, string memory errorMessage)
        internal
        returns (bytes memory)
    {
        require(isContract(target), "Address: call to non-contract");
        (bool success, bytes memory returndata) = target.call(data);
        return verifyCallResult(success, returndata, errorMessage);
    }

    function verifyCallResult(bool success, bytes memory returndata, string memory errorMessage)
        internal
        pure
        returns (bytes memory)
    {
        if (success) {
            return returndata;
        } else {
            if (returndata.length > 0) {
                assembly {
                    let returndata_size := mload(returndata)
                    revert(add(32, returndata), returndata_size)
                }
            } else {
                revert(errorMessage);
            }
        }
    }
}

// Contract implementing interfaces
contract InterfaceLibraryContract is IERC20, IAdvanced {
    using SafeMath for uint256;
    using AddressUtils for address;

    string public name = "Test Token";
    string public symbol = "TEST";
    uint8 public decimals = 18;
    uint256 private _totalSupply;

    mapping(address => uint256) private _balances;
    mapping(address => mapping(address => uint256)) private _allowances;
    mapping(address => UserData) private _users;
    UserData[] private _userList;

    constructor(uint256 _initialSupply) {
        _totalSupply = _initialSupply;
        _balances[msg.sender] = _initialSupply;
        emit Transfer(address(0), msg.sender, _initialSupply);
    }

    // IERC20 implementation
    function totalSupply() external view override returns (uint256) {
        return _totalSupply;
    }

    function balanceOf(address account) external view override returns (uint256) {
        return _balances[account];
    }

    function transfer(address to, uint256 amount) external override returns (bool) {
        _transfer(msg.sender, to, amount);
        return true;
    }

    function allowance(address owner, address spender) external view override returns (uint256) {
        return _allowances[owner][spender];
    }

    function approve(address spender, uint256 amount) external override returns (bool) {
        _approve(msg.sender, spender, amount);
        return true;
    }

    function transferFrom(address from, address to, uint256 amount) external override returns (bool) {
        uint256 currentAllowance = _allowances[from][msg.sender];
        require(currentAllowance >= amount, "ERC20: transfer amount exceeds allowance");

        _transfer(from, to, amount);
        _approve(from, msg.sender, currentAllowance.sub(amount));

        return true;
    }

    // IAdvanced implementation
    function processUser(UserData memory user) external override returns (bool) {
        _users[msg.sender] = user;
        _userList.push(user);
        return true;
    }

    function getUsers() external view override returns (UserData[] memory) {
        return _userList;
    }

    function updateScore(address user, uint256 newScore) external override {
        _users[user].score = newScore;

        // Update in array as well
        for (uint256 i = 0; i < _userList.length; i++) {
            if (keccak256(bytes(_userList[i].name)) == keccak256(bytes(_users[user].name))) {
                _userList[i].score = newScore;
                break;
            }
        }
    }

    // Internal functions using libraries
    function _transfer(address from, address to, uint256 amount) internal {
        require(from != address(0), "ERC20: transfer from the zero address");
        require(to != address(0), "ERC20: transfer to the zero address");

        uint256 fromBalance = _balances[from];
        require(fromBalance >= amount, "ERC20: transfer amount exceeds balance");

        _balances[from] = fromBalance.sub(amount);
        _balances[to] = _balances[to].add(amount);

        emit Transfer(from, to, amount);
    }

    function _approve(address owner, address spender, uint256 amount) internal {
        require(owner != address(0), "ERC20: approve from the zero address");
        require(spender != address(0), "ERC20: approve to the zero address");

        _allowances[owner][spender] = amount;
        emit Approval(owner, spender, amount);
    }

    // Functions demonstrating library usage
    function safeMathOperations(uint256 a, uint256 b)
        external
        pure
        returns (uint256 sum, uint256 difference, uint256 product, uint256 quotient)
    {
        sum = a.add(b);
        difference = a.sub(b);
        product = a.mul(b);
        quotient = a.div(b);
    }

    function checkIfContract(address addr) external view returns (bool) {
        return AddressUtils.isContract(addr);
    }

    function sendEther(address payable recipient, uint256 amount) external {
        AddressUtils.sendValue(recipient, amount);
    }

    function callContract(address target, bytes memory data) external returns (bytes memory) {
        return AddressUtils.functionCall(target, data);
    }

    // Interface type usage
    function interactWithERC20(IERC20 token, address to, uint256 amount) external {
        require(token.transfer(to, amount), "Transfer failed");
    }

    function checkERC20Balance(IERC20 token, address account) external view returns (uint256) {
        return token.balanceOf(account);
    }

    // Multiple interface support
    function supportsInterface(bytes4 interfaceId) external pure returns (bool) {
        return interfaceId == type(IERC20).interfaceId || interfaceId == type(IAdvanced).interfaceId;
    }

    // Getter functions
    function getUserData(address user) external view returns (UserData memory) {
        return _users[user];
    }

    function getUserCount() external view returns (uint256) {
        return _userList.length;
    }
}
