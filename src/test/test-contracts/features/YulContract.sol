// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Yul (Inline Assembly) Feature Test Contract
 * Tests Yul/assembly blocks and operations
 */
contract YulContract {
    uint256 public result;
    bytes32 public hashResult;
    address public addressResult;
    
    event AssemblyOperation(string operation, uint256 result);
    event MemoryOperation(string operation, bytes data);
    
    // Basic arithmetic operations in assembly
    function assemblyAdd(uint256 a, uint256 b) public returns (uint256) {
        uint256 sum;
        assembly {
            sum := add(a, b)
        }
        result = sum;
        emit AssemblyOperation("add", sum);
        return sum;
    }
    
    function assemblyMul(uint256 a, uint256 b) public returns (uint256) {
        uint256 product;
        assembly {
            product := mul(a, b)
        }
        result = product;
        emit AssemblyOperation("mul", product);
        return product;
    }
    
    function assemblySub(uint256 a, uint256 b) public returns (uint256) {
        uint256 difference;
        assembly {
            difference := sub(a, b)
        }
        result = difference;
        emit AssemblyOperation("sub", difference);
        return difference;
    }
    
    function assemblyDiv(uint256 a, uint256 b) public returns (uint256) {
        uint256 quotient;
        assembly {
            quotient := div(a, b)
        }
        result = quotient;
        emit AssemblyOperation("div", quotient);
        return quotient;
    }
    
    // Bitwise operations
    function assemblyBitwise(uint256 a, uint256 b) public returns (uint256, uint256, uint256) {
        uint256 andResult;
        uint256 orResult;
        uint256 xorResult;
        
        assembly {
            andResult := and(a, b)
            orResult := or(a, b)
            xorResult := xor(a, b)
        }
        
        return (andResult, orResult, xorResult);
    }
    
    function assemblyShift(uint256 value, uint256 positions) public returns (uint256, uint256) {
        uint256 leftShift;
        uint256 rightShift;
        
        assembly {
            leftShift := shl(positions, value)
            rightShift := shr(positions, value)
        }
        
        return (leftShift, rightShift);
    }
    
    // Memory operations
    function assemblyMemoryOperations() public returns (bytes memory) {
        bytes memory data;
        
        assembly {
            // Allocate memory
            data := mload(0x40)
            mstore(0x40, add(data, 0x60)) // Update free memory pointer
            
            // Store data in memory
            mstore(data, 0x40) // Store length (64 bytes)
            mstore(add(data, 0x20), 0x48656c6c6f20576f726c64) // "Hello World"
            mstore(add(data, 0x40), 0x66726f6d20417373656d626c79) // "from Assembly"
        }
        
        emit MemoryOperation("memory_write", data);
        return data;
    }
    
    // Storage operations
    function assemblyStorageOperations(uint256 value) public {
        assembly {
            // Store value in storage slot 0 (result variable)
            sstore(0, value)
        }
        emit AssemblyOperation("storage_write", value);
    }
    
    function assemblyStorageRead() public view returns (uint256) {
        uint256 value;
        assembly {
            // Read from storage slot 0
            value := sload(0)
        }
        return value;
    }
    
    // Hash operations
    function assemblyKeccak(bytes memory data) public returns (bytes32) {
        bytes32 hash;
        assembly {
            hash := keccak256(add(data, 0x20), mload(data))
        }
        hashResult = hash;
        return hash;
    }
    
    function assemblySha3(uint256 a, uint256 b) public returns (bytes32) {
        bytes32 hash;
        assembly {
            // Store values in memory and hash them
            let memPtr := mload(0x40)
            mstore(memPtr, a)
            mstore(add(memPtr, 0x20), b)
            hash := keccak256(memPtr, 0x40)
        }
        hashResult = hash;
        return hash;
    }
    
    // Control flow in assembly
    function assemblyConditional(uint256 a, uint256 b) public returns (uint256) {
        uint256 result_;
        assembly {
            switch gt(a, b)
            case 1 {
                result_ := a
            }
            default {
                result_ := b
            }
        }
        result = result_;
        return result_;
    }
    
    function assemblyLoop(uint256 n) public returns (uint256) {
        uint256 sum;
        assembly {
            for { let i := 0 } lt(i, n) { i := add(i, 1) } {
                sum := add(sum, i)
            }
        }
        result = sum;
        emit AssemblyOperation("loop_sum", sum);
        return sum;
    }
    
    // External call operations
    function assemblyExternalCall(address target, bytes memory data) 
        public 
        returns (bool success, bytes memory returnData) 
    {
        assembly {
            success := call(
                gas(),           // gas
                target,          // address
                0,               // value
                add(data, 0x20), // input data pointer
                mload(data),     // input data size
                0,               // output data pointer
                0                // output data size
            )
            
            // Get return data
            let returnDataSize := returndatasize()
            returnData := mload(0x40)
            mstore(0x40, add(returnData, add(returnDataSize, 0x20)))
            mstore(returnData, returnDataSize)
            returndatacopy(add(returnData, 0x20), 0, returnDataSize)
        }
    }
    
    // Address operations
    function assemblyAddressOperations() public returns (address, uint256, bytes32) {
        address currentAddress;
        uint256 balance;
        bytes32 codeHash;
        
        assembly {
            currentAddress := address()
            balance := balance(currentAddress)
            
            // Get code hash
            let size := extcodesize(currentAddress)
            if gt(size, 0) {
                let code := mload(0x40)
                mstore(0x40, add(code, size))
                extcodecopy(currentAddress, code, 0, size)
                codeHash := keccak256(code, size)
            }
        }
        
        addressResult = currentAddress;
        return (currentAddress, balance, codeHash);
    }
    
    // Block and transaction information
    function assemblyBlockInfo() public view returns (
        uint256 blockNumber,
        uint256 timestamp,
        address coinbase,
        uint256 difficulty,
        uint256 gasLimit
    ) {
        assembly {
            blockNumber := number()
            timestamp := timestamp()
            coinbase := coinbase()
            difficulty := difficulty()
            gasLimit := gaslimit()
        }
    }
    
    function assemblyTxInfo() public view returns (
        address origin,
        uint256 gasPrice,
        uint256 gasLeft
    ) {
        assembly {
            origin := origin()
            gasPrice := gasprice()
            gasLeft := gas()
        }
    }
    
    // Message information
    function assemblyMsgInfo() public payable returns (
        address sender,
        uint256 value,
        bytes memory data
    ) {
        assembly {
            sender := caller()
            value := callvalue()
            
            // Copy call data
            let dataSize := calldatasize()
            data := mload(0x40)
            mstore(0x40, add(data, add(dataSize, 0x20)))
            mstore(data, dataSize)
            calldatacopy(add(data, 0x20), 0, dataSize)
        }
    }
    
    // Complex assembly function with multiple operations
    function assemblyComplexOperation(uint256[] memory arr) public returns (uint256) {
        uint256 sum;
        uint256 product = 1;
        
        assembly {
            let length := mload(arr)
            let dataPtr := add(arr, 0x20)
            
            for { let i := 0 } lt(i, length) { i := add(i, 1) } {
                let value := mload(add(dataPtr, mul(i, 0x20)))
                sum := add(sum, value)
                
                // Avoid overflow in product calculation
                if and(gt(value, 0), lt(product, div(not(0), value))) {
                    product := mul(product, value)
                }
            }
        }
        
        result = sum;
        emit AssemblyOperation("complex_array_operation", sum);
        return sum;
    }
    
    // Assembly with function definitions
    function assemblyWithFunctions(uint256 a, uint256 b) public returns (uint256) {
        uint256 result_;
        
        assembly {
            function max(x, y) -> maximum {
                maximum := y
                if gt(x, y) { maximum := x }
            }
            
            function min(x, y) -> minimum {
                minimum := x
                if gt(x, y) { minimum := y }
            }
            
            result_ := add(max(a, b), min(a, b))
        }
        
        result = result_;
        return result_;
    }
    
    // Low-level operations
    function assemblyLowLevel() public returns (bytes32) {
        bytes32 result_;
        
        assembly {
            // Create a unique hash based on current state
            let memPtr := mload(0x40)
            mstore(memPtr, caller())
            mstore(add(memPtr, 0x20), timestamp())
            mstore(add(memPtr, 0x40), number())
            mstore(add(memPtr, 0x60), address())
            
            result_ := keccak256(memPtr, 0x80)
        }
        
        hashResult = result_;
        return result_;
    }
    
    // Getter functions
    function getResult() public view returns (uint256) {
        return result;
    }
    
    function getHashResult() public view returns (bytes32) {
        return hashResult;
    }
    
    function getAddressResult() public view returns (address) {
        return addressResult;
    }
}
