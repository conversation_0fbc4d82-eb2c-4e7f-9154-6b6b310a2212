// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Struct Feature Test Contract
 * Tests all struct-related language features
 */
contract StructContract {
    // Basic struct
    struct Person {
        string name;
        uint256 age;
        bool isActive;
    }

    // Nested struct
    struct Address {
        string street;
        string city;
        string country;
        uint256 zipCode;
    }

    struct Employee {
        Person personalInfo;
        Address homeAddress;
        uint256 salary;
        uint256 startDate;
        string department;
    }

    // Struct with arrays
    struct Project {
        string name;
        uint256[] milestones;
        address[] contributors;
        mapping(address => uint256) contributions;
        bool isCompleted;
    }

    // Struct with mappings
    struct Company {
        string name;
        mapping(address => Employee) employees;
        mapping(string => Project) projects;
        uint256 totalEmployees;
        address owner;
    }

    // Complex struct with multiple data types
    struct ComplexData {
        uint256 id;
        bytes32 hash;
        bytes data;
        uint256[] numbers;
        string[] tags;
        mapping(string => uint256) metadata;
        Person[] people;
        bool isValid;
    }

    // State variables using structs
    Person public owner;
    Company public company;
    mapping(uint256 => Employee) public employees;
    mapping(address => Person) public persons;
    Employee[] public employeeList;
    Project[] public projects;

    // Events with struct parameters (only structs without mappings)
    event PersonCreated(Person person);
    event EmployeeHired(uint256 indexed employeeId, Employee employee);
    event ProjectCreated(string indexed projectName, uint256 milestoneCount);

    constructor() {
        owner = Person({name: "Contract Owner", age: 30, isActive: true});

        // Initialize company fields individually since it contains mappings
        company.name = "Blockchain Corp";
        company.totalEmployees = 0;
        company.owner = msg.sender;
    }

    // Functions with struct parameters
    function createPerson(string memory _name, uint256 _age, bool _isActive) public returns (Person memory) {
        Person memory newPerson = Person({name: _name, age: _age, isActive: _isActive});

        persons[msg.sender] = newPerson;
        emit PersonCreated(newPerson);

        return newPerson;
    }

    function hireEmployee(
        string memory _name,
        uint256 _age,
        string memory _street,
        string memory _city,
        string memory _country,
        uint256 _zipCode,
        uint256 _salary,
        string memory _department
    ) public returns (uint256) {
        Person memory personalInfo = Person({name: _name, age: _age, isActive: true});

        Address memory homeAddress = Address({street: _street, city: _city, country: _country, zipCode: _zipCode});

        Employee memory newEmployee = Employee({
            personalInfo: personalInfo,
            homeAddress: homeAddress,
            salary: _salary,
            startDate: block.timestamp,
            department: _department
        });

        uint256 employeeId = company.totalEmployees;
        employees[employeeId] = newEmployee;
        employeeList.push(newEmployee);
        company.employees[msg.sender] = newEmployee;
        company.totalEmployees++;

        emit EmployeeHired(employeeId, newEmployee);

        return employeeId;
    }

    // Functions returning structs
    function getEmployee(uint256 _employeeId) public view returns (Employee memory) {
        return employees[_employeeId];
    }

    function getPerson(address _addr) public view returns (Person memory) {
        return persons[_addr];
    }

    // Functions modifying struct fields
    function updatePersonAge(address _addr, uint256 _newAge) public {
        persons[_addr].age = _newAge;
    }

    function updateEmployeeSalary(uint256 _employeeId, uint256 _newSalary) public {
        employees[_employeeId].salary = _newSalary;
    }

    function deactivatePerson(address _addr) public {
        persons[_addr].isActive = false;
    }

    // Functions with struct arrays
    function getAllEmployees() public view returns (Employee[] memory) {
        return employeeList;
    }

    function getEmployeeCount() public view returns (uint256) {
        return employeeList.length;
    }

    // Functions with nested struct access
    function getEmployeeName(uint256 _employeeId) public view returns (string memory) {
        return employees[_employeeId].personalInfo.name;
    }

    function getEmployeeCity(uint256 _employeeId) public view returns (string memory) {
        return employees[_employeeId].homeAddress.city;
    }

    // Functions with struct initialization patterns
    function createEmployeeInline(string memory _name, uint256 _age, uint256 _salary) public returns (uint256) {
        uint256 employeeId = company.totalEmployees;

        // Direct struct assignment
        employees[employeeId] = Employee({
            personalInfo: Person(_name, _age, true),
            homeAddress: Address("Unknown", "Unknown", "Unknown", 0),
            salary: _salary,
            startDate: block.timestamp,
            department: "General"
        });

        company.totalEmployees++;
        return employeeId;
    }

    // Functions with struct copying
    function copyEmployee(uint256 _fromId, uint256 _toId) public {
        employees[_toId] = employees[_fromId];
    }

    function clonePerson(address _from, address _to) public {
        persons[_to] = persons[_from];
    }

    // Functions with struct deletion
    function deleteEmployee(uint256 _employeeId) public {
        delete employees[_employeeId];
    }

    function deletePerson(address _addr) public {
        delete persons[_addr];
    }

    // Functions with struct comparison (manual)
    function comparePersons(address _addr1, address _addr2) public view returns (bool) {
        Person memory p1 = persons[_addr1];
        Person memory p2 = persons[_addr2];

        return
            (keccak256(bytes(p1.name)) == keccak256(bytes(p2.name)) && p1.age == p2.age && p1.isActive == p2.isActive);
    }

    // Functions with struct as storage reference
    function updatePersonInStorage(address _addr, string memory _newName) public {
        Person storage person = persons[_addr];
        person.name = _newName;
    }

    function updateEmployeeInStorage(uint256 _employeeId, string memory _newDepartment) public {
        Employee storage employee = employees[_employeeId];
        employee.department = _newDepartment;
    }

    // Functions with struct memory operations
    function processPersonInMemory(address _addr) public view returns (string memory) {
        Person memory person = persons[_addr];
        person.age += 1; // This doesn't affect storage

        return string(abi.encodePacked("Processed: ", person.name, " Age: ", uint2str(person.age)));
    }

    // Utility function for string conversion
    function uint2str(uint256 _i) internal pure returns (string memory) {
        if (_i == 0) {
            return "0";
        }
        uint256 j = _i;
        uint256 len;
        while (j != 0) {
            len++;
            j /= 10;
        }
        bytes memory bstr = new bytes(len);
        uint256 k = len;
        while (_i != 0) {
            k = k - 1;
            uint8 temp = (48 + uint8(_i - _i / 10 * 10));
            bytes1 b1 = bytes1(temp);
            bstr[k] = b1;
            _i /= 10;
        }
        return string(bstr);
    }

    // Functions with struct packing/unpacking
    function packPerson(Person memory _person) public pure returns (bytes memory) {
        return abi.encode(_person.name, _person.age, _person.isActive);
    }

    function unpackPerson(bytes memory _data) public pure returns (Person memory) {
        (string memory name, uint256 age, bool isActive) = abi.decode(_data, (string, uint256, bool));
        return Person(name, age, isActive);
    }
}
