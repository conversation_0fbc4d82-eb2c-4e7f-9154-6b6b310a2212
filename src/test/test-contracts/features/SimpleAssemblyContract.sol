// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract SimpleAssemblyContract {
    uint256 public value;
    
    function simpleAssembly(uint256 a, uint256 b) external returns (uint256 result) {
        assembly {
            result := add(a, b)
        }
    }
    
    function anotherAssembly(uint256 x) external returns (uint256 doubled) {
        assembly {
            doubled := mul(x, 2)
        }
    }
}
