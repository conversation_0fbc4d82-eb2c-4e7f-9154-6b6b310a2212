// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * ABI Encoding, Create2, and Proxy Patterns Test Contract
 * Tests ABI encoding/decoding, Create2 deployment, and proxy patterns
 */
contract ABICreate2ProxyContract {
    // State variables
    address public implementation;
    address public admin;
    mapping(bytes32 => address) public deployedContracts;
    mapping(address => bool) public isProxy;
    
    // Events
    event ABIOperation(string operation, uint256 dataLength);
    event ContractDeployed(address indexed contractAddress, bytes32 indexed salt);
    event ProxyUpgraded(address indexed oldImplementation, address indexed newImplementation);
    event ProxyCall(address indexed target, bytes data, bool success);
    
    constructor() {
        admin = msg.sender;
    }
    
    modifier onlyAdmin() {
        require(msg.sender == admin, "Only admin");
        _;
    }
    
    // ABI Encoding operations
    function abiEncodeBasic(
        uint256 number,
        string memory text,
        address addr,
        bool flag
    ) external returns (bytes memory encoded) {
        encoded = abi.encode(number, text, addr, flag);
        emit ABIOperation("encode", encoded.length);
    }
    
    function abiEncodePacked(
        uint256 number,
        string memory text,
        address addr
    ) external returns (bytes memory packed) {
        packed = abi.encodePacked(number, text, addr);
        emit ABIOperation("encodePacked", packed.length);
    }
    
    function abiEncodeWithSelector(
        bytes4 selector,
        uint256 number,
        string memory text
    ) external returns (bytes memory encoded) {
        encoded = abi.encodeWithSelector(selector, number, text);
        emit ABIOperation("encodeWithSelector", encoded.length);
    }
    
    function abiEncodeWithSignature(
        string memory signature,
        uint256 number,
        address addr
    ) external returns (bytes memory encoded) {
        encoded = abi.encodeWithSignature(signature, number, addr);
        emit ABIOperation("encodeWithSignature", encoded.length);
    }
    
    function abiEncodeCall() external returns (bytes memory encoded) {
        // Encode a call to this contract's function
        encoded = abi.encodeCall(this.abiEncodeBasic, (123, "test", address(this), true));
        emit ABIOperation("encodeCall", encoded.length);
    }
    
    // ABI Decoding operations
    function abiDecodeBasic(bytes memory data) external returns (
        uint256 number,
        string memory text,
        address addr,
        bool flag
    ) {
        (number, text, addr, flag) = abi.decode(data, (uint256, string, address, bool));
        emit ABIOperation("decode", data.length);
    }
    
    function abiDecodeComplex(bytes memory data) external returns (
        uint256[] memory numbers,
        string[] memory texts,
        address[] memory addresses
    ) {
        (numbers, texts, addresses) = abi.decode(data, (uint256[], string[], address[]));
        emit ABIOperation("decodeComplex", data.length);
    }
    
    // Function selector operations
    function getFunctionSelector(string memory signature) external pure returns (bytes4) {
        return bytes4(keccak256(bytes(signature)));
    }
    
    function extractSelector(bytes memory data) external pure returns (bytes4 selector) {
        require(data.length >= 4, "Data too short");
        assembly {
            selector := mload(add(data, 32))
        }
    }
    
    // Create2 operations
    function computeCreate2Address(
        bytes32 salt,
        bytes32 bytecodeHash,
        address deployer
    ) public pure returns (address) {
        return address(uint160(uint256(keccak256(abi.encodePacked(
            bytes1(0xff),
            deployer,
            salt,
            bytecodeHash
        )))));
    }
    
    function deployWithCreate2(
        bytes memory bytecode,
        bytes32 salt
    ) external onlyAdmin returns (address deployedAddress) {
        assembly {
            deployedAddress := create2(0, add(bytecode, 0x20), mload(bytecode), salt)
        }
        
        require(deployedAddress != address(0), "Create2 deployment failed");
        deployedContracts[salt] = deployedAddress;
        
        emit ContractDeployed(deployedAddress, salt);
    }
    
    function deployProxyWithCreate2(
        bytes32 salt,
        address implementationAddress
    ) external onlyAdmin returns (address proxyAddress) {
        // Simple proxy bytecode
        bytes memory bytecode = abi.encodePacked(
            hex"3d602d80600a3d3981f3363d3d373d3d3d363d73",
            implementationAddress,
            hex"5af43d82803e903d91602b57fd5bf3"
        );
        
        assembly {
            proxyAddress := create2(0, add(bytecode, 0x20), mload(bytecode), salt)
        }
        
        require(proxyAddress != address(0), "Proxy deployment failed");
        deployedContracts[salt] = proxyAddress;
        isProxy[proxyAddress] = true;
        
        emit ContractDeployed(proxyAddress, salt);
    }
    
    function predictCreate2Address(
        bytes memory bytecode,
        bytes32 salt
    ) external view returns (address predicted) {
        bytes32 bytecodeHash = keccak256(bytecode);
        predicted = computeCreate2Address(salt, bytecodeHash, address(this));
    }
    
    // Proxy pattern implementations
    function upgradeImplementation(address newImplementation) external onlyAdmin {
        require(newImplementation != address(0), "Invalid implementation");
        
        address oldImplementation = implementation;
        implementation = newImplementation;
        
        emit ProxyUpgraded(oldImplementation, newImplementation);
    }
    
    function proxyCall(bytes memory data) external returns (bytes memory result) {
        require(implementation != address(0), "No implementation set");
        
        (bool success, bytes memory returnData) = implementation.delegatecall(data);
        
        emit ProxyCall(implementation, data, success);
        
        if (!success) {
            // Bubble up the revert reason
            if (returnData.length > 0) {
                assembly {
                    let returnDataSize := mload(returnData)
                    revert(add(32, returnData), returnDataSize)
                }
            } else {
                revert("Proxy call failed");
            }
        }
        
        return returnData;
    }
    
    // Transparent proxy pattern
    function transparentProxyCall(
        address target,
        bytes memory data
    ) external returns (bytes memory result) {
        require(msg.sender != admin, "Admin cannot call implementation");
        
        (bool success, bytes memory returnData) = target.delegatecall(data);
        
        emit ProxyCall(target, data, success);
        
        if (!success) {
            assembly {
                let returnDataSize := mload(returnData)
                revert(add(32, returnData), returnDataSize)
            }
        }
        
        return returnData;
    }
    
    // UUPS proxy pattern
    function upgradeToAndCall(
        address newImplementation,
        bytes memory data
    ) external onlyAdmin {
        implementation = newImplementation;
        
        if (data.length > 0) {
            (bool success, ) = newImplementation.delegatecall(data);
            require(success, "Upgrade call failed");
        }
        
        emit ProxyUpgraded(implementation, newImplementation);
    }
    
    // Beacon proxy pattern
    mapping(address => address) public beacons;
    
    function setBeacon(address beacon, address implementationAddress) external onlyAdmin {
        beacons[beacon] = implementationAddress;
    }
    
    function beaconProxyCall(
        address beacon,
        bytes memory data
    ) external returns (bytes memory result) {
        address target = beacons[beacon];
        require(target != address(0), "Beacon not set");
        
        (bool success, bytes memory returnData) = target.delegatecall(data);
        
        emit ProxyCall(target, data, success);
        
        if (!success) {
            assembly {
                let returnDataSize := mload(returnData)
                revert(add(32, returnData), returnDataSize)
            }
        }
        
        return returnData;
    }
    
    // Diamond proxy pattern (simplified)
    struct Facet {
        address facetAddress;
        bytes4[] functionSelectors;
    }
    
    mapping(bytes4 => address) public selectorToFacet;
    
    function diamondCut(Facet[] memory facets) external onlyAdmin {
        for (uint256 i = 0; i < facets.length; i++) {
            for (uint256 j = 0; j < facets[i].functionSelectors.length; j++) {
                selectorToFacet[facets[i].functionSelectors[j]] = facets[i].facetAddress;
            }
        }
    }
    
    function diamondProxyCall(bytes memory data) external returns (bytes memory result) {
        bytes4 selector;
        assembly {
            selector := mload(add(data, 32))
        }
        
        address target = selectorToFacet[selector];
        require(target != address(0), "Function not found");
        
        (bool success, bytes memory returnData) = target.delegatecall(data);
        
        emit ProxyCall(target, data, success);
        
        if (!success) {
            assembly {
                let returnDataSize := mload(returnData)
                revert(add(32, returnData), returnDataSize)
            }
        }
        
        return returnData;
    }
    
    // Fallback and receive for proxy functionality
    fallback() external payable {
        if (implementation != address(0)) {
            assembly {
                let ptr := mload(0x40)
                calldatacopy(ptr, 0, calldatasize())
                let result := delegatecall(gas(), sload(implementation.slot), ptr, calldatasize(), 0, 0)
                let size := returndatasize()
                returndatacopy(ptr, 0, size)
                
                switch result
                case 0 { revert(ptr, size) }
                default { return(ptr, size) }
            }
        }
    }
    
    receive() external payable {
        // Handle plain ether transfers
    }
    
    // Utility functions
    function getDeployedContract(bytes32 salt) external view returns (address) {
        return deployedContracts[salt];
    }
    
    function isContractProxy(address contractAddress) external view returns (bool) {
        return isProxy[contractAddress];
    }
    
    function getImplementation() external view returns (address) {
        return implementation;
    }
    
    function getFacetAddress(bytes4 selector) external view returns (address) {
        return selectorToFacet[selector];
    }
}
