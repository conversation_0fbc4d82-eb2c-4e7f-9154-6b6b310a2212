// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Bytes Operations, Cryptographic Functions, and Block Properties Test Contract
 * Tests detailed bytes manipulation, hash functions, and blockchain properties
 */
contract BytesCryptoBlockContract {
    // State variables for testing
    bytes public storedBytes;
    bytes32 public storedHash;
    mapping(bytes32 => bytes) public hashToData;
    mapping(address => bytes32) public userHashes;
    
    // Block property tracking
    uint256 public deploymentBlock;
    uint256 public deploymentTimestamp;
    bytes32 public deploymentBlockHash;
    
    // Events
    event BytesOperation(string operation, uint256 inputLength, uint256 outputLength);
    event CryptoOperation(string operation, bytes32 hash, uint256 inputLength);
    event BlockPropertyAccessed(string property, uint256 value);
    event SignatureVerified(address signer, bool valid);
    
    constructor() {
        deploymentBlock = block.number;
        deploymentTimestamp = block.timestamp;
        if (block.number > 0) {
            deploymentBlockHash = blockhash(block.number - 1);
        }
    }
    
    // Bytes operations
    function bytesBasicOperations(bytes memory input) external returns (bytes memory) {
        // Store input
        storedBytes = input;
        
        // Get length
        uint256 length = input.length;
        
        // Create new bytes array
        bytes memory output = new bytes(length * 2);
        
        // Copy input twice
        for (uint256 i = 0; i < length; i++) {
            output[i] = input[i];
            output[i + length] = input[i];
        }
        
        emit BytesOperation("duplicate", length, output.length);
        return output;
    }
    
    function bytesSlicing(bytes memory input, uint256 start, uint256 end) 
        external 
        returns (bytes memory sliced) 
    {
        require(start < end && end <= input.length, "Invalid slice parameters");
        
        uint256 sliceLength = end - start;
        sliced = new bytes(sliceLength);
        
        for (uint256 i = 0; i < sliceLength; i++) {
            sliced[i] = input[start + i];
        }
        
        emit BytesOperation("slice", input.length, sliced.length);
    }
    
    function bytesConcatenation(bytes memory a, bytes memory b) 
        external 
        returns (bytes memory concatenated) 
    {
        concatenated = abi.encodePacked(a, b);
        emit BytesOperation("concatenate", a.length + b.length, concatenated.length);
    }
    
    function bytesComparison(bytes memory a, bytes memory b) external returns (bool equal) {
        equal = keccak256(a) == keccak256(b);
        emit BytesOperation("compare", a.length, equal ? 1 : 0);
    }
    
    function bytesToTypes(bytes memory input) external returns (
        uint256 asUint256,
        address asAddress,
        bytes32 asBytes32,
        string memory asString
    ) {
        require(input.length >= 32, "Input too short for uint256/bytes32");
        
        // Convert to uint256
        assembly {
            asUint256 := mload(add(input, 32))
        }
        
        // Convert to bytes32
        assembly {
            asBytes32 := mload(add(input, 32))
        }
        
        // Convert to address (last 20 bytes)
        if (input.length >= 20) {
            assembly {
                asAddress := mload(add(input, 20))
            }
        }
        
        // Convert to string
        asString = string(input);
        
        emit BytesOperation("convert_types", input.length, 4);
    }
    
    function typesToBytes(
        uint256 number,
        address addr,
        bytes32 hash,
        string memory text
    ) external returns (bytes memory packed) {
        packed = abi.encodePacked(number, addr, hash, text);
        emit BytesOperation("pack_types", 4, packed.length);
    }
    
    // Cryptographic operations
    function hashOperations(bytes memory input) external returns (
        bytes32 keccakHash,
        bytes32 sha256Hash,
        bytes20 ripemd160Hash
    ) {
        // Keccak256 (most common in Ethereum)
        keccakHash = keccak256(input);
        
        // SHA256
        sha256Hash = sha256(input);
        
        // RIPEMD160
        ripemd160Hash = ripemd160(input);
        
        // Store hash mapping
        hashToData[keccakHash] = input;
        storedHash = keccakHash;
        
        emit CryptoOperation("keccak256", keccakHash, input.length);
        emit CryptoOperation("sha256", sha256Hash, input.length);
        emit CryptoOperation("ripemd160", bytes32(ripemd160Hash), input.length);
    }
    
    function multipleHashOperations(bytes memory input) external returns (bytes32 finalHash) {
        // Chain multiple hash operations
        bytes32 hash1 = keccak256(input);
        bytes32 hash2 = sha256(abi.encodePacked(hash1));
        bytes32 hash3 = keccak256(abi.encodePacked(hash2, input));
        finalHash = keccak256(abi.encodePacked(hash1, hash2, hash3));
        
        emit CryptoOperation("chained_hash", finalHash, input.length);
    }
    
    function saltedHash(bytes memory input, bytes32 salt) external returns (bytes32 saltedHash) {
        saltedHash = keccak256(abi.encodePacked(salt, input, salt));
        userHashes[msg.sender] = saltedHash;
        
        emit CryptoOperation("salted_hash", saltedHash, input.length);
    }
    
    function merkleProofVerification(
        bytes32 leaf,
        bytes32[] memory proof,
        bytes32 root
    ) external returns (bool valid) {
        bytes32 computedHash = leaf;
        
        for (uint256 i = 0; i < proof.length; i++) {
            bytes32 proofElement = proof[i];
            if (computedHash <= proofElement) {
                computedHash = keccak256(abi.encodePacked(computedHash, proofElement));
            } else {
                computedHash = keccak256(abi.encodePacked(proofElement, computedHash));
            }
        }
        
        valid = computedHash == root;
        emit CryptoOperation("merkle_proof", computedHash, proof.length);
    }
    
    // Signature operations
    function signatureVerification(
        bytes32 messageHash,
        uint8 v,
        bytes32 r,
        bytes32 s
    ) external returns (address signer, bool valid) {
        // Recover signer
        signer = ecrecover(messageHash, v, r, s);
        valid = signer != address(0);
        
        emit SignatureVerified(signer, valid);
        emit CryptoOperation("signature_verify", messageHash, valid ? 1 : 0);
    }
    
    function ethSignedMessageHash(bytes32 messageHash) external pure returns (bytes32) {
        return keccak256(abi.encodePacked("\x19Ethereum Signed Message:\n32", messageHash));
    }
    
    function personalSign(string memory message) external pure returns (bytes32) {
        bytes memory messageBytes = bytes(message);
        return keccak256(abi.encodePacked(
            "\x19Ethereum Signed Message:\n",
            uintToString(messageBytes.length),
            message
        ));
    }
    
    // Block and transaction properties
    function currentBlockProperties() external returns (
        uint256 blockNumber,
        uint256 timestamp,
        uint256 gasLimit,
        uint256 difficulty,
        address coinbase
    ) {
        blockNumber = block.number;
        timestamp = block.timestamp;
        gasLimit = block.gaslimit;
        difficulty = block.difficulty;
        coinbase = block.coinbase;
        
        emit BlockPropertyAccessed("block.number", blockNumber);
        emit BlockPropertyAccessed("block.timestamp", timestamp);
        emit BlockPropertyAccessed("block.gaslimit", gasLimit);
        emit BlockPropertyAccessed("block.difficulty", difficulty);
    }
    
    function transactionProperties() external returns (
        address origin,
        uint256 gasPrice,
        uint256 gasLeft,
        bytes memory msgData,
        bytes4 msgSig
    ) {
        origin = tx.origin;
        gasPrice = tx.gasprice;
        gasLeft = gasleft();
        msgData = msg.data;
        msgSig = msg.sig;
        
        emit BlockPropertyAccessed("tx.origin", uint256(uint160(origin)));
        emit BlockPropertyAccessed("tx.gasprice", gasPrice);
        emit BlockPropertyAccessed("gasleft", gasLeft);
    }
    
    function blockHashOperations(uint256 blockNumber) external returns (bytes32 blockHash) {
        require(blockNumber < block.number, "Block not yet mined");
        require(block.number - blockNumber <= 256, "Block too old");
        
        blockHash = blockhash(blockNumber);
        emit CryptoOperation("blockhash", blockHash, blockNumber);
    }
    
    function chainProperties() external view returns (
        uint256 chainId,
        uint256 blockNumber,
        uint256 timestamp
    ) {
        chainId = block.chainid;
        blockNumber = block.number;
        timestamp = block.timestamp;
    }
    
    // Advanced cryptographic patterns
    function commitRevealScheme(bytes32 commitment) external {
        // Store commitment
        userHashes[msg.sender] = commitment;
        emit CryptoOperation("commit", commitment, 1);
    }
    
    function revealCommitment(uint256 value, uint256 nonce) external returns (bool valid) {
        bytes32 hash = keccak256(abi.encodePacked(value, nonce, msg.sender));
        valid = userHashes[msg.sender] == hash;
        
        if (valid) {
            delete userHashes[msg.sender];
        }
        
        emit CryptoOperation("reveal", hash, valid ? 1 : 0);
    }
    
    function randomnessFromBlock() external view returns (uint256 pseudoRandom) {
        // WARNING: This is not secure randomness!
        pseudoRandom = uint256(keccak256(abi.encodePacked(
            block.timestamp,
            block.difficulty,
            block.coinbase,
            blockhash(block.number - 1)
        )));
    }
    
    // Utility functions
    function uintToString(uint256 value) internal pure returns (string memory) {
        if (value == 0) {
            return "0";
        }
        uint256 temp = value;
        uint256 digits;
        while (temp != 0) {
            digits++;
            temp /= 10;
        }
        bytes memory buffer = new bytes(digits);
        while (value != 0) {
            digits -= 1;
            buffer[digits] = bytes1(uint8(48 + uint256(value % 10)));
            value /= 10;
        }
        return string(buffer);
    }
    
    // View functions
    function getStoredData() external view returns (bytes memory data, bytes32 hash) {
        return (storedBytes, storedHash);
    }
    
    function getDeploymentInfo() external view returns (
        uint256 blockNum,
        uint256 timestamp,
        bytes32 blockHash
    ) {
        return (deploymentBlock, deploymentTimestamp, deploymentBlockHash);
    }
}
