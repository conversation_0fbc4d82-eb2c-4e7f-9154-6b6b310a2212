// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Unchecked Block Feature Test Contract
 * Tests unchecked arithmetic operations
 */
contract UncheckedContract {
    uint256 public checkedResult;
    uint256 public uncheckedResult;

    event OverflowDetected(string operation, uint256 a, uint256 b);
    event UnderflowDetected(string operation, uint256 a, uint256 b);
    event ArithmeticOperation(string operation, uint256 result, bool wasUnchecked);

    // Checked arithmetic (default behavior in Solidity 0.8+)
    function checkedAddition(uint256 a, uint256 b) public returns (uint256) {
        uint256 result = a + b; // Will revert on overflow
        checkedResult = result;
        emit ArithmeticOperation("addition", result, false);
        return result;
    }

    function checkedSubtraction(uint256 a, uint256 b) public returns (uint256) {
        uint256 result = a - b; // Will revert on underflow
        checkedResult = result;
        emit ArithmeticOperation("subtraction", result, false);
        return result;
    }

    function checkedMultiplication(uint256 a, uint256 b) public returns (uint256) {
        uint256 result = a * b; // Will revert on overflow
        checkedResult = result;
        emit ArithmeticOperation("multiplication", result, false);
        return result;
    }

    // Unchecked arithmetic (wraps around on overflow/underflow)
    function uncheckedAddition(uint256 a, uint256 b) public returns (uint256) {
        uint256 result;
        unchecked {
            result = a + b; // Wraps around on overflow
        }
        uncheckedResult = result;
        emit ArithmeticOperation("addition", result, true);

        // Check if overflow occurred
        if (result < a || result < b) {
            emit OverflowDetected("addition", a, b);
        }

        return result;
    }

    function uncheckedSubtraction(uint256 a, uint256 b) public returns (uint256) {
        uint256 result;
        unchecked {
            result = a - b; // Wraps around on underflow
        }
        uncheckedResult = result;
        emit ArithmeticOperation("subtraction", result, true);

        // Check if underflow occurred
        if (result > a) {
            emit UnderflowDetected("subtraction", a, b);
        }

        return result;
    }

    function uncheckedMultiplication(uint256 a, uint256 b) public returns (uint256) {
        uint256 result;
        unchecked {
            result = a * b; // Wraps around on overflow
        }
        uncheckedResult = result;
        emit ArithmeticOperation("multiplication", result, true);

        // Check if overflow occurred (if b != 0 and result / b != a)
        if (b != 0 && result / b != a) {
            emit OverflowDetected("multiplication", a, b);
        }

        return result;
    }

    function uncheckedDivision(uint256 a, uint256 b) public returns (uint256) {
        require(b != 0, "Division by zero");
        uint256 result;
        unchecked {
            result = a / b; // Division doesn't overflow, but we use unchecked for consistency
        }
        uncheckedResult = result;
        emit ArithmeticOperation("division", result, true);
        return result;
    }

    // Complex unchecked operations
    function uncheckedComplexOperation(uint256 a, uint256 b, uint256 c) public returns (uint256) {
        uint256 result;
        unchecked {
            result = (a + b) * c - (a * b) + c;
        }
        uncheckedResult = result;
        emit ArithmeticOperation("complex", result, true);
        return result;
    }

    // Mixed checked and unchecked operations
    function mixedOperations(uint256 a, uint256 b, uint256 c) public returns (uint256, uint256) {
        // Checked operation
        uint256 checkedSum = a + b; // Will revert on overflow

        // Unchecked operation
        uint256 uncheckedProduct;
        unchecked {
            uncheckedProduct = checkedSum * c; // Wraps on overflow
        }

        checkedResult = checkedSum;
        uncheckedResult = uncheckedProduct;

        return (checkedSum, uncheckedProduct);
    }

    // Unchecked loops (for gas optimization)
    function uncheckedLoop(uint256 iterations) public returns (uint256) {
        uint256 sum = 0;

        for (uint256 i = 0; i < iterations;) {
            sum += i;
            unchecked {
                i++; // Gas optimization: skip overflow check for loop counter
            }
        }

        uncheckedResult = sum;
        return sum;
    }

    // Unchecked array operations
    function uncheckedArraySum(uint256[] memory arr) public returns (uint256) {
        uint256 sum = 0;
        uint256 length = arr.length;

        for (uint256 i = 0; i < length;) {
            unchecked {
                sum += arr[i]; // Unchecked addition
                i++; // Unchecked increment
            }
        }

        uncheckedResult = sum;
        return sum;
    }

    // Unchecked with negative numbers (using int256)
    function uncheckedSignedOperations(int256 a, int256 b) public returns (int256) {
        int256 result;
        unchecked {
            result = a + b; // Can wrap around for signed integers too
        }
        return result;
    }

    // Unchecked type conversions
    function uncheckedTypeConversion(uint256 value) public pure returns (uint8) {
        uint8 result;
        unchecked {
            result = uint8(value); // Truncates without checking bounds
        }
        return result;
    }

    // Unchecked with function calls
    function uncheckedWithFunctionCalls(uint256 a, uint256 b) public returns (uint256) {
        uint256 result;
        unchecked {
            result = getSquare(a) + getSquare(b);
        }
        uncheckedResult = result;
        return result;
    }

    function getSquare(uint256 x) private pure returns (uint256) {
        return x * x;
    }

    // Sequential unchecked blocks (nested not allowed)
    function sequentialUncheckedBlocks(uint256 a, uint256 b, uint256 c) public returns (uint256) {
        uint256 result;
        uint256 temp1;
        uint256 temp2;

        unchecked {
            temp1 = a + b;
        }

        unchecked {
            temp2 = temp1 * c;
            result = temp2 - a;
        }

        uncheckedResult = result;
        return result;
    }

    // Unchecked with storage operations
    uint256 private storageValue;

    function uncheckedStorageOperations(uint256 increment) public returns (uint256) {
        unchecked {
            storageValue += increment;
        }
        uncheckedResult = storageValue;
        return storageValue;
    }

    // Comparison between checked and unchecked
    function compareCheckedUnchecked(uint256 a, uint256 b)
        public
        returns (bool checkedSuccess, uint256 checkedRes, uint256 uncheckedRes)
    {
        // Try checked operation
        try this.checkedAddition(a, b) returns (uint256 result) {
            checkedSuccess = true;
            checkedRes = result;
        } catch {
            checkedSuccess = false;
            checkedRes = 0;
        }

        // Always do unchecked operation
        uncheckedRes = uncheckedAddition(a, b);

        return (checkedSuccess, checkedRes, uncheckedRes);
    }

    // Unchecked with assembly (advanced)
    function uncheckedWithAssembly(uint256 a, uint256 b) public pure returns (uint256 result) {
        unchecked {
            assembly {
                result := add(a, b) // Assembly operations are always unchecked
            }
        }
    }

    // Gas comparison functions
    function gasTestChecked(uint256 iterations) public returns (uint256 gasUsed) {
        uint256 gasBefore = gasleft();
        uint256 sum = 0;

        for (uint256 i = 0; i < iterations; i++) {
            sum += i; // Checked arithmetic
        }

        gasUsed = gasBefore - gasleft();
        checkedResult = sum;
        return gasUsed;
    }

    function gasTestUnchecked(uint256 iterations) public returns (uint256 gasUsed) {
        uint256 gasBefore = gasleft();
        uint256 sum = 0;

        unchecked {
            for (uint256 i = 0; i < iterations; i++) {
                sum += i; // Unchecked arithmetic
            }
        }

        gasUsed = gasBefore - gasleft();
        uncheckedResult = sum;
        return gasUsed;
    }

    // Utility functions
    function getResults() public view returns (uint256 checked, uint256 uncheckedRes) {
        return (checkedResult, uncheckedResult);
    }

    function resetResults() public {
        checkedResult = 0;
        uncheckedResult = 0;
    }

    // Test overflow/underflow scenarios
    function testMaxUintOverflow() public returns (uint256) {
        uint256 maxUint = type(uint256).max;
        return uncheckedAddition(maxUint, 1); // Should wrap to 0
    }

    function testZeroUnderflow() public returns (uint256) {
        return uncheckedSubtraction(0, 1); // Should wrap to max uint256
    }
}
