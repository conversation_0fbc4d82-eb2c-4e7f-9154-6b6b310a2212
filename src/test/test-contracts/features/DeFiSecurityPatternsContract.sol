// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * DeFi and Security Patterns Test Contract
 * Tests common DeFi patterns, security practices, and gas optimizations
 */
contract DeFiSecurityPatternsContract {
    // Security patterns
    bool private locked;
    mapping(address => uint256) private nonces;
    mapping(bytes32 => bool) private usedHashes;
    
    // DeFi state variables
    uint256 public totalSupply;
    uint256 public totalLiquidity;
    uint256 public reserveA;
    uint256 public reserveB;
    uint256 public constant MINIMUM_LIQUIDITY = 1000;
    uint256 public constant FEE_DENOMINATOR = 10000;
    uint256 public constant SWAP_FEE = 30; // 0.3%
    
    mapping(address => uint256) public balances;
    mapping(address => uint256) public liquidityShares;
    mapping(address => mapping(address => uint256)) public allowances;
    
    // Gas optimization: pack structs
    struct UserInfo {
        uint128 balance;      // 16 bytes
        uint128 lastUpdate;   // 16 bytes
        // Total: 32 bytes (1 storage slot)
    }
    
    struct PoolInfo {
        uint128 reserve0;     // 16 bytes
        uint128 reserve1;     // 16 bytes
        // Total: 32 bytes (1 storage slot)
    }
    
    mapping(address => UserInfo) public userInfo;
    PoolInfo public poolInfo;
    
    // Events
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
    event Swap(address indexed user, uint256 amountIn, uint256 amountOut, bool zeroForOne);
    event LiquidityAdded(address indexed provider, uint256 amountA, uint256 amountB, uint256 liquidity);
    event LiquidityRemoved(address indexed provider, uint256 amountA, uint256 amountB, uint256 liquidity);
    event FlashLoan(address indexed borrower, uint256 amount, uint256 fee);
    
    // Security modifiers
    modifier nonReentrant() {
        require(!locked, "ReentrancyGuard: reentrant call");
        locked = true;
        _;
        locked = false;
    }
    
    modifier validAddress(address addr) {
        require(addr != address(0), "Invalid address");
        _;
    }
    
    modifier onlyValidAmount(uint256 amount) {
        require(amount > 0, "Amount must be positive");
        _;
    }
    
    // Gas optimization: use unchecked for safe operations
    function safeAdd(uint256 a, uint256 b) internal pure returns (uint256) {
        unchecked {
            uint256 c = a + b;
            require(c >= a, "SafeMath: addition overflow");
            return c;
        }
    }
    
    function safeSub(uint256 a, uint256 b) internal pure returns (uint256) {
        require(b <= a, "SafeMath: subtraction underflow");
        unchecked {
            return a - b;
        }
    }
    
    function safeMul(uint256 a, uint256 b) internal pure returns (uint256) {
        if (a == 0) return 0;
        unchecked {
            uint256 c = a * b;
            require(c / a == b, "SafeMath: multiplication overflow");
            return c;
        }
    }
    
    // AMM functions (Uniswap-like)
    function addLiquidity(uint256 amountA, uint256 amountB) 
        external 
        nonReentrant 
        onlyValidAmount(amountA) 
        onlyValidAmount(amountB) 
        returns (uint256 liquidity) 
    {
        // Transfer tokens (simplified - would use transferFrom in real implementation)
        balances[msg.sender] = safeSub(balances[msg.sender], amountA + amountB);
        
        if (totalLiquidity == 0) {
            liquidity = sqrt(safeMul(amountA, amountB)) - MINIMUM_LIQUIDITY;
            liquidityShares[address(0)] = MINIMUM_LIQUIDITY; // Lock minimum liquidity
        } else {
            liquidity = min(
                safeMul(amountA, totalLiquidity) / reserveA,
                safeMul(amountB, totalLiquidity) / reserveB
            );
        }
        
        require(liquidity > 0, "Insufficient liquidity minted");
        
        liquidityShares[msg.sender] = safeAdd(liquidityShares[msg.sender], liquidity);
        totalLiquidity = safeAdd(totalLiquidity, liquidity);
        reserveA = safeAdd(reserveA, amountA);
        reserveB = safeAdd(reserveB, amountB);
        
        emit LiquidityAdded(msg.sender, amountA, amountB, liquidity);
    }
    
    function removeLiquidity(uint256 liquidity) 
        external 
        nonReentrant 
        onlyValidAmount(liquidity) 
        returns (uint256 amountA, uint256 amountB) 
    {
        require(liquidityShares[msg.sender] >= liquidity, "Insufficient liquidity");
        
        amountA = safeMul(liquidity, reserveA) / totalLiquidity;
        amountB = safeMul(liquidity, reserveB) / totalLiquidity;
        
        require(amountA > 0 && amountB > 0, "Insufficient liquidity burned");
        
        liquidityShares[msg.sender] = safeSub(liquidityShares[msg.sender], liquidity);
        totalLiquidity = safeSub(totalLiquidity, liquidity);
        reserveA = safeSub(reserveA, amountA);
        reserveB = safeSub(reserveB, amountB);
        
        balances[msg.sender] = safeAdd(balances[msg.sender], amountA + amountB);
        
        emit LiquidityRemoved(msg.sender, amountA, amountB, liquidity);
    }
    
    function swap(uint256 amountIn, bool zeroForOne) 
        external 
        nonReentrant 
        onlyValidAmount(amountIn) 
        returns (uint256 amountOut) 
    {
        require(balances[msg.sender] >= amountIn, "Insufficient balance");
        
        uint256 amountInWithFee = safeMul(amountIn, FEE_DENOMINATOR - SWAP_FEE);
        
        if (zeroForOne) {
            amountOut = safeMul(amountInWithFee, reserveB) / 
                       safeAdd(safeMul(reserveA, FEE_DENOMINATOR), amountInWithFee);
            require(amountOut < reserveB, "Insufficient liquidity");
            
            reserveA = safeAdd(reserveA, amountIn);
            reserveB = safeSub(reserveB, amountOut);
        } else {
            amountOut = safeMul(amountInWithFee, reserveA) / 
                       safeAdd(safeMul(reserveB, FEE_DENOMINATOR), amountInWithFee);
            require(amountOut < reserveA, "Insufficient liquidity");
            
            reserveB = safeAdd(reserveB, amountIn);
            reserveA = safeSub(reserveA, amountOut);
        }
        
        balances[msg.sender] = safeSub(balances[msg.sender], amountIn);
        balances[msg.sender] = safeAdd(balances[msg.sender], amountOut);
        
        emit Swap(msg.sender, amountIn, amountOut, zeroForOne);
    }
    
    // Flash loan pattern
    function flashLoan(uint256 amount, bytes calldata data) external nonReentrant {
        require(amount <= reserveA, "Insufficient liquidity for flash loan");
        
        uint256 balanceBefore = reserveA;
        uint256 fee = safeMul(amount, SWAP_FEE) / FEE_DENOMINATOR;
        
        // Send tokens to borrower
        balances[msg.sender] = safeAdd(balances[msg.sender], amount);
        reserveA = safeSub(reserveA, amount);
        
        // Call borrower's callback
        IFlashLoanReceiver(msg.sender).receiveFlashLoan(amount, fee, data);
        
        // Check repayment
        require(balances[msg.sender] >= amount + fee, "Flash loan not repaid");
        balances[msg.sender] = safeSub(balances[msg.sender], amount + fee);
        reserveA = safeAdd(reserveA, amount + fee);
        
        require(reserveA >= balanceBefore, "Flash loan fee not paid");
        
        emit FlashLoan(msg.sender, amount, fee);
    }
    
    // Security patterns: signature verification
    function verifySignature(
        bytes32 hash,
        uint8 v,
        bytes32 r,
        bytes32 s,
        address signer
    ) public pure returns (bool) {
        bytes32 messageHash = keccak256(abi.encodePacked("\x19Ethereum Signed Message:\n32", hash));
        address recovered = ecrecover(messageHash, v, r, s);
        return recovered == signer;
    }
    
    // Permit pattern (EIP-2612)
    function permit(
        address owner,
        address spender,
        uint256 value,
        uint256 deadline,
        uint8 v,
        bytes32 r,
        bytes32 s
    ) external {
        require(block.timestamp <= deadline, "Permit expired");
        
        bytes32 structHash = keccak256(abi.encode(
            keccak256("Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)"),
            owner,
            spender,
            value,
            nonces[owner]++,
            deadline
        ));
        
        bytes32 hash = keccak256(abi.encodePacked("\x19\x01", getDomainSeparator(), structHash));
        require(verifySignature(hash, v, r, s, owner), "Invalid signature");
        
        allowances[owner][spender] = value;
        emit Approval(owner, spender, value);
    }
    
    function getDomainSeparator() public view returns (bytes32) {
        return keccak256(abi.encode(
            keccak256("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)"),
            keccak256("DeFiSecurityPatternsContract"),
            keccak256("1"),
            block.chainid,
            address(this)
        ));
    }
    
    // Gas optimization: batch operations
    function batchTransfer(address[] calldata recipients, uint256[] calldata amounts) external {
        require(recipients.length == amounts.length, "Array length mismatch");
        
        uint256 totalAmount = 0;
        uint256 length = recipients.length;
        
        // Calculate total first to check balance once
        for (uint256 i = 0; i < length;) {
            totalAmount = safeAdd(totalAmount, amounts[i]);
            unchecked { ++i; }
        }
        
        require(balances[msg.sender] >= totalAmount, "Insufficient balance");
        balances[msg.sender] = safeSub(balances[msg.sender], totalAmount);
        
        // Perform transfers
        for (uint256 i = 0; i < length;) {
            balances[recipients[i]] = safeAdd(balances[recipients[i]], amounts[i]);
            emit Transfer(msg.sender, recipients[i], amounts[i]);
            unchecked { ++i; }
        }
    }
    
    // Utility functions
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
    
    function min(uint256 a, uint256 b) internal pure returns (uint256) {
        return a < b ? a : b;
    }
    
    function max(uint256 a, uint256 b) internal pure returns (uint256) {
        return a > b ? a : b;
    }
    
    // View functions
    function getReserves() external view returns (uint256, uint256) {
        return (reserveA, reserveB);
    }
    
    function getAmountOut(uint256 amountIn, bool zeroForOne) external view returns (uint256) {
        uint256 amountInWithFee = safeMul(amountIn, FEE_DENOMINATOR - SWAP_FEE);
        
        if (zeroForOne) {
            return safeMul(amountInWithFee, reserveB) / 
                   safeAdd(safeMul(reserveA, FEE_DENOMINATOR), amountInWithFee);
        } else {
            return safeMul(amountInWithFee, reserveA) / 
                   safeAdd(safeMul(reserveB, FEE_DENOMINATOR), amountInWithFee);
        }
    }
}

interface IFlashLoanReceiver {
    function receiveFlashLoan(uint256 amount, uint256 fee, bytes calldata data) external;
}
