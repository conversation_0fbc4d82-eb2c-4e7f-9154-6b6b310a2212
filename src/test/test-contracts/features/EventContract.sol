// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Event Feature Test Contract
 * Tests all event-related language features
 */
contract EventContract {
    // Basic events
    event BasicEvent();
    event SimpleEvent(uint256 value);
    event StringEvent(string message);
    
    // Events with indexed parameters
    event IndexedEvent(uint256 indexed id, address indexed user, uint256 amount);
    event TransferEvent(address indexed from, address indexed to, uint256 indexed tokenId, uint256 amount);
    
    // Events with mixed indexed and non-indexed parameters
    event MixedEvent(
        uint256 indexed id,
        address indexed user,
        string name,
        uint256 timestamp,
        bool indexed success
    );
    
    // Events with complex data types
    event ArrayEvent(uint256[] values, string[] names);
    event StructEvent(uint256 indexed id, UserData userData);
    event MappingUpdateEvent(address indexed user, uint256 indexed key, uint256 value);
    
    // Anonymous events
    event AnonymousEvent(uint256 indexed value) anonymous;
    event AnotherAnonymousEvent(address indexed user, string data) anonymous;
    
    // Events with different parameter counts (testing limits)
    event OneParam(uint256 indexed a);
    event TwoParams(uint256 indexed a, uint256 indexed b);
    event ThreeParams(uint256 indexed a, uint256 indexed b, uint256 indexed c);
    event FourParams(uint256 indexed a, uint256 indexed b, uint256 indexed c, uint256 d); // Max 3 indexed
    
    // Events for different contract states
    event ContractDeployed(address indexed deployer, uint256 timestamp);
    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);
    event ContractPaused(address indexed by, uint256 timestamp);
    event ContractUnpaused(address indexed by, uint256 timestamp);
    
    // Events for business logic
    event UserRegistered(address indexed user, string username, uint256 registrationTime);
    event BalanceUpdated(address indexed user, uint256 oldBalance, uint256 newBalance);
    event TransactionProcessed(
        bytes32 indexed txHash,
        address indexed from,
        address indexed to,
        uint256 amount,
        uint256 fee,
        string memo
    );
    
    // Struct for complex event data
    struct UserData {
        string name;
        uint256 age;
        bool isActive;
        uint256[] scores;
    }
    
    // State variables
    address public owner;
    bool public paused;
    mapping(address => uint256) public balances;
    mapping(address => UserData) public users;
    uint256 public eventCounter;
    
    constructor() {
        owner = msg.sender;
        paused = false;
        emit ContractDeployed(msg.sender, block.timestamp);
    }
    
    // Functions that emit basic events
    function emitBasicEvent() public {
        emit BasicEvent();
        eventCounter++;
    }
    
    function emitSimpleEvent(uint256 _value) public {
        emit SimpleEvent(_value);
        eventCounter++;
    }
    
    function emitStringEvent(string memory _message) public {
        emit StringEvent(_message);
        eventCounter++;
    }
    
    // Functions that emit indexed events
    function emitIndexedEvent(uint256 _id, uint256 _amount) public {
        emit IndexedEvent(_id, msg.sender, _amount);
        eventCounter++;
    }
    
    function emitTransferEvent(address _to, uint256 _tokenId, uint256 _amount) public {
        emit TransferEvent(msg.sender, _to, _tokenId, _amount);
        eventCounter++;
    }
    
    // Function that emits mixed events
    function emitMixedEvent(uint256 _id, string memory _name, bool _success) public {
        emit MixedEvent(_id, msg.sender, _name, block.timestamp, _success);
        eventCounter++;
    }
    
    // Functions that emit complex data type events
    function emitArrayEvent(uint256[] memory _values, string[] memory _names) public {
        emit ArrayEvent(_values, _names);
        eventCounter++;
    }
    
    function emitStructEvent(uint256 _id, UserData memory _userData) public {
        emit StructEvent(_id, _userData);
        eventCounter++;
    }
    
    // Function that emits anonymous events
    function emitAnonymousEvent(uint256 _value) public {
        emit AnonymousEvent(_value);
        eventCounter++;
    }
    
    function emitAnotherAnonymousEvent(string memory _data) public {
        emit AnotherAnonymousEvent(msg.sender, _data);
        eventCounter++;
    }
    
    // Functions that emit multiple events
    function emitMultipleEvents(uint256 _value, string memory _message) public {
        emit SimpleEvent(_value);
        emit StringEvent(_message);
        emit IndexedEvent(eventCounter, msg.sender, _value);
        eventCounter += 3;
    }
    
    // Business logic functions with events
    function registerUser(string memory _username, uint256 _age) public {
        users[msg.sender] = UserData({
            name: _username,
            age: _age,
            isActive: true,
            scores: new uint256[](0)
        });
        
        emit UserRegistered(msg.sender, _username, block.timestamp);
        eventCounter++;
    }
    
    function updateBalance(address _user, uint256 _newBalance) public {
        require(msg.sender == owner, "Only owner can update balances");
        
        uint256 oldBalance = balances[_user];
        balances[_user] = _newBalance;
        
        emit BalanceUpdated(_user, oldBalance, _newBalance);
        emit MappingUpdateEvent(_user, 0, _newBalance); // Using 0 as key for balance
        eventCounter += 2;
    }
    
    function processTransaction(
        address _to,
        uint256 _amount,
        uint256 _fee,
        string memory _memo
    ) public {
        require(balances[msg.sender] >= _amount + _fee, "Insufficient balance");
        
        balances[msg.sender] -= (_amount + _fee);
        balances[_to] += _amount;
        
        bytes32 txHash = keccak256(abi.encodePacked(
            msg.sender,
            _to,
            _amount,
            _fee,
            block.timestamp
        ));
        
        emit TransactionProcessed(txHash, msg.sender, _to, _amount, _fee, _memo);
        emit BalanceUpdated(msg.sender, balances[msg.sender] + _amount + _fee, balances[msg.sender]);
        emit BalanceUpdated(_to, balances[_to] - _amount, balances[_to]);
        eventCounter += 3;
    }
    
    // Admin functions with events
    function transferOwnership(address _newOwner) public {
        require(msg.sender == owner, "Only owner can transfer ownership");
        require(_newOwner != address(0), "New owner cannot be zero address");
        
        address previousOwner = owner;
        owner = _newOwner;
        
        emit OwnershipTransferred(previousOwner, _newOwner);
        eventCounter++;
    }
    
    function pause() public {
        require(msg.sender == owner, "Only owner can pause");
        require(!paused, "Already paused");
        
        paused = true;
        emit ContractPaused(msg.sender, block.timestamp);
        eventCounter++;
    }
    
    function unpause() public {
        require(msg.sender == owner, "Only owner can unpause");
        require(paused, "Not paused");
        
        paused = false;
        emit ContractUnpaused(msg.sender, block.timestamp);
        eventCounter++;
    }
    
    // Function that emits events in loops
    function emitEventsInLoop(uint256 _count) public {
        for (uint256 i = 0; i < _count && i < 10; i++) { // Limit to prevent gas issues
            emit SimpleEvent(i);
            eventCounter++;
        }
    }
    
    // Function that emits events conditionally
    function conditionalEventEmission(uint256 _value) public {
        if (_value > 100) {
            emit SimpleEvent(_value);
            emit StringEvent("High value");
        } else if (_value > 50) {
            emit SimpleEvent(_value);
            emit StringEvent("Medium value");
        } else {
            emit StringEvent("Low value");
        }
        eventCounter++;
    }
    
    // Function that emits events with calculated data
    function emitCalculatedEvent(uint256 _a, uint256 _b) public {
        uint256 sum = _a + _b;
        uint256 product = _a * _b;
        
        emit TwoParams(sum, product);
        emit MixedEvent(
            eventCounter,
            msg.sender,
            "Calculation result",
            block.timestamp,
            sum > product
        );
        eventCounter += 2;
    }
    
    // Getter functions
    function getEventCounter() public view returns (uint256) {
        return eventCounter;
    }
    
    function getUser(address _user) public view returns (UserData memory) {
        return users[_user];
    }
    
    function getBalance(address _user) public view returns (uint256) {
        return balances[_user];
    }
}
