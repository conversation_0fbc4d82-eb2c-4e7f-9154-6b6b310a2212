// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Gas Optimization Patterns Test Contract
 * Tests various gas optimization techniques and patterns
 */
contract GasOptimizationContract {
    // Gas-optimized storage packing
    struct PackedStruct {
        uint128 value1;    // 16 bytes
        uint128 value2;    // 16 bytes
        // Total: 32 bytes (1 storage slot)
    }
    
    struct UnpackedStruct {
        uint256 value1;    // 32 bytes (1 storage slot)
        uint256 value2;    // 32 bytes (1 storage slot)
        // Total: 64 bytes (2 storage slots)
    }
    
    // Packed storage variables
    uint128 public packedVar1;
    uint128 public packedVar2;
    bool public flag1;
    bool public flag2;
    uint8 public smallNumber;
    // All above fit in 1-2 storage slots
    
    // Arrays for testing
    uint256[] public dynamicArray;
    uint256[10] public fixedArray;
    mapping(uint256 => uint256) public simpleMapping;
    mapping(uint256 => PackedStruct) public packedMapping;
    
    // Events
    event GasOptimization(string technique, uint256 gasUsed);
    event LoopOptimization(string technique, uint256 iterations, uint256 gasUsed);
    event StorageOptimization(string technique, uint256 slotsUsed);
    
    constructor() {
        packedVar1 = 100;
        packedVar2 = 200;
        flag1 = true;
        flag2 = false;
        smallNumber = 42;
    }
    
    // Gas optimization: Use unchecked for safe operations
    function uncheckedArithmetic(uint256 a, uint256 b) external returns (uint256 result) {
        uint256 gasBefore = gasleft();
        
        unchecked {
            result = a + b;
            result = result * 2;
            result = result - 1;
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("unchecked_arithmetic", gasUsed);
    }
    
    function checkedArithmetic(uint256 a, uint256 b) external returns (uint256 result) {
        uint256 gasBefore = gasleft();
        
        result = a + b;
        result = result * 2;
        result = result - 1;
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("checked_arithmetic", gasUsed);
    }
    
    // Gas optimization: Efficient loops
    function inefficientLoop(uint256[] memory array) external returns (uint256 sum) {
        uint256 gasBefore = gasleft();
        
        for (uint256 i = 0; i < array.length; i++) {
            sum += array[i];
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit LoopOptimization("inefficient", array.length, gasUsed);
    }
    
    function efficientLoop(uint256[] memory array) external returns (uint256 sum) {
        uint256 gasBefore = gasleft();
        
        uint256 length = array.length;
        for (uint256 i = 0; i < length;) {
            sum += array[i];
            unchecked { ++i; }
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit LoopOptimization("efficient", length, gasUsed);
    }
    
    function assemblyLoop(uint256[] memory array) external returns (uint256 sum) {
        uint256 gasBefore = gasleft();
        
        assembly {
            let len := mload(array)
            let data := add(array, 0x20)
            
            for { let i := 0 } lt(i, len) { i := add(i, 1) } {
                sum := add(sum, mload(add(data, mul(i, 0x20))))
            }
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit LoopOptimization("assembly", array.length, gasUsed);
    }
    
    // Gas optimization: Storage packing
    function setPackedValues(uint128 val1, uint128 val2) external {
        uint256 gasBefore = gasleft();
        
        packedVar1 = val1;
        packedVar2 = val2;
        
        uint256 gasUsed = gasBefore - gasleft();
        emit StorageOptimization("packed_storage", gasUsed);
    }
    
    function setUnpackedValues(uint256 val1, uint256 val2) external {
        uint256 gasBefore = gasleft();
        
        // These would use separate storage slots if they were state variables
        uint256 temp1 = val1;
        uint256 temp2 = val2;
        
        uint256 gasUsed = gasBefore - gasleft();
        emit StorageOptimization("unpacked_storage", gasUsed);
    }
    
    // Gas optimization: Memory vs Storage
    function memoryOperations(uint256[] memory array) external returns (uint256 result) {
        uint256 gasBefore = gasleft();
        
        uint256[] memory tempArray = new uint256[](array.length);
        for (uint256 i = 0; i < array.length; i++) {
            tempArray[i] = array[i] * 2;
            result += tempArray[i];
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("memory_operations", gasUsed);
    }
    
    function storageOperations(uint256[] memory array) external returns (uint256 result) {
        uint256 gasBefore = gasleft();
        
        // Clear existing array
        delete dynamicArray;
        
        for (uint256 i = 0; i < array.length; i++) {
            dynamicArray.push(array[i] * 2);
            result += dynamicArray[i];
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("storage_operations", gasUsed);
    }
    
    // Gas optimization: Short-circuiting
    function shortCircuitAnd(bool a, bool b, bool c) external returns (bool result) {
        uint256 gasBefore = gasleft();
        
        result = a && expensiveOperation() && b && c;
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("short_circuit_and", gasUsed);
    }
    
    function shortCircuitOr(bool a, bool b, bool c) external returns (bool result) {
        uint256 gasBefore = gasleft();
        
        result = a || expensiveOperation() || b || c;
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("short_circuit_or", gasUsed);
    }
    
    function expensiveOperation() internal pure returns (bool) {
        uint256 temp = 0;
        for (uint256 i = 0; i < 100; i++) {
            temp += i * i;
        }
        return temp > 0;
    }
    
    // Gas optimization: Function modifiers vs internal functions
    modifier expensiveModifier() {
        uint256 temp = 0;
        for (uint256 i = 0; i < 50; i++) {
            temp += i;
        }
        _;
    }
    
    function functionWithModifier() external expensiveModifier returns (uint256) {
        uint256 gasBefore = gasleft();
        uint256 result = 42;
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("with_modifier", gasUsed);
        return result;
    }
    
    function functionWithInternalCall() external returns (uint256) {
        uint256 gasBefore = gasleft();
        internalExpensiveFunction();
        uint256 result = 42;
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("with_internal_call", gasUsed);
        return result;
    }
    
    function internalExpensiveFunction() internal pure {
        uint256 temp = 0;
        for (uint256 i = 0; i < 50; i++) {
            temp += i;
        }
    }
    
    // Gas optimization: Bit operations
    function bitwiseOperations(uint256 value) external returns (uint256 result) {
        uint256 gasBefore = gasleft();
        
        // Multiply by 2 using bit shift (cheaper than multiplication)
        result = value << 1;
        
        // Divide by 4 using bit shift (cheaper than division)
        result = result >> 2;
        
        // Check if even using bitwise AND (cheaper than modulo)
        bool isEven = (value & 1) == 0;
        if (isEven) {
            result += 1;
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("bitwise_operations", gasUsed);
    }
    
    function arithmeticOperations(uint256 value) external returns (uint256 result) {
        uint256 gasBefore = gasleft();
        
        // Multiply by 2
        result = value * 2;
        
        // Divide by 4
        result = result / 4;
        
        // Check if even
        bool isEven = (value % 2) == 0;
        if (isEven) {
            result += 1;
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("arithmetic_operations", gasUsed);
    }
    
    // Gas optimization: Batch operations
    function batchSetMapping(uint256[] memory keys, uint256[] memory values) external {
        require(keys.length == values.length, "Array length mismatch");
        
        uint256 gasBefore = gasleft();
        
        uint256 length = keys.length;
        for (uint256 i = 0; i < length;) {
            simpleMapping[keys[i]] = values[i];
            unchecked { ++i; }
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("batch_mapping", gasUsed);
    }
    
    function individualSetMapping(uint256[] memory keys, uint256[] memory values) external {
        require(keys.length == values.length, "Array length mismatch");
        
        uint256 gasBefore = gasleft();
        
        for (uint256 i = 0; i < keys.length; i++) {
            simpleMapping[keys[i]] = values[i];
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("individual_mapping", gasUsed);
    }
    
    // Gas optimization: String operations
    function efficientStringComparison(string memory a, string memory b) external returns (bool equal) {
        uint256 gasBefore = gasleft();
        
        equal = keccak256(bytes(a)) == keccak256(bytes(b));
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("efficient_string_compare", gasUsed);
    }
    
    function inefficientStringComparison(string memory a, string memory b) external returns (bool equal) {
        uint256 gasBefore = gasleft();
        
        bytes memory bytesA = bytes(a);
        bytes memory bytesB = bytes(b);
        
        if (bytesA.length != bytesB.length) {
            equal = false;
        } else {
            equal = true;
            for (uint256 i = 0; i < bytesA.length; i++) {
                if (bytesA[i] != bytesB[i]) {
                    equal = false;
                    break;
                }
            }
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("inefficient_string_compare", gasUsed);
    }
    
    // Gas optimization: Early returns
    function earlyReturn(uint256 value) external returns (uint256 result) {
        uint256 gasBefore = gasleft();
        
        if (value == 0) {
            uint256 gasUsed = gasBefore - gasleft();
            emit GasOptimization("early_return", gasUsed);
            return 0;
        }
        
        // Expensive operations
        for (uint256 i = 0; i < value; i++) {
            result += i * i;
        }
        
        uint256 gasUsed = gasBefore - gasleft();
        emit GasOptimization("full_execution", gasUsed);
    }
    
    // View functions for gas analysis
    function getPackedValues() external view returns (uint128, uint128, bool, bool, uint8) {
        return (packedVar1, packedVar2, flag1, flag2, smallNumber);
    }
    
    function getArrayLength() external view returns (uint256) {
        return dynamicArray.length;
    }
    
    function getMappingValue(uint256 key) external view returns (uint256) {
        return simpleMapping[key];
    }
}
