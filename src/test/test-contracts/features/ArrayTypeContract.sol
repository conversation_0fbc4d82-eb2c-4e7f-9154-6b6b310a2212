// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * ArrayType Feature Test Contract
 * Tests all array-related language features
 */
contract ArrayTypeContract {
    // Fixed-size arrays
    uint256[5] public fixedArray;
    bytes32[3] public fixedBytesArray;
    address[10] public fixedAddressArray;
    
    // Dynamic arrays
    uint256[] public dynamicArray;
    string[] public stringArray;
    bytes[] public bytesArray;
    
    // Multi-dimensional arrays
    uint256[][] public twoDimensionalArray;
    uint256[3][4] public fixedTwoDimensionalArray;
    
    // Array of structs
    struct Item {
        uint256 id;
        string name;
    }
    Item[] public itemArray;
    
    // Array of mappings
    mapping(address => uint256)[] public mappingArray;
    
    // Events for array operations
    event ArrayPushed(uint256 indexed index, uint256 value);
    event ArrayPopped(uint256 value);
    event ArrayUpdated(uint256 indexed index, uint256 oldValue, uint256 newValue);
    
    constructor() {
        // Initialize fixed arrays
        fixedArray[0] = 100;
        fixedArray[1] = 200;
        
        // Initialize dynamic arrays
        dynamicArray.push(1);
        dynamicArray.push(2);
        dynamicArray.push(3);
        
        // Initialize string array
        stringArray.push("first");
        stringArray.push("second");
        
        // Initialize 2D array
        twoDimensionalArray.push([1, 2, 3]);
        twoDimensionalArray.push([4, 5, 6]);
    }
    
    // Array manipulation functions
    function pushToDynamicArray(uint256 _value) public {
        dynamicArray.push(_value);
        emit ArrayPushed(dynamicArray.length - 1, _value);
    }
    
    function popFromDynamicArray() public returns (uint256) {
        require(dynamicArray.length > 0, "Array is empty");
        uint256 value = dynamicArray[dynamicArray.length - 1];
        dynamicArray.pop();
        emit ArrayPopped(value);
        return value;
    }
    
    function updateArrayElement(uint256 _index, uint256 _newValue) public {
        require(_index < dynamicArray.length, "Index out of bounds");
        uint256 oldValue = dynamicArray[_index];
        dynamicArray[_index] = _newValue;
        emit ArrayUpdated(_index, oldValue, _newValue);
    }
    
    function getArrayLength() public view returns (uint256) {
        return dynamicArray.length;
    }
    
    function getFixedArrayElement(uint256 _index) public view returns (uint256) {
        require(_index < 5, "Index out of bounds");
        return fixedArray[_index];
    }
    
    function setFixedArrayElement(uint256 _index, uint256 _value) public {
        require(_index < 5, "Index out of bounds");
        fixedArray[_index] = _value;
    }
    
    // Array slicing and memory operations
    function getArraySlice(uint256 _start, uint256 _end) public view returns (uint256[] memory) {
        require(_start <= _end && _end <= dynamicArray.length, "Invalid slice parameters");
        uint256[] memory slice = new uint256[](_end - _start);
        for (uint256 i = _start; i < _end; i++) {
            slice[i - _start] = dynamicArray[i];
        }
        return slice;
    }
    
    function concatenateArrays(uint256[] memory _array1, uint256[] memory _array2) 
        public 
        pure 
        returns (uint256[] memory) 
    {
        uint256[] memory result = new uint256[](_array1.length + _array2.length);
        uint256 index = 0;
        
        for (uint256 i = 0; i < _array1.length; i++) {
            result[index++] = _array1[i];
        }
        
        for (uint256 i = 0; i < _array2.length; i++) {
            result[index++] = _array2[i];
        }
        
        return result;
    }
    
    // Array with complex types
    function addItem(uint256 _id, string memory _name) public {
        itemArray.push(Item({id: _id, name: _name}));
    }
    
    function getItem(uint256 _index) public view returns (uint256, string memory) {
        require(_index < itemArray.length, "Index out of bounds");
        Item memory item = itemArray[_index];
        return (item.id, item.name);
    }
    
    // Array deletion
    function deleteArrayElement(uint256 _index) public {
        require(_index < dynamicArray.length, "Index out of bounds");
        
        // Move last element to deleted spot
        dynamicArray[_index] = dynamicArray[dynamicArray.length - 1];
        dynamicArray.pop();
    }
    
    function clearArray() public {
        delete dynamicArray;
    }
}
