// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Payable and Ether Handling Feature Test Contract
 * Tests payable functions, fallback, receive, and ether operations
 */
contract PayableContract {
    address public owner;
    uint256 public totalReceived;
    uint256 public totalWithdrawn;
    
    mapping(address => uint256) public deposits;
    mapping(address => uint256) public withdrawals;
    
    event EtherReceived(address indexed from, uint256 amount, string source);
    event EtherWithdrawn(address indexed to, uint256 amount);
    event FallbackCalled(address indexed from, uint256 amount, bytes data);
    event ReceiveCalled(address indexed from, uint256 amount);
    
    constructor() payable {
        owner = msg.sender;
        if (msg.value > 0) {
            totalReceived += msg.value;
            deposits[msg.sender] += msg.value;
            emit EtherReceived(msg.sender, msg.value, "constructor");
        }
    }
    
    // Receive function - called when plain ether is sent
    receive() external payable {
        totalReceived += msg.value;
        deposits[msg.sender] += msg.value;
        emit ReceiveCalled(msg.sender, msg.value);
        emit EtherReceived(msg.sender, msg.value, "receive");
    }
    
    // Fallback function - called when no function matches or data is sent
    fallback() external payable {
        totalReceived += msg.value;
        deposits[msg.sender] += msg.value;
        emit FallbackCalled(msg.sender, msg.value, msg.data);
        emit EtherReceived(msg.sender, msg.value, "fallback");
    }
    
    // Basic payable function
    function deposit() external payable {
        require(msg.value > 0, "Must send ether");
        totalReceived += msg.value;
        deposits[msg.sender] += msg.value;
        emit EtherReceived(msg.sender, msg.value, "deposit");
    }
    
    // Payable function with parameters
    function depositWithMessage(string memory message) external payable {
        require(msg.value > 0, "Must send ether");
        require(bytes(message).length > 0, "Message cannot be empty");
        
        totalReceived += msg.value;
        deposits[msg.sender] += msg.value;
        emit EtherReceived(msg.sender, msg.value, message);
    }
    
    // Payable function with minimum amount
    function depositMinimum(uint256 minAmount) external payable {
        require(msg.value >= minAmount, "Insufficient ether sent");
        
        totalReceived += msg.value;
        deposits[msg.sender] += msg.value;
        emit EtherReceived(msg.sender, msg.value, "deposit_minimum");
    }
    
    // Withdraw functions
    function withdraw(uint256 amount) external {
        require(deposits[msg.sender] >= amount, "Insufficient balance");
        require(address(this).balance >= amount, "Contract has insufficient balance");
        
        deposits[msg.sender] -= amount;
        totalWithdrawn += amount;
        withdrawals[msg.sender] += amount;
        
        payable(msg.sender).transfer(amount);
        emit EtherWithdrawn(msg.sender, amount);
    }
    
    function withdrawAll() external {
        uint256 amount = deposits[msg.sender];
        require(amount > 0, "No balance to withdraw");
        require(address(this).balance >= amount, "Contract has insufficient balance");
        
        deposits[msg.sender] = 0;
        totalWithdrawn += amount;
        withdrawals[msg.sender] += amount;
        
        payable(msg.sender).transfer(amount);
        emit EtherWithdrawn(msg.sender, amount);
    }
    
    // Owner-only withdraw
    function ownerWithdraw(uint256 amount) external {
        require(msg.sender == owner, "Only owner can call this");
        require(address(this).balance >= amount, "Insufficient contract balance");
        
        totalWithdrawn += amount;
        withdrawals[owner] += amount;
        
        payable(owner).transfer(amount);
        emit EtherWithdrawn(owner, amount);
    }
    
    // Send ether to another address
    function sendEther(address payable recipient, uint256 amount) external {
        require(msg.sender == owner, "Only owner can send ether");
        require(address(this).balance >= amount, "Insufficient contract balance");
        require(recipient != address(0), "Cannot send to zero address");
        
        totalWithdrawn += amount;
        withdrawals[recipient] += amount;
        
        recipient.transfer(amount);
        emit EtherWithdrawn(recipient, amount);
    }
    
    // Send ether using call (recommended method)
    function sendEtherCall(address payable recipient, uint256 amount) external returns (bool) {
        require(msg.sender == owner, "Only owner can send ether");
        require(address(this).balance >= amount, "Insufficient contract balance");
        require(recipient != address(0), "Cannot send to zero address");
        
        totalWithdrawn += amount;
        withdrawals[recipient] += amount;
        
        (bool success, ) = recipient.call{value: amount}("");
        if (success) {
            emit EtherWithdrawn(recipient, amount);
        } else {
            // Revert the accounting if transfer failed
            totalWithdrawn -= amount;
            withdrawals[recipient] -= amount;
        }
        
        return success;
    }
    
    // Send ether using send (returns bool)
    function sendEtherSend(address payable recipient, uint256 amount) external returns (bool) {
        require(msg.sender == owner, "Only owner can send ether");
        require(address(this).balance >= amount, "Insufficient contract balance");
        require(recipient != address(0), "Cannot send to zero address");
        
        totalWithdrawn += amount;
        withdrawals[recipient] += amount;
        
        bool success = recipient.send(amount);
        if (success) {
            emit EtherWithdrawn(recipient, amount);
        } else {
            // Revert the accounting if transfer failed
            totalWithdrawn -= amount;
            withdrawals[recipient] -= amount;
        }
        
        return success;
    }
    
    // Batch send ether
    function batchSendEther(address payable[] memory recipients, uint256[] memory amounts) external {
        require(msg.sender == owner, "Only owner can send ether");
        require(recipients.length == amounts.length, "Arrays length mismatch");
        
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            totalAmount += amounts[i];
        }
        
        require(address(this).balance >= totalAmount, "Insufficient contract balance");
        
        for (uint256 i = 0; i < recipients.length; i++) {
            require(recipients[i] != address(0), "Cannot send to zero address");
            
            totalWithdrawn += amounts[i];
            withdrawals[recipients[i]] += amounts[i];
            
            (bool success, ) = recipients[i].call{value: amounts[i]}("");
            if (success) {
                emit EtherWithdrawn(recipients[i], amounts[i]);
            } else {
                // Revert the accounting if transfer failed
                totalWithdrawn -= amounts[i];
                withdrawals[recipients[i]] -= amounts[i];
            }
        }
    }
    
    // Payable function with complex logic
    function investWithBonus() external payable {
        require(msg.value >= 0.1 ether, "Minimum investment is 0.1 ether");
        
        uint256 bonus = 0;
        if (msg.value >= 1 ether) {
            bonus = msg.value / 10; // 10% bonus
        } else if (msg.value >= 0.5 ether) {
            bonus = msg.value / 20; // 5% bonus
        }
        
        uint256 totalCredit = msg.value + bonus;
        totalReceived += msg.value;
        deposits[msg.sender] += totalCredit; // Credit includes bonus
        
        emit EtherReceived(msg.sender, msg.value, "investment");
    }
    
    // Function to check contract balance
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    // Function to check user balance
    function getUserBalance(address user) external view returns (uint256) {
        return deposits[user];
    }
    
    // Function to check if address can receive ether
    function canReceiveEther(address addr) external view returns (bool) {
        // Check if it's a contract
        uint256 size;
        assembly {
            size := extcodesize(addr)
        }
        
        if (size == 0) {
            return true; // EOA can always receive ether
        }
        
        // For contracts, we can't easily check if they have receive/fallback
        // This is a simplified check
        return true;
    }
    
    // Emergency functions
    function emergencyWithdraw() external {
        require(msg.sender == owner, "Only owner can emergency withdraw");
        uint256 balance = address(this).balance;
        totalWithdrawn += balance;
        withdrawals[owner] += balance;
        
        payable(owner).transfer(balance);
        emit EtherWithdrawn(owner, balance);
    }
    
    // Selfdestruct function (sends all ether to owner)
    function destroy() external {
        require(msg.sender == owner, "Only owner can destroy contract");
        selfdestruct(payable(owner));
    }
    
    // View functions
    function getStats() external view returns (
        uint256 contractBalance,
        uint256 totalRec,
        uint256 totalWith,
        uint256 userDeposit,
        uint256 userWithdrawal
    ) {
        return (
            address(this).balance,
            totalReceived,
            totalWithdrawn,
            deposits[msg.sender],
            withdrawals[msg.sender]
        );
    }
}
