// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Enum Feature Test Contract
 * Tests all enum-related language features
 */
contract EnumContract {
    // Basic enum
    enum Status {
        Pending,
        Active,
        Inactive,
        Suspended,
        Terminated
    }
    
    // Enum with explicit values (implicit in Solidity)
    enum Priority {
        Low,      // 0
        Medium,   // 1
        High,     // 2
        Critical  // 3
    }
    
    // Enum for order states
    enum OrderState {
        Created,
        Paid,
        Shipped,
        Delivered,
        Cancelled,
        Refunded
    }
    
    // State variables using enums
    Status public contractStatus;
    Priority public defaultPriority;
    
    // Mapping with enum keys
    mapping(Status => uint256) public statusCounts;
    mapping(address => Priority) public userPriorities;
    mapping(uint256 => OrderState) public orderStates;
    
    // Array of enums
    Status[] public statusHistory;
    Priority[] public priorityQueue;
    
    // Struct containing enums
    struct Task {
        uint256 id;
        string description;
        Priority priority;
        Status status;
        address assignee;
    }
    
    Task[] public tasks;
    mapping(uint256 => Task) public taskById;
    
    // Events with enum parameters
    event StatusChanged(Status indexed oldStatus, Status indexed newStatus);
    event PriorityUpdated(address indexed user, Priority indexed newPriority);
    event TaskCreated(uint256 indexed taskId, Priority priority, Status status);
    event OrderStateChanged(uint256 indexed orderId, OrderState indexed newState);
    
    constructor() {
        contractStatus = Status.Pending;
        defaultPriority = Priority.Medium;
        
        // Initialize status counts
        statusCounts[Status.Pending] = 1;
    }
    
    // Functions to change enum values
    function setStatus(Status _newStatus) public {
        Status oldStatus = contractStatus;
        contractStatus = _newStatus;
        
        // Update counts
        if (statusCounts[oldStatus] > 0) {
            statusCounts[oldStatus]--;
        }
        statusCounts[_newStatus]++;
        
        // Add to history
        statusHistory.push(_newStatus);
        
        emit StatusChanged(oldStatus, _newStatus);
    }
    
    function setPriority(Priority _priority) public {
        userPriorities[msg.sender] = _priority;
        priorityQueue.push(_priority);
        
        emit PriorityUpdated(msg.sender, _priority);
    }
    
    // Function with enum parameters and return values
    function getNextStatus(Status _currentStatus) public pure returns (Status) {
        if (_currentStatus == Status.Pending) {
            return Status.Active;
        } else if (_currentStatus == Status.Active) {
            return Status.Inactive;
        } else if (_currentStatus == Status.Inactive) {
            return Status.Suspended;
        } else if (_currentStatus == Status.Suspended) {
            return Status.Terminated;
        } else {
            return Status.Pending; // Reset cycle
        }
    }
    
    // Function to check enum equality
    function isHighPriority(Priority _priority) public pure returns (bool) {
        return _priority == Priority.High || _priority == Priority.Critical;
    }
    
    // Function with enum in conditional statements
    function getStatusDescription(Status _status) public pure returns (string memory) {
        if (_status == Status.Pending) {
            return "Waiting for activation";
        } else if (_status == Status.Active) {
            return "Currently active";
        } else if (_status == Status.Inactive) {
            return "Temporarily inactive";
        } else if (_status == Status.Suspended) {
            return "Suspended due to issues";
        } else if (_status == Status.Terminated) {
            return "Permanently terminated";
        } else {
            return "Unknown status";
        }
    }
    
    // Function using enum in switch-like pattern
    function getPriorityWeight(Priority _priority) public pure returns (uint256) {
        if (_priority == Priority.Low) {
            return 1;
        } else if (_priority == Priority.Medium) {
            return 5;
        } else if (_priority == Priority.High) {
            return 10;
        } else if (_priority == Priority.Critical) {
            return 20;
        } else {
            return 0;
        }
    }
    
    // Task management with enums
    function createTask(
        string memory _description,
        Priority _priority,
        address _assignee
    ) public returns (uint256) {
        uint256 taskId = tasks.length;
        
        Task memory newTask = Task({
            id: taskId,
            description: _description,
            priority: _priority,
            status: Status.Pending,
            assignee: _assignee
        });
        
        tasks.push(newTask);
        taskById[taskId] = newTask;
        
        emit TaskCreated(taskId, _priority, Status.Pending);
        
        return taskId;
    }
    
    function updateTaskStatus(uint256 _taskId, Status _newStatus) public {
        require(_taskId < tasks.length, "Task does not exist");
        
        tasks[_taskId].status = _newStatus;
        taskById[_taskId].status = _newStatus;
    }
    
    // Order management with enums
    function createOrder(uint256 _orderId) public {
        orderStates[_orderId] = OrderState.Created;
        emit OrderStateChanged(_orderId, OrderState.Created);
    }
    
    function updateOrderState(uint256 _orderId, OrderState _newState) public {
        require(orderStates[_orderId] != OrderState.Cancelled, "Cannot update cancelled order");
        require(orderStates[_orderId] != OrderState.Refunded, "Cannot update refunded order");
        
        orderStates[_orderId] = _newState;
        emit OrderStateChanged(_orderId, _newState);
    }
    
    // Enum conversion functions
    function statusToUint(Status _status) public pure returns (uint256) {
        return uint256(_status);
    }
    
    function uintToStatus(uint256 _value) public pure returns (Status) {
        require(_value <= uint256(Status.Terminated), "Invalid status value");
        return Status(_value);
    }
    
    // Enum array operations
    function getStatusHistoryLength() public view returns (uint256) {
        return statusHistory.length;
    }
    
    function getLastStatus() public view returns (Status) {
        require(statusHistory.length > 0, "No status history");
        return statusHistory[statusHistory.length - 1];
    }
    
    // Complex enum logic
    function canTransitionTo(Status _from, Status _to) public pure returns (bool) {
        // Define valid transitions
        if (_from == Status.Pending) {
            return _to == Status.Active || _to == Status.Terminated;
        } else if (_from == Status.Active) {
            return _to == Status.Inactive || _to == Status.Suspended || _to == Status.Terminated;
        } else if (_from == Status.Inactive) {
            return _to == Status.Active || _to == Status.Terminated;
        } else if (_from == Status.Suspended) {
            return _to == Status.Active || _to == Status.Terminated;
        } else {
            return false; // Cannot transition from Terminated
        }
    }
    
    // Getter functions
    function getTask(uint256 _taskId) public view returns (
        uint256 id,
        string memory description,
        Priority priority,
        Status status,
        address assignee
    ) {
        require(_taskId < tasks.length, "Task does not exist");
        Task memory task = tasks[_taskId];
        return (task.id, task.description, task.priority, task.status, task.assignee);
    }
    
    function getTaskCount() public view returns (uint256) {
        return tasks.length;
    }
}
