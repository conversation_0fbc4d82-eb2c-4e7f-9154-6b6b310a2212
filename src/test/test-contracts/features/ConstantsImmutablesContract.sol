// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;
pragma experimental ABIEncoderV2;

// Import statements for dependency analysis
import "./InterfaceLibraryContract.sol";
import "./PayableContract.sol";

/**
 * Constants and Immutables Test Contract
 * Tests constants, immutable variables, pragma directives, and imports
 */
contract ConstantsImmutablesContract {
    // Constants - compile-time values
    uint256 public constant MAX_SUPPLY = 1_000_000_000;
    uint256 public constant MIN_DEPOSIT = 0.01 ether;
    uint256 public constant PERCENTAGE_BASE = 10000; // 100.00%
    string public constant CONTRACT_NAME = "ConstantsImmutablesContract";
    bytes32 public constant DOMAIN_SEPARATOR = keccak256("ConstantsImmutablesContract.v1");
    address public constant ZERO_ADDRESS = address(0);
    address public constant BURN_ADDRESS = ******************************************;

    // Mathematical constants
    uint256 public constant SECONDS_PER_DAY = 86400;
    uint256 public constant SECONDS_PER_YEAR = 365 * SECONDS_PER_DAY;
    uint256 public constant MAX_INT = 2 ** 256 - 1;

    // Protocol constants
    uint256 public constant PROTOCOL_FEE = 300; // 3%
    uint256 public constant MAX_SLIPPAGE = 1000; // 10%
    uint256 public constant LIQUIDATION_THRESHOLD = 8000; // 80%

    // Immutable variables - set once in constructor
    uint256 public immutable DEPLOYMENT_TIMESTAMP;
    address public immutable DEPLOYER;
    address public immutable FACTORY;
    bytes32 public immutable CREATION_CODE_HASH;
    uint256 public immutable CHAIN_ID;

    // Contract-specific immutables
    address public immutable TOKEN_A;
    address public immutable TOKEN_B;
    uint256 public immutable INITIAL_SUPPLY;
    bytes32 public immutable VERSION_HASH;

    // Complex immutable calculations
    uint256 public immutable DEPLOYMENT_BLOCK;
    bytes32 public immutable DEPLOYMENT_HASH;
    address public immutable COMPUTED_ADDRESS;

    // State variables for comparison
    uint256 public mutableCounter;
    address public mutableOwner;
    string public mutableName;

    // Events
    event ConstantAccessed(string constantName, uint256 value);
    event ImmutableAccessed(string immutableName, uint256 value);
    event MutableUpdated(string variableName, uint256 newValue);

    constructor(address _tokenA, address _tokenB, uint256 _initialSupply, string memory _version, address _factory) {
        // Set immutable variables
        DEPLOYMENT_TIMESTAMP = block.timestamp;
        DEPLOYER = msg.sender;
        FACTORY = _factory;
        CREATION_CODE_HASH = keccak256(abi.encodePacked("ConstantsImmutablesContract", block.timestamp));
        CHAIN_ID = block.chainid;

        // Contract-specific immutables
        TOKEN_A = _tokenA;
        TOKEN_B = _tokenB;
        INITIAL_SUPPLY = _initialSupply;
        VERSION_HASH = keccak256(bytes(_version));

        // Complex calculations for immutables
        DEPLOYMENT_BLOCK = block.number;
        DEPLOYMENT_HASH = keccak256(abi.encodePacked(block.timestamp, msg.sender, _tokenA, _tokenB));
        COMPUTED_ADDRESS = address(
            uint160(
                uint256(
                    keccak256(
                        abi.encodePacked(bytes1(0xff), _factory, bytes32(block.timestamp), keccak256("sample_bytecode"))
                    )
                )
            )
        );

        // Initialize mutable variables
        mutableCounter = 0;
        mutableOwner = msg.sender;
        mutableName = "Initial Name";
    }

    // Functions to access constants
    function getSupplyConstants()
        external
        pure
        returns (uint256 maxSupply, uint256 minDeposit, uint256 percentageBase)
    {
        maxSupply = MAX_SUPPLY;
        minDeposit = MIN_DEPOSIT;
        percentageBase = PERCENTAGE_BASE;
    }

    function getAddressConstants() external pure returns (address zeroAddr, address burnAddr) {
        zeroAddr = ZERO_ADDRESS;
        burnAddr = BURN_ADDRESS;
    }

    function getTimeConstants() external pure returns (uint256 secondsPerDay, uint256 secondsPerYear, uint256 maxInt) {
        secondsPerDay = SECONDS_PER_DAY;
        secondsPerYear = SECONDS_PER_YEAR;
        maxInt = MAX_INT;
    }

    function getProtocolConstants()
        external
        pure
        returns (uint256 protocolFee, uint256 maxSlippage, uint256 liquidationThreshold)
    {
        protocolFee = PROTOCOL_FEE;
        maxSlippage = MAX_SLIPPAGE;
        liquidationThreshold = LIQUIDATION_THRESHOLD;
    }

    function getStringConstants() external pure returns (string memory contractName, bytes32 domainSeparator) {
        contractName = CONTRACT_NAME;
        domainSeparator = DOMAIN_SEPARATOR;
    }

    // Functions to access immutables
    function getDeploymentImmutables()
        external
        view
        returns (
            uint256 deploymentTimestamp,
            address deployer,
            address factory,
            bytes32 creationCodeHash,
            uint256 chainId
        )
    {
        deploymentTimestamp = DEPLOYMENT_TIMESTAMP;
        deployer = DEPLOYER;
        factory = FACTORY;
        creationCodeHash = CREATION_CODE_HASH;
        chainId = CHAIN_ID;
    }

    function getTokenImmutables()
        external
        view
        returns (address tokenA, address tokenB, uint256 initialSupply, bytes32 versionHash)
    {
        tokenA = TOKEN_A;
        tokenB = TOKEN_B;
        initialSupply = INITIAL_SUPPLY;
        versionHash = VERSION_HASH;
    }

    function getComputedImmutables()
        external
        view
        returns (uint256 deploymentBlock, bytes32 deploymentHash, address computedAddress)
    {
        deploymentBlock = DEPLOYMENT_BLOCK;
        deploymentHash = DEPLOYMENT_HASH;
        computedAddress = COMPUTED_ADDRESS;
    }

    // Functions to demonstrate constant vs immutable vs mutable
    function constantCalculation() external pure returns (uint256) {
        // Constants can be used in compile-time calculations
        return MAX_SUPPLY * PROTOCOL_FEE / PERCENTAGE_BASE;
    }

    function immutableCalculation() external view returns (uint256) {
        // Immutables can be used in runtime calculations
        return INITIAL_SUPPLY * (block.timestamp - DEPLOYMENT_TIMESTAMP) / SECONDS_PER_DAY;
    }

    function mutableCalculation() external view returns (uint256) {
        // Mutable variables change over time
        return mutableCounter * (block.timestamp - DEPLOYMENT_TIMESTAMP);
    }

    // Functions to update mutable variables (for comparison)
    function incrementCounter() external {
        mutableCounter++;
        emit MutableUpdated("mutableCounter", mutableCounter);
    }

    function updateMutableOwner(address newOwner) external {
        require(msg.sender == mutableOwner, "Only current owner");
        mutableOwner = newOwner;
    }

    function updateMutableName(string memory newName) external {
        require(msg.sender == mutableOwner, "Only owner");
        mutableName = newName;
    }

    // Gas comparison functions
    function accessConstant() external pure returns (uint256) {
        // Very cheap - no storage read
        return MAX_SUPPLY;
    }

    function accessImmutable() external view returns (uint256) {
        // Cheap - no storage read, but not compile-time
        return DEPLOYMENT_TIMESTAMP;
    }

    function accessMutable() external view returns (uint256) {
        // Expensive - requires storage read
        return mutableCounter;
    }

    // Complex operations using constants and immutables
    function calculateFee(uint256 amount) external pure returns (uint256) {
        require(amount <= MAX_SUPPLY, "Amount exceeds max supply");
        return amount * PROTOCOL_FEE / PERCENTAGE_BASE;
    }

    function isValidDeposit(uint256 amount) external pure returns (bool) {
        return amount >= MIN_DEPOSIT && amount <= MAX_SUPPLY;
    }

    function getContractAge() external view returns (uint256) {
        return block.timestamp - DEPLOYMENT_TIMESTAMP;
    }

    function isDeployedBy(address account) external view returns (bool) {
        return account == DEPLOYER;
    }

    function getTokenPair() external view returns (address, address) {
        return (TOKEN_A, TOKEN_B);
    }

    // Function demonstrating all three types
    function getAllTypes()
        external
        view
        returns (
            uint256 constantValue,
            uint256 immutableValue,
            uint256 mutableValue,
            string memory constantString,
            bytes32 immutableHash,
            string memory mutableString
        )
    {
        constantValue = MAX_SUPPLY;
        immutableValue = DEPLOYMENT_TIMESTAMP;
        mutableValue = mutableCounter;
        constantString = CONTRACT_NAME;
        immutableHash = VERSION_HASH;
        mutableString = mutableName;
    }

    // Function to emit events for testing
    function emitConstantEvent() external {
        emit ConstantAccessed("MAX_SUPPLY", MAX_SUPPLY);
    }

    function emitImmutableEvent() external {
        emit ImmutableAccessed("DEPLOYMENT_TIMESTAMP", DEPLOYMENT_TIMESTAMP);
    }

    function emitMutableEvent() external {
        emit MutableUpdated("mutableCounter", mutableCounter);
    }
}
