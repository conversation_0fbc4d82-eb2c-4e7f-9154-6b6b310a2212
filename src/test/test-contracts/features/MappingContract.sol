// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Mapping Feature Test Contract
 * Tests all mapping-related language features
 */
contract MappingContract {
    // Basic mappings
    mapping(address => uint256) public balances;
    mapping(uint256 => string) public idToName;
    mapping(string => bool) public nameExists;
    mapping(bytes32 => address) public hashToAddress;
    
    // Nested mappings
    mapping(address => mapping(uint256 => bool)) public userTokenApprovals;
    mapping(uint256 => mapping(string => uint256)) public categoryScores;
    mapping(address => mapping(address => uint256)) public allowances;
    
    // Complex key types
    mapping(bytes => uint256) public bytesToValue;
    mapping(address => mapping(bytes32 => mapping(uint256 => bool))) public tripleNested;
    
    // Mappings with struct values
    struct UserInfo {
        string name;
        uint256 age;
        bool isActive;
        uint256[] scores;
    }
    
    mapping(address => UserInfo) public userInfo;
    mapping(uint256 => UserInfo) public userById;
    
    // Mappings with array values
    mapping(address => uint256[]) public userScores;
    mapping(string => address[]) public categoryUsers;
    mapping(uint256 => string[]) public groupMessages;
    
    // Mappings with mapping values (nested)
    mapping(address => mapping(string => mapping(uint256 => bool))) public permissions;
    
    // Events for mapping operations
    event BalanceUpdated(address indexed user, uint256 newBalance);
    event ApprovalSet(address indexed user, uint256 indexed tokenId, bool approved);
    event UserInfoUpdated(address indexed user, string name, uint256 age);
    event PermissionChanged(address indexed user, string indexed permission, uint256 indexed level, bool granted);
    
    constructor() {
        // Initialize some mappings
        balances[msg.sender] = 1000;
        idToName[1] = "Admin";
        nameExists["Admin"] = true;
        
        userInfo[msg.sender] = UserInfo({
            name: "Contract Owner",
            age: 30,
            isActive: true,
            scores: new uint256[](0)
        });
    }
    
    // Basic mapping operations
    function setBalance(address _user, uint256 _amount) public {
        balances[_user] = _amount;
        emit BalanceUpdated(_user, _amount);
    }
    
    function getBalance(address _user) public view returns (uint256) {
        return balances[_user];
    }
    
    function addToBalance(address _user, uint256 _amount) public {
        balances[_user] += _amount;
        emit BalanceUpdated(_user, balances[_user]);
    }
    
    // String key mappings
    function setNameMapping(uint256 _id, string memory _name) public {
        idToName[_id] = _name;
        nameExists[_name] = true;
    }
    
    function checkNameExists(string memory _name) public view returns (bool) {
        return nameExists[_name];
    }
    
    // Nested mapping operations
    function setApproval(uint256 _tokenId, bool _approved) public {
        userTokenApprovals[msg.sender][_tokenId] = _approved;
        emit ApprovalSet(msg.sender, _tokenId, _approved);
    }
    
    function isApproved(address _user, uint256 _tokenId) public view returns (bool) {
        return userTokenApprovals[_user][_tokenId];
    }
    
    function setAllowance(address _spender, uint256 _amount) public {
        allowances[msg.sender][_spender] = _amount;
    }
    
    function getAllowance(address _owner, address _spender) public view returns (uint256) {
        return allowances[_owner][_spender];
    }
    
    // Complex nested mapping operations
    function setCategoryScore(uint256 _category, string memory _subcategory, uint256 _score) public {
        categoryScores[_category][_subcategory] = _score;
    }
    
    function getCategoryScore(uint256 _category, string memory _subcategory) public view returns (uint256) {
        return categoryScores[_category][_subcategory];
    }
    
    // Struct value mappings
    function setUserInfo(string memory _name, uint256 _age, bool _isActive) public {
        userInfo[msg.sender] = UserInfo({
            name: _name,
            age: _age,
            isActive: _isActive,
            scores: new uint256[](0)
        });
        emit UserInfoUpdated(msg.sender, _name, _age);
    }
    
    function getUserInfo(address _user) public view returns (
        string memory name,
        uint256 age,
        bool isActive,
        uint256 scoresLength
    ) {
        UserInfo memory info = userInfo[_user];
        return (info.name, info.age, info.isActive, info.scores.length);
    }
    
    function addUserScore(uint256 _score) public {
        userInfo[msg.sender].scores.push(_score);
        userScores[msg.sender].push(_score);
    }
    
    // Array value mappings
    function addUserToCategory(string memory _category) public {
        categoryUsers[_category].push(msg.sender);
    }
    
    function getCategoryUsersCount(string memory _category) public view returns (uint256) {
        return categoryUsers[_category].length;
    }
    
    function getCategoryUser(string memory _category, uint256 _index) public view returns (address) {
        require(_index < categoryUsers[_category].length, "Index out of bounds");
        return categoryUsers[_category][_index];
    }
    
    function addGroupMessage(uint256 _groupId, string memory _message) public {
        groupMessages[_groupId].push(_message);
    }
    
    function getGroupMessagesCount(uint256 _groupId) public view returns (uint256) {
        return groupMessages[_groupId].length;
    }
    
    // Triple nested mapping operations
    function setPermission(string memory _permission, uint256 _level, bool _granted) public {
        permissions[msg.sender][_permission][_level] = _granted;
        emit PermissionChanged(msg.sender, _permission, _level, _granted);
    }
    
    function hasPermission(address _user, string memory _permission, uint256 _level) public view returns (bool) {
        return permissions[_user][_permission][_level];
    }
    
    // Bytes key mappings
    function setBytesValue(bytes memory _key, uint256 _value) public {
        bytesToValue[_key] = _value;
    }
    
    function getBytesValue(bytes memory _key) public view returns (uint256) {
        return bytesToValue[_key];
    }
    
    // Hash-based mappings
    function setHashAddress(bytes32 _hash, address _addr) public {
        hashToAddress[_hash] = _addr;
    }
    
    function getHashAddress(bytes32 _hash) public view returns (address) {
        return hashToAddress[_hash];
    }
    
    // Mapping deletion operations
    function deleteBalance(address _user) public {
        delete balances[_user];
        emit BalanceUpdated(_user, 0);
    }
    
    function deleteUserInfo(address _user) public {
        delete userInfo[_user];
    }
    
    function deleteApproval(uint256 _tokenId) public {
        delete userTokenApprovals[msg.sender][_tokenId];
        emit ApprovalSet(msg.sender, _tokenId, false);
    }
    
    // Mapping iteration helpers (since mappings are not iterable)
    address[] public userList;
    uint256[] public idList;
    string[] public nameList;
    
    function addUser(address _user) public {
        userList.push(_user);
    }
    
    function addId(uint256 _id) public {
        idList.push(_id);
    }
    
    function addName(string memory _name) public {
        nameList.push(_name);
    }
    
    function getUserCount() public view returns (uint256) {
        return userList.length;
    }
    
    function getUser(uint256 _index) public view returns (address) {
        require(_index < userList.length, "Index out of bounds");
        return userList[_index];
    }
    
    // Complex mapping operations
    function batchSetBalances(address[] memory _users, uint256[] memory _amounts) public {
        require(_users.length == _amounts.length, "Arrays length mismatch");
        
        for (uint256 i = 0; i < _users.length; i++) {
            balances[_users[i]] = _amounts[i];
            emit BalanceUpdated(_users[i], _amounts[i]);
        }
    }
    
    function transferBalance(address _to, uint256 _amount) public {
        require(balances[msg.sender] >= _amount, "Insufficient balance");
        
        balances[msg.sender] -= _amount;
        balances[_to] += _amount;
        
        emit BalanceUpdated(msg.sender, balances[msg.sender]);
        emit BalanceUpdated(_to, balances[_to]);
    }
    
    // Mapping with computed keys
    function setComputedValue(address _user, uint256 _id, uint256 _value) public {
        bytes32 key = keccak256(abi.encodePacked(_user, _id));
        hashToAddress[key] = address(uint160(_value));
    }
    
    function getComputedValue(address _user, uint256 _id) public view returns (address) {
        bytes32 key = keccak256(abi.encodePacked(_user, _id));
        return hashToAddress[key];
    }
}
