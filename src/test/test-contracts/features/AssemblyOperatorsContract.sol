// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Assembly, Operators, and Type Aliases Test Contract
 * Tests inline assembly, operator overloading patterns, and type aliases
 */
contract AssemblyOperatorsContract {
    // Type aliases using 'using' statements
    using SafeMath for uint256;
    using AddressUtils for address;
    using BytesLib for bytes;
    
    // Custom type definitions (Solidity 0.8.8+)
    type Price is uint256;
    type Quantity is uint256;
    type UserId is uint256;
    
    // State variables
    uint256 public value;
    address public owner;
    bytes public data;
    
    // Custom types usage
    Price public currentPrice;
    Quantity public totalQuantity;
    mapping(UserId => Price) public userPrices;
    
    // Events
    event AssemblyOperation(string operation, uint256 input, uint256 output);
    event TypeOperation(string operation, uint256 value);
    event OperatorUsed(string operator, uint256 a, uint256 b, uint256 result);
    
    constructor() {
        owner = msg.sender;
        currentPrice = Price.wrap(100);
        totalQuantity = Quantity.wrap(1000);
    }
    
    // Assembly block examples
    function assemblyBasicOperations(uint256 a, uint256 b) external returns (uint256 result) {
        assembly {
            // Basic arithmetic
            result := add(a, b)
            
            // Store result in memory
            let memPtr := mload(0x40)
            mstore(memPtr, result)
            
            // Update free memory pointer
            mstore(0x40, add(memPtr, 0x20))
        }
        
        emit AssemblyOperation("add", a, result);
    }
    
    function assemblyMemoryOperations(bytes memory input) external returns (bytes32 hash) {
        assembly {
            // Get input length and data pointer
            let len := mload(input)
            let dataPtr := add(input, 0x20)
            
            // Calculate keccak256 hash
            hash := keccak256(dataPtr, len)
            
            // Store hash in storage slot 0
            sstore(0, hash)
        }
        
        emit AssemblyOperation("keccak256", input.length, uint256(hash));
    }
    
    function assemblyStorageOperations(uint256 slot, uint256 newValue) external returns (uint256 oldValue) {
        assembly {
            // Read from storage
            oldValue := sload(slot)
            
            // Write to storage
            sstore(slot, newValue)
            
            // Emit log
            log2(0, 0, 0x1234567890abcdef, oldValue)
        }
        
        emit AssemblyOperation("storage", slot, newValue);
    }
    
    function assemblyCallOperations(address target, bytes memory callData) 
        external 
        returns (bool success, bytes memory returnData) 
    {
        assembly {
            // Prepare call
            let dataPtr := add(callData, 0x20)
            let dataSize := mload(callData)
            
            // Make call
            success := call(
                gas(),          // gas
                target,         // address
                0,              // value
                dataPtr,        // input data pointer
                dataSize,       // input data size
                0,              // output data pointer
                0               // output data size
            )
            
            // Get return data
            let returnSize := returndatasize()
            returnData := mload(0x40)
            mstore(returnData, returnSize)
            returndatacopy(add(returnData, 0x20), 0, returnSize)
            mstore(0x40, add(add(returnData, 0x20), returnSize))
        }
        
        emit AssemblyOperation("call", uint256(uint160(target)), success ? 1 : 0);
    }
    
    function assemblyLoopOperations(uint256[] memory array) external returns (uint256 sum) {
        assembly {
            // Get array length and data pointer
            let len := mload(array)
            let dataPtr := add(array, 0x20)
            
            // Initialize sum
            sum := 0
            
            // Loop through array
            for { let i := 0 } lt(i, len) { i := add(i, 1) } {
                let element := mload(add(dataPtr, mul(i, 0x20)))
                sum := add(sum, element)
            }
        }
        
        emit AssemblyOperation("loop_sum", array.length, sum);
    }
    
    function assemblyConditionalOperations(uint256 a, uint256 b) external returns (uint256 result) {
        assembly {
            // Conditional logic
            switch gt(a, b)
            case 1 {
                result := sub(a, b)
            }
            default {
                result := sub(b, a)
            }
            
            // Another conditional using if
            if eq(result, 0) {
                result := 1
            }
        }
        
        emit AssemblyOperation("conditional", a, result);
    }
    
    // Type alias operations using custom types
    function typeAliasOperations(Price price, Quantity qty) external returns (Price totalValue) {
        // Unwrap custom types
        uint256 priceValue = Price.unwrap(price);
        uint256 qtyValue = Quantity.unwrap(qty);
        
        // Calculate total value
        uint256 total = priceValue * qtyValue;
        
        // Wrap back to custom type
        totalValue = Price.wrap(total);
        
        emit TypeOperation("multiply", total);
    }
    
    function userTypeOperations(UserId userId, Price price) external {
        // Store user price
        userPrices[userId] = price;
        
        // Update current price
        currentPrice = price;
        
        emit TypeOperation("user_price", Price.unwrap(price));
    }
    
    // Operator overloading patterns using libraries
    function operatorOverloadingPatterns(uint256 a, uint256 b) external returns (uint256) {
        // Using SafeMath library (operator overloading pattern)
        uint256 sum = a.add(b);
        uint256 product = a.mul(b);
        uint256 difference = a.sub(b);
        uint256 quotient = a.div(b);
        
        emit OperatorUsed("add", a, b, sum);
        emit OperatorUsed("mul", a, b, product);
        emit OperatorUsed("sub", a, b, difference);
        emit OperatorUsed("div", a, b, quotient);
        
        return sum;
    }
    
    function addressOperatorPatterns(address addr, bytes memory data) external returns (bool) {
        // Using AddressUtils library
        bool isContract = addr.isContract();
        
        if (isContract) {
            bytes memory result = addr.functionCall(data);
            emit TypeOperation("function_call", result.length);
        }
        
        return isContract;
    }
    
    function bytesOperatorPatterns(bytes memory input) external returns (bytes32) {
        // Using BytesLib library
        bytes32 hash = input.toBytes32();
        bytes memory sliced = input.slice(0, 10);
        
        emit TypeOperation("bytes_ops", sliced.length);
        return hash;
    }
    
    // Complex assembly with multiple blocks
    function complexAssemblyOperations(uint256 x, uint256 y) external returns (uint256 result1, uint256 result2) {
        // First assembly block
        assembly {
            result1 := mul(x, y)
            
            // Nested assembly-like operations
            let temp := add(x, y)
            result1 := div(result1, temp)
        }
        
        // Second assembly block
        assembly {
            result2 := mod(x, y)
            
            // Complex calculation
            let squared := mul(x, x)
            let cubed := mul(squared, x)
            result2 := add(result2, cubed)
        }
        
        emit AssemblyOperation("complex", x, result1);
    }
    
    // Assembly with function definitions
    function assemblyWithFunctions(uint256 input) external returns (uint256 output) {
        assembly {
            // Define assembly function
            function double(val) -> doubled {
                doubled := mul(val, 2)
            }
            
            function square(val) -> squared {
                squared := mul(val, val)
            }
            
            // Use assembly functions
            let doubled := double(input)
            let squared := square(input)
            output := add(doubled, squared)
        }
        
        emit AssemblyOperation("functions", input, output);
    }
    
    // View functions for type checking
    function getCustomTypes() external view returns (uint256 price, uint256 quantity) {
        price = Price.unwrap(currentPrice);
        quantity = Quantity.unwrap(totalQuantity);
    }
    
    function getUserPrice(UserId userId) external view returns (uint256) {
        return Price.unwrap(userPrices[userId]);
    }
}

// Library for SafeMath operations (operator overloading pattern)
library SafeMath {
    function add(uint256 a, uint256 b) internal pure returns (uint256) {
        uint256 c = a + b;
        require(c >= a, "SafeMath: addition overflow");
        return c;
    }
    
    function sub(uint256 a, uint256 b) internal pure returns (uint256) {
        require(b <= a, "SafeMath: subtraction underflow");
        return a - b;
    }
    
    function mul(uint256 a, uint256 b) internal pure returns (uint256) {
        if (a == 0) return 0;
        uint256 c = a * b;
        require(c / a == b, "SafeMath: multiplication overflow");
        return c;
    }
    
    function div(uint256 a, uint256 b) internal pure returns (uint256) {
        require(b > 0, "SafeMath: division by zero");
        return a / b;
    }
}

// Library for Address operations
library AddressUtils {
    function isContract(address account) internal view returns (bool) {
        uint256 size;
        assembly {
            size := extcodesize(account)
        }
        return size > 0;
    }
    
    function functionCall(address target, bytes memory data) internal returns (bytes memory) {
        (bool success, bytes memory returndata) = target.call(data);
        require(success, "Address: low-level call failed");
        return returndata;
    }
}

// Library for Bytes operations
library BytesLib {
    function toBytes32(bytes memory input) internal pure returns (bytes32 result) {
        assembly {
            result := mload(add(input, 32))
        }
    }
    
    function slice(bytes memory input, uint256 start, uint256 length) internal pure returns (bytes memory) {
        require(start + length <= input.length, "BytesLib: slice out of bounds");
        
        bytes memory result = new bytes(length);
        assembly {
            let src := add(add(input, 0x20), start)
            let dst := add(result, 0x20)
            
            for { let i := 0 } lt(i, length) { i := add(i, 1) } {
                mstore8(add(dst, i), byte(0, mload(add(src, i))))
            }
        }
        
        return result;
    }
}
