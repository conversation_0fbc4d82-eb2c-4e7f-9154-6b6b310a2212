// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Error Feature Test Contract
 * Tests custom errors, error handling, and revert patterns
 */
contract ErrorContract {
    // Custom errors with different parameter types
    error InsufficientBalance(address account, uint256 requested, uint256 available);
    error UnauthorizedAccess(address caller, address required);
    error InvalidInput(string parameter, uint256 value, string reason);
    error TransferFailed(address from, address to, uint256 amount);
    error ContractPaused();
    error ZeroAddress();
    error ArrayIndexOutOfBounds(uint256 index, uint256 length);
    error DivisionByZero();
    error Overflow(uint256 a, uint256 b);
    error StringTooLong(uint256 length, uint256 maxLength);
    
    // State variables
    mapping(address => uint256) public balances;
    address public owner;
    bool public paused;
    uint256 public totalSupply;
    string[] public messages;
    
    // Events for error tracking
    event ErrorOccurred(string errorType, address indexed user);
    event BalanceUpdated(address indexed account, uint256 newBalance);
    
    modifier onlyOwner() {
        if (msg.sender != owner) {
            revert UnauthorizedAccess(msg.sender, owner);
        }
        _;
    }
    
    modifier whenNotPaused() {
        if (paused) {
            revert ContractPaused();
        }
        _;
    }
    
    modifier validAddress(address _addr) {
        if (_addr == address(0)) {
            revert ZeroAddress();
        }
        _;
    }
    
    constructor() {
        owner = msg.sender;
        balances[msg.sender] = 1000;
        totalSupply = 1000;
        paused = false;
    }
    
    // Function that uses custom error with multiple parameters
    function transfer(address _to, uint256 _amount) 
        public 
        whenNotPaused 
        validAddress(_to) 
    {
        if (balances[msg.sender] < _amount) {
            revert InsufficientBalance(msg.sender, _amount, balances[msg.sender]);
        }
        
        balances[msg.sender] -= _amount;
        balances[_to] += _amount;
        
        emit BalanceUpdated(msg.sender, balances[msg.sender]);
        emit BalanceUpdated(_to, balances[_to]);
    }
    
    // Function with input validation errors
    function setBalance(address _account, uint256 _amount) 
        public 
        onlyOwner 
        validAddress(_account) 
    {
        if (_amount > 1000000) {
            revert InvalidInput("amount", _amount, "exceeds maximum allowed");
        }
        
        balances[_account] = _amount;
        emit BalanceUpdated(_account, _amount);
    }
    
    // Function with array bounds checking
    function getMessage(uint256 _index) public view returns (string memory) {
        if (_index >= messages.length) {
            revert ArrayIndexOutOfBounds(_index, messages.length);
        }
        return messages[_index];
    }
    
    function addMessage(string memory _message) public {
        if (bytes(_message).length > 100) {
            revert StringTooLong(bytes(_message).length, 100);
        }
        messages.push(_message);
    }
    
    // Mathematical operations with error handling
    function safeDivide(uint256 _a, uint256 _b) public pure returns (uint256) {
        if (_b == 0) {
            revert DivisionByZero();
        }
        return _a / _b;
    }
    
    function safeMultiply(uint256 _a, uint256 _b) public pure returns (uint256) {
        if (_a != 0 && _b > type(uint256).max / _a) {
            revert Overflow(_a, _b);
        }
        return _a * _b;
    }
    
    // Function with try-catch for external calls
    function attemptTransfer(address _to, uint256 _amount) public returns (bool success) {
        try this.transfer(_to, _amount) {
            return true;
        } catch Error(string memory reason) {
            emit ErrorOccurred(reason, msg.sender);
            return false;
        } catch (bytes memory lowLevelData) {
            emit ErrorOccurred("Low level error", msg.sender);
            return false;
        }
    }
    
    // Function that demonstrates different revert patterns
    function demonstrateReverts(uint256 _option) public view {
        if (_option == 1) {
            // Traditional require
            require(false, "Traditional require revert");
        } else if (_option == 2) {
            // Custom error
            revert ContractPaused();
        } else if (_option == 3) {
            // Custom error with parameters
            revert InsufficientBalance(msg.sender, 100, 50);
        } else if (_option == 4) {
            // Assert (should not be used for user input validation)
            assert(false);
        } else if (_option == 5) {
            // Revert with string
            revert("Custom revert message");
        }
    }
    
    // Function with nested error conditions
    function complexOperation(uint256 _value, address _recipient) 
        public 
        whenNotPaused 
        returns (bool) 
    {
        if (_value == 0) {
            revert InvalidInput("value", _value, "cannot be zero");
        }
        
        if (_recipient == address(0)) {
            revert ZeroAddress();
        }
        
        if (_recipient == msg.sender) {
            revert InvalidInput("recipient", uint256(uint160(_recipient)), "cannot be sender");
        }
        
        if (balances[msg.sender] < _value) {
            revert InsufficientBalance(msg.sender, _value, balances[msg.sender]);
        }
        
        // Simulate complex operation that might fail
        if (_value > 500) {
            revert TransferFailed(msg.sender, _recipient, _value);
        }
        
        balances[msg.sender] -= _value;
        balances[_recipient] += _value;
        
        return true;
    }
    
    // Admin functions with authorization errors
    function pause() public onlyOwner {
        paused = true;
    }
    
    function unpause() public onlyOwner {
        paused = false;
    }
    
    function mint(address _to, uint256 _amount) 
        public 
        onlyOwner 
        validAddress(_to) 
        whenNotPaused 
    {
        if (_amount > 1000) {
            revert InvalidInput("amount", _amount, "exceeds mint limit");
        }
        
        balances[_to] += _amount;
        totalSupply += _amount;
        
        emit BalanceUpdated(_to, balances[_to]);
    }
    
    // Function that catches and re-throws errors
    function safeComplexOperation(uint256 _value, address _recipient) 
        public 
        returns (bool success, string memory errorMessage) 
    {
        try this.complexOperation(_value, _recipient) returns (bool result) {
            return (result, "");
        } catch InsufficientBalance(address account, uint256 requested, uint256 available) {
            return (false, string(abi.encodePacked(
                "Insufficient balance: requested ", 
                uint2str(requested), 
                " but only ", 
                uint2str(available), 
                " available"
            )));
        } catch UnauthorizedAccess(address caller, address required) {
            return (false, "Unauthorized access");
        } catch Error(string memory reason) {
            return (false, reason);
        } catch (bytes memory) {
            return (false, "Unknown error occurred");
        }
    }
    
    // Utility function for error messages
    function uint2str(uint256 _i) internal pure returns (string memory) {
        if (_i == 0) {
            return "0";
        }
        uint256 j = _i;
        uint256 len;
        while (j != 0) {
            len++;
            j /= 10;
        }
        bytes memory bstr = new bytes(len);
        uint256 k = len;
        while (_i != 0) {
            k = k - 1;
            uint8 temp = (48 + uint8(_i - _i / 10 * 10));
            bytes1 b1 = bytes1(temp);
            bstr[k] = b1;
            _i /= 10;
        }
        return string(bstr);
    }
    
    // Getter functions
    function getBalance(address _account) public view returns (uint256) {
        return balances[_account];
    }
    
    function getMessagesCount() public view returns (uint256) {
        return messages.length;
    }
    
    function isPaused() public view returns (bool) {
        return paused;
    }
}
