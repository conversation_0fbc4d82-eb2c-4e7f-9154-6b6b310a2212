// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Low-Level Calls and Advanced Patterns Test Contract
 * Tests call, delegatecall, staticcall, Create2, and proxy patterns
 */
contract LowLevelCallsContract {
    address public owner;
    uint256 public value;
    string public data;

    mapping(address => bool) public authorized;
    mapping(bytes32 => address) public deployedContracts;

    event CallExecuted(address indexed target, bool success, bytes returnData);
    event DelegateCallExecuted(address indexed target, bool success, bytes returnData);
    event StaticCallExecuted(address indexed target, bool success, bytes returnData);
    event ContractCreated(address indexed contractAddress, bytes32 indexed salt);
    event ProxyUpgraded(address indexed oldImplementation, address indexed newImplementation);

    constructor() {
        owner = msg.sender;
        authorized[msg.sender] = true;
    }

    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner");
        _;
    }

    modifier onlyAuthorized() {
        require(authorized[msg.sender], "Not authorized");
        _;
    }

    // Low-level call functions
    function executeCall(address target, bytes memory callData)
        external
        payable
        onlyAuthorized
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.call{value: msg.value}(callData);
        emit CallExecuted(target, success, returnData);
    }

    function executeCallWithGas(address target, bytes memory callData, uint256 gasLimit)
        external
        payable
        onlyAuthorized
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.call{value: msg.value, gas: gasLimit}(callData);
        emit CallExecuted(target, success, returnData);
    }

    function executeDelegateCall(address target, bytes memory callData)
        external
        onlyAuthorized
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.delegatecall(callData);
        emit DelegateCallExecuted(target, success, returnData);
    }

    function executeDelegateCallWithGas(address target, bytes memory callData, uint256 gasLimit)
        external
        onlyAuthorized
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.delegatecall{gas: gasLimit}(callData);
        emit DelegateCallExecuted(target, success, returnData);
    }

    function executeStaticCall(address target, bytes memory callData)
        external
        view
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.staticcall(callData);
    }

    function executeStaticCallWithGas(address target, bytes memory callData, uint256 gasLimit)
        external
        view
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.staticcall{gas: gasLimit}(callData);
    }

    // Batch call functions
    function batchCall(address[] memory targets, bytes[] memory callDatas)
        external
        payable
        onlyAuthorized
        returns (bool[] memory successes, bytes[] memory returnDatas)
    {
        require(targets.length == callDatas.length, "Array length mismatch");

        successes = new bool[](targets.length);
        returnDatas = new bytes[](targets.length);

        for (uint256 i = 0; i < targets.length; i++) {
            (successes[i], returnDatas[i]) = targets[i].call(callDatas[i]);
            emit CallExecuted(targets[i], successes[i], returnDatas[i]);
        }
    }

    function batchDelegateCall(address[] memory targets, bytes[] memory callDatas)
        external
        onlyAuthorized
        returns (bool[] memory successes, bytes[] memory returnDatas)
    {
        require(targets.length == callDatas.length, "Array length mismatch");

        successes = new bool[](targets.length);
        returnDatas = new bytes[](targets.length);

        for (uint256 i = 0; i < targets.length; i++) {
            (successes[i], returnDatas[i]) = targets[i].delegatecall(callDatas[i]);
            emit DelegateCallExecuted(targets[i], successes[i], returnDatas[i]);
        }
    }

    // Create2 functions
    function computeCreate2Address(bytes32 salt, bytes32 bytecodeHash) public view returns (address) {
        return address(uint160(uint256(keccak256(abi.encodePacked(bytes1(0xff), address(this), salt, bytecodeHash)))));
    }

    function deployWithCreate2(bytes memory bytecode, bytes32 salt)
        external
        onlyAuthorized
        returns (address deployedAddress)
    {
        assembly {
            deployedAddress := create2(0, add(bytecode, 0x20), mload(bytecode), salt)
        }

        require(deployedAddress != address(0), "Create2 deployment failed");
        deployedContracts[salt] = deployedAddress;
        emit ContractCreated(deployedAddress, salt);
    }

    function deployWithCreate2AndValue(bytes memory bytecode, bytes32 salt, uint256 deployValue)
        external
        payable
        onlyAuthorized
        returns (address deployedAddress)
    {
        require(msg.value >= deployValue, "Insufficient value");

        assembly {
            deployedAddress := create2(deployValue, add(bytecode, 0x20), mload(bytecode), salt)
        }

        require(deployedAddress != address(0), "Create2 deployment failed");
        deployedContracts[salt] = deployedAddress;
        emit ContractCreated(deployedAddress, salt);
    }

    // Proxy pattern functions
    address public implementation;

    function upgradeImplementation(address newImplementation) external onlyOwner {
        require(newImplementation != address(0), "Invalid implementation");
        require(newImplementation != implementation, "Same implementation");

        address oldImplementation = implementation;
        implementation = newImplementation;

        emit ProxyUpgraded(oldImplementation, newImplementation);
    }

    function delegateToImplementation(bytes memory callData) external returns (bytes memory) {
        require(implementation != address(0), "No implementation set");

        (bool success, bytes memory returnData) = implementation.delegatecall(callData);
        require(success, "Delegate call failed");

        return returnData;
    }

    // Fallback function for proxy pattern
    fallback() external payable {
        if (implementation != address(0)) {
            assembly {
                let ptr := mload(0x40)
                calldatacopy(ptr, 0, calldatasize())
                let result := delegatecall(gas(), sload(implementation.slot), ptr, calldatasize(), 0, 0)
                let size := returndatasize()
                returndatacopy(ptr, 0, size)

                switch result
                case 0 { revert(ptr, size) }
                default { return(ptr, size) }
            }
        }
    }

    // Assembly functions for low-level operations
    function assemblyCall(address target, bytes memory callData, uint256 callValue)
        external
        returns (bool success, bytes memory returnData)
    {
        assembly {
            success :=
                call(
                    gas(), // gas
                    target, // address
                    callValue, // value
                    add(callData, 0x20), // input data pointer
                    mload(callData), // input data size
                    0, // output data pointer
                    0 // output data size
                )

            let size := returndatasize()
            returnData := mload(0x40)
            mstore(returnData, size)
            returndatacopy(add(returnData, 0x20), 0, size)
            mstore(0x40, add(add(returnData, 0x20), size))
        }
    }

    function assemblyDelegateCall(address target, bytes memory callData)
        external
        returns (bool success, bytes memory returnData)
    {
        assembly {
            success :=
                delegatecall(
                    gas(), // gas
                    target, // address
                    add(callData, 0x20), // input data pointer
                    mload(callData), // input data size
                    0, // output data pointer
                    0 // output data size
                )

            let size := returndatasize()
            returnData := mload(0x40)
            mstore(returnData, size)
            returndatacopy(add(returnData, 0x20), 0, size)
            mstore(0x40, add(add(returnData, 0x20), size))
        }
    }

    function assemblyStaticCall(address target, bytes memory callData)
        external
        view
        returns (bool success, bytes memory returnData)
    {
        assembly {
            success :=
                staticcall(
                    gas(), // gas
                    target, // address
                    add(callData, 0x20), // input data pointer
                    mload(callData), // input data size
                    0, // output data pointer
                    0 // output data size
                )

            let size := returndatasize()
            returnData := mload(0x40)
            mstore(returnData, size)
            returndatacopy(add(returnData, 0x20), 0, size)
            mstore(0x40, add(add(returnData, 0x20), size))
        }
    }

    // Utility functions
    function authorize(address account) external onlyOwner {
        authorized[account] = true;
    }

    function deauthorize(address account) external onlyOwner {
        authorized[account] = false;
    }

    function getDeployedContract(bytes32 salt) external view returns (address) {
        return deployedContracts[salt];
    }

    function isContract(address account) public view returns (bool) {
        uint256 size;
        assembly {
            size := extcodesize(account)
        }
        return size > 0;
    }

    function getCodeHash(address account) public view returns (bytes32 codeHash) {
        assembly {
            codeHash := extcodehash(account)
        }
    }

    function getCodeSize(address account) public view returns (uint256 size) {
        assembly {
            size := extcodesize(account)
        }
    }

    // Emergency functions
    function emergencyWithdraw() external onlyOwner {
        payable(owner).transfer(address(this).balance);
    }

    function emergencyCall(address target, bytes memory callData)
        external
        onlyOwner
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.call(callData);
    }

    // Receive function
    receive() external payable {
        // Log the received ether
        emit CallExecuted(msg.sender, true, abi.encodePacked("Ether received: ", msg.value));
    }
}
