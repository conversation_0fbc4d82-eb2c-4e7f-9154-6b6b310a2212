// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Try-Catch Feature Test Contract
 * Tests all try-catch related language features
 */

// External contract for testing external calls
contract ExternalContract {
    uint256 public value;
    bool public shouldRevert;
    
    error CustomError(string message, uint256 code);
    
    constructor(uint256 _value) {
        value = _value;
        shouldRevert = false;
    }
    
    function setValue(uint256 _value) external {
        if (shouldRevert) {
            revert("External contract reverted");
        }
        value = _value;
    }
    
    function getValueWithRevert() external view returns (uint256) {
        if (shouldRevert) {
            revert CustomError("Custom error occurred", 404);
        }
        return value;
    }
    
    function setShouldRevert(bool _shouldRevert) external {
        shouldRevert = _shouldRevert;
    }
    
    function divideByZero() external pure returns (uint256) {
        return 10 / 0; // This will cause a panic
    }
}

contract TryCatchContract {
    ExternalContract public externalContract;
    uint256 public successCount;
    uint256 public errorCount;
    uint256 public panicCount;
    uint256 public lowLevelErrorCount;
    
    string public lastError;
    bytes public lastLowLevelData;
    
    event TrySuccess(string operation, uint256 result);
    event TryError(string operation, string reason);
    event TryPanic(string operation, uint256 errorCode);
    event TryLowLevel(string operation, bytes data);
    
    constructor() {
        externalContract = new ExternalContract(100);
    }
    
    // Basic try-catch with external function call
    function trySetValue(uint256 _value) public returns (bool success) {
        try externalContract.setValue(_value) {
            successCount++;
            emit TrySuccess("setValue", _value);
            return true;
        } catch Error(string memory reason) {
            errorCount++;
            lastError = reason;
            emit TryError("setValue", reason);
            return false;
        } catch (bytes memory lowLevelData) {
            lowLevelErrorCount++;
            lastLowLevelData = lowLevelData;
            emit TryLowLevel("setValue", lowLevelData);
            return false;
        }
    }
    
    // Try-catch with return value
    function tryGetValue() public returns (bool success, uint256 result) {
        try externalContract.getValueWithRevert() returns (uint256 value) {
            successCount++;
            emit TrySuccess("getValue", value);
            return (true, value);
        } catch Error(string memory reason) {
            errorCount++;
            lastError = reason;
            emit TryError("getValue", reason);
            return (false, 0);
        } catch (bytes memory lowLevelData) {
            lowLevelErrorCount++;
            lastLowLevelData = lowLevelData;
            emit TryLowLevel("getValue", lowLevelData);
            return (false, 0);
        }
    }
    
    // Try-catch with panic handling
    function tryDivideByZero() public returns (bool success) {
        try externalContract.divideByZero() returns (uint256 result) {
            successCount++;
            emit TrySuccess("divideByZero", result);
            return true;
        } catch Error(string memory reason) {
            errorCount++;
            lastError = reason;
            emit TryError("divideByZero", reason);
            return false;
        } catch Panic(uint errorCode) {
            panicCount++;
            emit TryPanic("divideByZero", errorCode);
            return false;
        } catch (bytes memory lowLevelData) {
            lowLevelErrorCount++;
            lastLowLevelData = lowLevelData;
            emit TryLowLevel("divideByZero", lowLevelData);
            return false;
        }
    }
    
    // Try-catch with contract creation
    function tryCreateContract(uint256 _value) public returns (bool success, address contractAddress) {
        try new ExternalContract(_value) returns (ExternalContract newContract) {
            successCount++;
            emit TrySuccess("createContract", _value);
            return (true, address(newContract));
        } catch Error(string memory reason) {
            errorCount++;
            lastError = reason;
            emit TryError("createContract", reason);
            return (false, address(0));
        } catch (bytes memory lowLevelData) {
            lowLevelErrorCount++;
            lastLowLevelData = lowLevelData;
            emit TryLowLevel("createContract", lowLevelData);
            return (false, address(0));
        }
    }
    
    // Try-catch with this.function() pattern
    function tryInternalCall(uint256 _value) public returns (bool success) {
        try this.internalFunction(_value) returns (uint256 result) {
            successCount++;
            emit TrySuccess("internalCall", result);
            return true;
        } catch Error(string memory reason) {
            errorCount++;
            lastError = reason;
            emit TryError("internalCall", reason);
            return false;
        } catch (bytes memory lowLevelData) {
            lowLevelErrorCount++;
            lastLowLevelData = lowLevelData;
            emit TryLowLevel("internalCall", lowLevelData);
            return false;
        }
    }
    
    function internalFunction(uint256 _value) external pure returns (uint256) {
        require(_value > 0, "Value must be positive");
        require(_value < 1000, "Value too large");
        return _value * 2;
    }
    
    // Nested try-catch
    function nestedTryCatch(uint256 _value) public returns (string memory result) {
        try this.trySetValue(_value) returns (bool success) {
            if (success) {
                try this.tryGetValue() returns (bool getSuccess, uint256 getValue) {
                    if (getSuccess) {
                        return "Both operations succeeded";
                    } else {
                        return "Set succeeded, get failed";
                    }
                } catch {
                    return "Set succeeded, get threw exception";
                }
            } else {
                return "Set operation failed";
            }
        } catch {
            return "Set operation threw exception";
        }
    }
    
    // Try-catch with multiple external calls
    function tryMultipleOperations() public returns (string memory result) {
        uint256 operations = 0;
        uint256 successes = 0;
        
        // Operation 1
        try externalContract.setValue(50) {
            operations++;
            successes++;
        } catch {
            operations++;
        }
        
        // Operation 2
        try externalContract.getValueWithRevert() {
            operations++;
            successes++;
        } catch {
            operations++;
        }
        
        // Operation 3
        try this.internalFunction(25) {
            operations++;
            successes++;
        } catch {
            operations++;
        }
        
        if (successes == operations) {
            return "All operations succeeded";
        } else if (successes == 0) {
            return "All operations failed";
        } else {
            return "Some operations succeeded";
        }
    }
    
    // Try-catch with different error types
    function tryDifferentErrors(uint256 _errorType) public returns (string memory) {
        if (_errorType == 1) {
            // Test require error
            try this.requireError() {
                return "No error";
            } catch Error(string memory reason) {
                return string(abi.encodePacked("Require error: ", reason));
            } catch {
                return "Other error";
            }
        } else if (_errorType == 2) {
            // Test assert error (panic)
            try this.assertError() {
                return "No error";
            } catch Panic(uint errorCode) {
                return string(abi.encodePacked("Panic error: ", uint2str(errorCode)));
            } catch {
                return "Other error";
            }
        } else if (_errorType == 3) {
            // Test custom error
            try this.customError() {
                return "No error";
            } catch (bytes memory lowLevelData) {
                return "Custom error caught";
            }
        } else {
            return "Invalid error type";
        }
    }
    
    function requireError() external pure {
        require(false, "This is a require error");
    }
    
    function assertError() external pure {
        assert(false); // This causes a Panic
    }
    
    error MyCustomError(string message);
    
    function customError() external pure {
        revert MyCustomError("This is a custom error");
    }
    
    // Try-catch with gas limit considerations
    function tryWithGasLimit(uint256 _gasLimit) public returns (bool success) {
        try externalContract.setValue{gas: _gasLimit}(123) {
            successCount++;
            return true;
        } catch Error(string memory reason) {
            errorCount++;
            lastError = reason;
            return false;
        } catch {
            lowLevelErrorCount++;
            return false;
        }
    }
    
    // Try-catch with value transfer
    function tryWithValue(uint256 _value) public payable returns (bool success) {
        try this.payableFunction{value: _value}() {
            successCount++;
            return true;
        } catch Error(string memory reason) {
            errorCount++;
            lastError = reason;
            return false;
        } catch {
            lowLevelErrorCount++;
            return false;
        }
    }
    
    function payableFunction() external payable {
        require(msg.value > 0, "No value sent");
    }
    
    // Utility functions
    function uint2str(uint256 _i) internal pure returns (string memory) {
        if (_i == 0) {
            return "0";
        }
        uint256 j = _i;
        uint256 len;
        while (j != 0) {
            len++;
            j /= 10;
        }
        bytes memory bstr = new bytes(len);
        uint256 k = len;
        while (_i != 0) {
            k = k - 1;
            uint8 temp = (48 + uint8(_i - _i / 10 * 10));
            bytes1 b1 = bytes1(temp);
            bstr[k] = b1;
            _i /= 10;
        }
        return string(bstr);
    }
    
    // Control functions for testing
    function setExternalShouldRevert(bool _shouldRevert) public {
        externalContract.setShouldRevert(_shouldRevert);
    }
    
    function getStats() public view returns (
        uint256 success,
        uint256 error,
        uint256 panic,
        uint256 lowLevel
    ) {
        return (successCount, errorCount, panicCount, lowLevelErrorCount);
    }
    
    function resetStats() public {
        successCount = 0;
        errorCount = 0;
        panicCount = 0;
        lowLevelErrorCount = 0;
        lastError = "";
        lastLowLevelData = "";
    }
}
