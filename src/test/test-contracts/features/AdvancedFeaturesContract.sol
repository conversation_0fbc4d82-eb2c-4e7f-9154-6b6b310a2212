// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Advanced Solidity Features Test Contract
 * Tests constants, immutable, function overloading, type casting, etc.
 */
contract AdvancedFeaturesContract {
    // Constants
    uint256 public constant MAX_SUPPLY = 1000000;
    string public constant NAME = "Advanced Contract";
    bytes32 public constant DOMAIN_SEPARATOR = keccak256("AdvancedContract");
    address public constant ZERO_ADDRESS = address(0);

    // Immutable variables (set once in constructor)
    uint256 public immutable DEPLOYMENT_TIME;
    address public immutable DEPLOYER;
    bytes32 public immutable CREATION_HASH;

    // State variables for testing
    uint256 public value;
    string public text;
    bytes public data;

    // Events
    event ValueSet(uint256 indexed oldValue, uint256 indexed newValue);
    event DataProcessed(bytes data, string result);
    event TypeCasted(string operation, uint256 input, uint256 output);

    constructor() {
        DEPLOYMENT_TIME = block.timestamp;
        DEPLOYER = msg.sender;
        CREATION_HASH = keccak256(abi.encodePacked(block.timestamp, msg.sender));
    }

    // Function overloading - same name, different parameters
    function setValue(uint256 _value) external {
        uint256 oldValue = value;
        value = _value;
        emit ValueSet(oldValue, _value);
    }

    function setValue(uint256 _value, string memory _text) external {
        uint256 oldValue = value;
        value = _value;
        text = _text;
        emit ValueSet(oldValue, _value);
    }

    function setValue(uint256 _value, string memory _text, bytes memory _data) external {
        uint256 oldValue = value;
        value = _value;
        text = _text;
        data = _data;
        emit ValueSet(oldValue, _value);
    }

    // Type casting and conversions
    function typeCasting(uint256 input)
        external
        returns (uint8 toUint8, uint16 toUint16, int256 toInt256, bytes32 toBytes32, address toAddress)
    {
        // Explicit type casting
        toUint8 = uint8(input);
        toUint16 = uint16(input);
        toInt256 = int256(input);
        toBytes32 = bytes32(input);
        toAddress = address(uint160(input)); // Address requires uint160

        emit TypeCasted("uint8", input, toUint8);
        emit TypeCasted("uint16", input, toUint16);
        emit TypeCasted("int256", input, uint256(toInt256));
    }

    // Bytes and string operations
    function bytesOperations(bytes memory input)
        external
        returns (uint256 length, bytes1 firstByte, bytes memory sliced, bytes32 hashed)
    {
        length = input.length;

        if (length > 0) {
            firstByte = input[0];

            // Slice bytes (first 4 bytes or less)
            uint256 sliceLength = length > 4 ? 4 : length;
            sliced = new bytes(sliceLength);
            for (uint256 i = 0; i < sliceLength; i++) {
                sliced[i] = input[i];
            }
        }

        hashed = keccak256(input);
        emit DataProcessed(input, "bytes_operations");
    }

    function stringOperations(string memory input)
        external
        returns (uint256 length, bytes memory asBytes, bytes32 hashed, string memory concatenated)
    {
        asBytes = bytes(input);
        length = asBytes.length;
        hashed = keccak256(asBytes);
        concatenated = string(abi.encodePacked(input, "_processed"));

        emit DataProcessed(asBytes, concatenated);
    }

    // Keccak256 and cryptographic functions
    function cryptographicOperations(string memory message, uint256 nonce, address sender)
        external
        pure
        returns (bytes32 messageHash, bytes32 combinedHash, bytes32 packedHash)
    {
        messageHash = keccak256(bytes(message));
        combinedHash = keccak256(abi.encode(message, nonce, sender));
        packedHash = keccak256(abi.encodePacked(message, nonce, sender));
    }

    // Block and transaction properties
    function blockProperties()
        external
        view
        returns (uint256 blockNumber, uint256 timestamp, uint256 gasLimit, address coinbase, bytes32 blockHash)
    {
        blockNumber = block.number;
        timestamp = block.timestamp;
        gasLimit = block.gaslimit;
        coinbase = block.coinbase;

        // Get hash of previous block
        if (blockNumber > 0) {
            blockHash = blockhash(blockNumber - 1);
        }
    }

    function transactionProperties()
        external
        payable
        returns (
            address origin,
            uint256 gasPrice,
            uint256 gasLeft,
            address sender,
            uint256 msgValue,
            bytes memory msgData
        )
    {
        origin = tx.origin;
        gasPrice = tx.gasprice;
        gasLeft = gasleft();
        sender = msg.sender;
        msgValue = msg.value;
        msgData = msg.data;
    }

    // ABI encoding and decoding
    function abiOperations(uint256 number, string memory textParam, address addr)
        external
        pure
        returns (bytes memory encoded, bytes memory packed, bytes memory encodedWithSelector)
    {
        encoded = abi.encode(number, textParam, addr);
        packed = abi.encodePacked(number, textParam, addr);

        // Encode with function selector
        bytes4 selector = bytes4(keccak256("setValue(uint256,string,address)"));
        encodedWithSelector = abi.encodeWithSelector(selector, number, textParam, addr);
    }

    function abiDecode(bytes memory dataParam)
        external
        pure
        returns (uint256 number, string memory textParam, address addr)
    {
        (number, textParam, addr) = abi.decode(dataParam, (uint256, string, address));
    }

    // Low-level call operations
    function lowLevelCall(address target, bytes memory callData)
        external
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.call(callData);
    }

    function lowLevelStaticCall(address target, bytes memory callData)
        external
        view
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.staticcall(callData);
    }

    function lowLevelDelegateCall(address target, bytes memory callData)
        external
        returns (bool success, bytes memory returnData)
    {
        (success, returnData) = target.delegatecall(callData);
    }

    // Create2 contract creation (simplified example)
    function computeCreate2Address(bytes32 salt, bytes32 bytecodeHash) external view returns (address) {
        return address(uint160(uint256(keccak256(abi.encodePacked(bytes1(0xff), address(this), salt, bytecodeHash)))));
    }

    // Gas optimization patterns
    function gasOptimizedLoop(uint256[] memory array) external pure returns (uint256 sum) {
        uint256 length = array.length;
        for (uint256 i = 0; i < length;) {
            sum += array[i];
            unchecked {
                ++i; // Gas optimization: unchecked increment
            }
        }
    }

    function gasOptimizedStorage() external {
        // Pack multiple values in single storage slot
        uint128 value1 = 100;
        uint128 value2 = 200;

        // This would be more gas efficient than separate storage slots
        uint256 packed = (uint256(value1) << 128) | uint256(value2);
        value = packed;
    }

    // Function with complex return types
    function complexReturns()
        external
        pure
        returns (uint256[3] memory fixedArray, uint256[] memory dynamicArray, string memory str, bool success)
    {
        fixedArray = [uint256(1), 2, 3];
        dynamicArray = new uint256[](2);
        dynamicArray[0] = 10;
        dynamicArray[1] = 20;
        str = "complex_return";
        success = true;
    }

    // Modifier with complex logic
    modifier onlyAfterDeployment(uint256 minTime) {
        require(block.timestamp >= DEPLOYMENT_TIME + minTime, "Function called too early");
        _;
    }

    function timeLockedFunction() external onlyAfterDeployment(1 hours) {
        value = block.timestamp;
    }

    // View functions for constants and immutables
    function getConstants()
        external
        pure
        returns (uint256 maxSupply, string memory name, bytes32 domainSeparator, address zeroAddr)
    {
        return (MAX_SUPPLY, NAME, DOMAIN_SEPARATOR, ZERO_ADDRESS);
    }

    function getImmutables() external view returns (uint256 deploymentTime, address deployer, bytes32 creationHash) {
        return (DEPLOYMENT_TIME, DEPLOYER, CREATION_HASH);
    }
}
