// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Constructor Feature Test Contract
 * Tests all constructor-related language features
 */

// Base contract with constructor
contract BaseConstructor {
    uint256 public baseValue;
    address public baseOwner;
    
    constructor(uint256 _baseValue) {
        baseValue = _baseValue;
        baseOwner = msg.sender;
    }
}

// Contract with complex constructor
contract ConstructorContract is BaseConstructor {
    // State variables initialized in constructor
    uint256 public immutable CREATION_TIME;
    address public immutable DEPLOYER;
    string public constant CONTRACT_NAME = "ConstructorContract";
    
    uint256 public initialValue;
    address public owner;
    bool public isInitialized;
    
    // Arrays initialized in constructor
    uint256[] public numbers;
    string[] public names;
    
    // Mapping initialized in constructor
    mapping(address => uint256) public balances;
    mapping(uint256 => string) public idToName;
    
    // Struct initialized in constructor
    struct Config {
        uint256 maxSupply;
        uint256 minDeposit;
        bool isActive;
    }
    Config public config;
    
    // Events emitted in constructor
    event ContractDeployed(address indexed deployer, uint256 timestamp);
    event InitialConfigSet(uint256 maxSupply, uint256 minDeposit);
    
    // Modifiers that can be used in constructor
    modifier onlyDuringConstruction() {
        require(!isInitialized, "Already initialized");
        _;
    }
    
    // Constructor with multiple parameters and complex logic
    constructor(
        uint256 _initialValue,
        uint256 _maxSupply,
        uint256 _minDeposit,
        string[] memory _initialNames
    ) BaseConstructor(_initialValue * 2) onlyDuringConstruction {
        // Set immutable variables
        CREATION_TIME = block.timestamp;
        DEPLOYER = msg.sender;
        
        // Initialize state variables
        initialValue = _initialValue;
        owner = msg.sender;
        
        // Initialize arrays
        numbers.push(1);
        numbers.push(2);
        numbers.push(3);
        
        for (uint256 i = 0; i < _initialNames.length; i++) {
            names.push(_initialNames[i]);
            idToName[i] = _initialNames[i];
        }
        
        // Initialize mappings
        balances[msg.sender] = 1000;
        balances[address(this)] = 500;
        
        // Initialize struct
        config = Config({
            maxSupply: _maxSupply,
            minDeposit: _minDeposit,
            isActive: true
        });
        
        // Complex constructor logic
        if (_initialValue > 100) {
            numbers.push(_initialValue);
        }
        
        // Emit events
        emit ContractDeployed(msg.sender, block.timestamp);
        emit InitialConfigSet(_maxSupply, _minDeposit);
        
        // Mark as initialized
        isInitialized = true;
    }
    
    // Functions to verify constructor initialization
    function getNumbersLength() public view returns (uint256) {
        return numbers.length;
    }
    
    function getNamesLength() public view returns (uint256) {
        return names.length;
    }
    
    function getBalance(address _account) public view returns (uint256) {
        return balances[_account];
    }
    
    function getConfig() public view returns (uint256, uint256, bool) {
        return (config.maxSupply, config.minDeposit, config.isActive);
    }
    
    function getCreationInfo() public view returns (uint256, address) {
        return (CREATION_TIME, DEPLOYER);
    }
}

// Contract with payable constructor
contract PayableConstructorContract {
    uint256 public initialDeposit;
    address public depositor;
    
    event InitialDepositReceived(address indexed depositor, uint256 amount);
    
    constructor() payable {
        initialDeposit = msg.value;
        depositor = msg.sender;
        
        emit InitialDepositReceived(msg.sender, msg.value);
    }
    
    function getInitialDeposit() public view returns (uint256) {
        return initialDeposit;
    }
}

// Contract with constructor that calls external functions
contract ConstructorWithExternalCalls {
    uint256 public externalValue;
    bool public externalCallMade;
    
    constructor(address _externalContract) {
        // This would be an external call in a real scenario
        // For testing purposes, we'll simulate it
        if (_externalContract != address(0)) {
            externalCallMade = true;
            externalValue = 42; // Simulated external call result
        }
    }
}

// Contract with constructor that reverts under certain conditions
contract ConditionalConstructorContract {
    uint256 public value;
    
    constructor(uint256 _value) {
        require(_value > 0, "Value must be greater than zero");
        require(_value < 1000000, "Value too large");
        
        value = _value;
    }
}
