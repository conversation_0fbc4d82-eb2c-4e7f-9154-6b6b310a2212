// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Nested Mappings Test Contract
 * Tests 2-3 levels of nested mappings and complex mapping patterns
 */
contract NestedMappingsContract {
    // 2-level nested mappings
    mapping(address => mapping(uint256 => uint256)) public userBalances;
    mapping(address => mapping(address => bool)) public approvals;
    mapping(uint256 => mapping(string => bytes32)) public dataStorage;
    mapping(bytes32 => mapping(bool => address[])) public complexMapping;
    
    // 3-level nested mappings
    mapping(address => mapping(uint256 => mapping(string => uint256))) public tripleNested;
    mapping(address => mapping(address => mapping(uint256 => bool))) public permissions;
    mapping(uint256 => mapping(bytes32 => mapping(address => string))) public deepStorage;
    mapping(string => mapping(uint256 => mapping(bool => bytes))) public veryDeepData;
    
    // Complex nested mappings with structs
    struct UserData {
        string name;
        uint256 score;
        bool active;
    }
    
    struct TokenInfo {
        string symbol;
        uint256 decimals;
        address owner;
    }
    
    mapping(address => mapping(uint256 => UserData)) public userProfiles;
    mapping(uint256 => mapping(address => TokenInfo)) public tokenRegistry;
    mapping(address => mapping(string => mapping(uint256 => UserData))) public usersByCategory;
    
    // Mappings with arrays
    mapping(address => mapping(uint256 => uint256[])) public userArrays;
    mapping(uint256 => mapping(string => address[])) public categoryMembers;
    mapping(address => mapping(bytes32 => mapping(uint256 => string[]))) public nestedArrays;
    
    // Events for testing
    event BalanceUpdated(address indexed user, uint256 indexed tokenId, uint256 amount);
    event ApprovalSet(address indexed owner, address indexed spender, bool approved);
    event DataStored(uint256 indexed id, string indexed key, bytes32 value);
    event TripleNestedSet(address indexed user, uint256 indexed id, string key, uint256 value);
    event PermissionGranted(address indexed grantor, address indexed grantee, uint256 indexed permission);
    
    constructor() {
        // Initialize some test data
        userBalances[msg.sender][1] = 1000;
        approvals[msg.sender][address(this)] = true;
        dataStorage[1]["test"] = keccak256("test_data");
        
        // Initialize 3-level nested data
        tripleNested[msg.sender][1]["balance"] = 500;
        permissions[msg.sender][address(this)][1] = true;
        deepStorage[1][keccak256("key")][msg.sender] = "initial_value";
        
        // Initialize struct mappings
        userProfiles[msg.sender][1] = UserData("Admin", 100, true);
        tokenRegistry[1][msg.sender] = TokenInfo("TEST", 18, msg.sender);
        usersByCategory[msg.sender]["admin"][1] = UserData("SuperAdmin", 1000, true);
    }
    
    // Functions to interact with 2-level mappings
    function setUserBalance(address user, uint256 tokenId, uint256 amount) external {
        userBalances[user][tokenId] = amount;
        emit BalanceUpdated(user, tokenId, amount);
    }
    
    function getUserBalance(address user, uint256 tokenId) external view returns (uint256) {
        return userBalances[user][tokenId];
    }
    
    function setApproval(address owner, address spender, bool approved) external {
        approvals[owner][spender] = approved;
        emit ApprovalSet(owner, spender, approved);
    }
    
    function isApproved(address owner, address spender) external view returns (bool) {
        return approvals[owner][spender];
    }
    
    function storeData(uint256 id, string memory key, bytes32 value) external {
        dataStorage[id][key] = value;
        emit DataStored(id, key, value);
    }
    
    function getData(uint256 id, string memory key) external view returns (bytes32) {
        return dataStorage[id][key];
    }
    
    // Functions to interact with 3-level mappings
    function setTripleNested(address user, uint256 id, string memory key, uint256 value) external {
        tripleNested[user][id][key] = value;
        emit TripleNestedSet(user, id, key, value);
    }
    
    function getTripleNested(address user, uint256 id, string memory key) external view returns (uint256) {
        return tripleNested[user][id][key];
    }
    
    function grantPermission(address grantor, address grantee, uint256 permission) external {
        permissions[grantor][grantee][permission] = true;
        emit PermissionGranted(grantor, grantee, permission);
    }
    
    function hasPermission(address grantor, address grantee, uint256 permission) external view returns (bool) {
        return permissions[grantor][grantee][permission];
    }
    
    function setDeepStorage(uint256 id, bytes32 key, address user, string memory value) external {
        deepStorage[id][key][user] = value;
    }
    
    function getDeepStorage(uint256 id, bytes32 key, address user) external view returns (string memory) {
        return deepStorage[id][key][user];
    }
    
    function setVeryDeepData(string memory category, uint256 id, bool flag, bytes memory data) external {
        veryDeepData[category][id][flag] = data;
    }
    
    function getVeryDeepData(string memory category, uint256 id, bool flag) external view returns (bytes memory) {
        return veryDeepData[category][id][flag];
    }
    
    // Functions to interact with struct mappings
    function setUserProfile(address user, uint256 profileId, string memory name, uint256 score, bool active) external {
        userProfiles[user][profileId] = UserData(name, score, active);
    }
    
    function getUserProfile(address user, uint256 profileId) external view returns (UserData memory) {
        return userProfiles[user][profileId];
    }
    
    function setTokenInfo(uint256 tokenId, address owner, string memory symbol, uint256 decimals) external {
        tokenRegistry[tokenId][owner] = TokenInfo(symbol, decimals, owner);
    }
    
    function getTokenInfo(uint256 tokenId, address owner) external view returns (TokenInfo memory) {
        return tokenRegistry[tokenId][owner];
    }
    
    function setUserByCategory(address user, string memory category, uint256 id, UserData memory userData) external {
        usersByCategory[user][category][id] = userData;
    }
    
    function getUserByCategory(address user, string memory category, uint256 id) external view returns (UserData memory) {
        return usersByCategory[user][category][id];
    }
    
    // Functions to interact with array mappings
    function addToUserArray(address user, uint256 arrayId, uint256 value) external {
        userArrays[user][arrayId].push(value);
    }
    
    function getUserArray(address user, uint256 arrayId) external view returns (uint256[] memory) {
        return userArrays[user][arrayId];
    }
    
    function addCategoryMember(uint256 categoryId, string memory categoryName, address member) external {
        categoryMembers[categoryId][categoryName].push(member);
    }
    
    function getCategoryMembers(uint256 categoryId, string memory categoryName) external view returns (address[] memory) {
        return categoryMembers[categoryId][categoryName];
    }
    
    function addToNestedArray(address user, bytes32 key, uint256 index, string memory value) external {
        nestedArrays[user][key][index].push(value);
    }
    
    function getNestedArray(address user, bytes32 key, uint256 index) external view returns (string[] memory) {
        return nestedArrays[user][key][index];
    }
    
    // Complex operations combining multiple nested mappings
    function transferWithApproval(
        address from,
        address to,
        uint256 tokenId,
        uint256 amount
    ) external {
        require(approvals[from][msg.sender], "Not approved");
        require(userBalances[from][tokenId] >= amount, "Insufficient balance");
        
        userBalances[from][tokenId] -= amount;
        userBalances[to][tokenId] += amount;
        
        emit BalanceUpdated(from, tokenId, userBalances[from][tokenId]);
        emit BalanceUpdated(to, tokenId, userBalances[to][tokenId]);
    }
    
    function batchUpdateTripleNested(
        address[] memory users,
        uint256[] memory ids,
        string[] memory keys,
        uint256[] memory values
    ) external {
        require(users.length == ids.length && ids.length == keys.length && keys.length == values.length, "Array length mismatch");
        
        for (uint256 i = 0; i < users.length; i++) {
            tripleNested[users[i]][ids[i]][keys[i]] = values[i];
            emit TripleNestedSet(users[i], ids[i], keys[i], values[i]);
        }
    }
    
    // View functions to get mapping statistics
    function getMappingStats() external view returns (
        uint256 userBalance,
        bool isApproved,
        bytes32 storedData,
        uint256 tripleValue,
        bool hasPermissionFlag
    ) {
        userBalance = userBalances[msg.sender][1];
        isApproved = approvals[msg.sender][address(this)];
        storedData = dataStorage[1]["test"];
        tripleValue = tripleNested[msg.sender][1]["balance"];
        hasPermissionFlag = permissions[msg.sender][address(this)][1];
    }
    
    // Function to demonstrate complex nested access patterns
    function complexNestedAccess(
        address user,
        uint256 tokenId,
        string memory category,
        bytes32 key
    ) external view returns (
        uint256 balance,
        UserData memory profile,
        string memory deepValue,
        uint256[] memory arrayData
    ) {
        balance = userBalances[user][tokenId];
        profile = usersByCategory[user][category][tokenId];
        deepValue = deepStorage[tokenId][key][user];
        arrayData = userArrays[user][tokenId];
    }
}
