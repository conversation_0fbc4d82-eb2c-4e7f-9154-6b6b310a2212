// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Using For Feature Test Contract
 * Tests using for directive with libraries
 */

// Library for uint256 operations
library MathLibrary {
    function add(uint256 a, uint256 b) internal pure returns (uint256) {
        return a + b;
    }
    
    function multiply(uint256 a, uint256 b) internal pure returns (uint256) {
        return a * b;
    }
    
    function power(uint256 base, uint256 exponent) internal pure returns (uint256) {
        if (exponent == 0) return 1;
        uint256 result = base;
        for (uint256 i = 1; i < exponent; i++) {
            result *= base;
        }
        return result;
    }
    
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
}

// Library for string operations
library StringLibrary {
    function length(string memory str) internal pure returns (uint256) {
        return bytes(str).length;
    }
    
    function concat(string memory a, string memory b) internal pure returns (string memory) {
        return string(abi.encodePacked(a, b));
    }
    
    function toUpper(string memory str) internal pure returns (string memory) {
        bytes memory bStr = bytes(str);
        for (uint256 i = 0; i < bStr.length; i++) {
            if (bStr[i] >= 0x61 && bStr[i] <= 0x7A) {
                bStr[i] = bytes1(uint8(bStr[i]) - 32);
            }
        }
        return string(bStr);
    }
    
    function contains(string memory str, string memory substr) internal pure returns (bool) {
        bytes memory strBytes = bytes(str);
        bytes memory substrBytes = bytes(substr);
        
        if (substrBytes.length > strBytes.length) return false;
        if (substrBytes.length == 0) return true;
        
        for (uint256 i = 0; i <= strBytes.length - substrBytes.length; i++) {
            bool found = true;
            for (uint256 j = 0; j < substrBytes.length; j++) {
                if (strBytes[i + j] != substrBytes[j]) {
                    found = false;
                    break;
                }
            }
            if (found) return true;
        }
        return false;
    }
}

// Library for array operations
library ArrayLibrary {
    function sum(uint256[] memory arr) internal pure returns (uint256) {
        uint256 total = 0;
        for (uint256 i = 0; i < arr.length; i++) {
            total += arr[i];
        }
        return total;
    }
    
    function average(uint256[] memory arr) internal pure returns (uint256) {
        require(arr.length > 0, "Array is empty");
        return sum(arr) / arr.length;
    }
    
    function max(uint256[] memory arr) internal pure returns (uint256) {
        require(arr.length > 0, "Array is empty");
        uint256 maxVal = arr[0];
        for (uint256 i = 1; i < arr.length; i++) {
            if (arr[i] > maxVal) {
                maxVal = arr[i];
            }
        }
        return maxVal;
    }
    
    function min(uint256[] memory arr) internal pure returns (uint256) {
        require(arr.length > 0, "Array is empty");
        uint256 minVal = arr[0];
        for (uint256 i = 1; i < arr.length; i++) {
            if (arr[i] < minVal) {
                minVal = arr[i];
            }
        }
        return minVal;
    }
    
    function contains(uint256[] memory arr, uint256 value) internal pure returns (bool) {
        for (uint256 i = 0; i < arr.length; i++) {
            if (arr[i] == value) {
                return true;
            }
        }
        return false;
    }
}

// Library for address operations
library AddressLibrary {
    function isContract(address addr) internal view returns (bool) {
        uint256 size;
        assembly {
            size := extcodesize(addr)
        }
        return size > 0;
    }
    
    function toString(address addr) internal pure returns (string memory) {
        bytes32 value = bytes32(uint256(uint160(addr)));
        bytes memory alphabet = "0123456789abcdef";
        bytes memory str = new bytes(42);
        str[0] = '0';
        str[1] = 'x';
        for (uint256 i = 0; i < 20; i++) {
            str[2 + i * 2] = alphabet[uint8(value[i + 12] >> 4)];
            str[3 + i * 2] = alphabet[uint8(value[i + 12] & 0x0f)];
        }
        return string(str);
    }
}

contract UsingForContract {
    // Using for directives
    using MathLibrary for uint256;
    using StringLibrary for string;
    using ArrayLibrary for uint256[];
    using AddressLibrary for address;
    
    // State variables
    uint256 public result;
    string public textResult;
    uint256[] public numbers;
    address public contractAddress;
    
    // Events
    event MathOperation(string operation, uint256 input1, uint256 input2, uint256 result);
    event StringOperation(string operation, string input, string result);
    event ArrayOperation(string operation, uint256[] input, uint256 result);
    event AddressOperation(string operation, address input, string result);
    
    constructor() {
        numbers = [1, 2, 3, 4, 5];
        contractAddress = address(this);
    }
    
    // Math library usage
    function testMathOperations(uint256 a, uint256 b) public {
        // Using library functions as methods on uint256
        result = a.add(b);
        emit MathOperation("add", a, b, result);
        
        result = a.multiply(b);
        emit MathOperation("multiply", a, b, result);
        
        result = a.power(b);
        emit MathOperation("power", a, b, result);
        
        result = a.sqrt();
        emit MathOperation("sqrt", a, 0, result);
    }
    
    function calculateComplex(uint256 x, uint256 y, uint256 z) public returns (uint256) {
        // Chaining library function calls
        result = x.add(y).multiply(z).sqrt();
        return result;
    }
    
    // String library usage
    function testStringOperations(string memory text) public {
        // Using library functions as methods on string
        uint256 len = text.length();
        emit StringOperation("length", text, uint2str(len));
        
        textResult = text.toUpper();
        emit StringOperation("toUpper", text, textResult);
        
        textResult = text.concat(" - processed");
        emit StringOperation("concat", text, textResult);
        
        bool containsHello = text.contains("hello");
        emit StringOperation("contains", text, containsHello ? "true" : "false");
    }
    
    function processText(string memory input, string memory suffix) public returns (string memory) {
        // Chaining string operations
        textResult = input.toUpper().concat(suffix);
        return textResult;
    }
    
    // Array library usage
    function testArrayOperations() public {
        // Using library functions as methods on uint256[]
        uint256 total = numbers.sum();
        emit ArrayOperation("sum", numbers, total);
        
        uint256 avg = numbers.average();
        emit ArrayOperation("average", numbers, avg);
        
        uint256 maxVal = numbers.max();
        emit ArrayOperation("max", numbers, maxVal);
        
        uint256 minVal = numbers.min();
        emit ArrayOperation("min", numbers, minVal);
        
        result = total;
    }
    
    function analyzeArray(uint256[] memory arr) public returns (uint256, uint256, uint256) {
        return (arr.sum(), arr.max(), arr.min());
    }
    
    function searchInArray(uint256 value) public view returns (bool) {
        return numbers.contains(value);
    }
    
    // Address library usage
    function testAddressOperations(address addr) public {
        // Using library functions as methods on address
        bool isContract = addr.isContract();
        emit AddressOperation("isContract", addr, isContract ? "true" : "false");
        
        textResult = addr.toString();
        emit AddressOperation("toString", addr, textResult);
    }
    
    function analyzeAddress(address addr) public view returns (bool, string memory) {
        return (addr.isContract(), addr.toString());
    }
    
    // Multiple using for with same type
    function demonstrateChaining(uint256 base, uint256 exp, uint256 multiplier) public returns (uint256) {
        // Complex chaining of library functions
        result = base.power(exp).multiply(multiplier).sqrt();
        return result;
    }
    
    // Using for with custom types
    struct Point {
        uint256 x;
        uint256 y;
    }
    
    // Note: In a real scenario, you'd define a library for Point operations
    // For demonstration, we'll use existing libraries on the struct fields
    function processPoint(Point memory point) public returns (uint256) {
        // Using library functions on struct fields
        result = point.x.add(point.y).sqrt();
        return result;
    }
    
    // Array manipulation with library functions
    function updateNumbers(uint256[] memory newNumbers) public {
        numbers = newNumbers;
        result = numbers.sum();
    }
    
    function addToNumbers(uint256 value) public {
        numbers.push(value);
        result = numbers.sum();
    }
    
    // Complex operations combining multiple libraries
    function complexOperation(
        string memory text,
        uint256[] memory values,
        address addr
    ) public returns (string memory, uint256, bool) {
        // Use all libraries in one function
        string memory processedText = text.toUpper().concat(" - analyzed");
        uint256 arraySum = values.sum();
        bool isContractAddr = addr.isContract();
        
        textResult = processedText;
        result = arraySum;
        
        return (processedText, arraySum, isContractAddr);
    }
    
    // Utility function
    function uint2str(uint256 _i) internal pure returns (string memory) {
        if (_i == 0) {
            return "0";
        }
        uint256 j = _i;
        uint256 len;
        while (j != 0) {
            len++;
            j /= 10;
        }
        bytes memory bstr = new bytes(len);
        uint256 k = len;
        while (_i != 0) {
            k = k - 1;
            uint8 temp = (48 + uint8(_i - _i / 10 * 10));
            bytes1 b1 = bytes1(temp);
            bstr[k] = b1;
            _i /= 10;
        }
        return string(bstr);
    }
    
    // Getter functions
    function getNumbers() public view returns (uint256[] memory) {
        return numbers;
    }
    
    function getResult() public view returns (uint256) {
        return result;
    }
    
    function getTextResult() public view returns (string memory) {
        return textResult;
    }
}
