// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Modifier Feature Test Contract
 * Tests all modifier-related language features
 */
contract ModifierContract {
    address public owner;
    bool public paused;
    uint256 public minValue;
    mapping(address => bool) public authorized;
    mapping(address => uint256) public balances;
    uint256 public totalSupply;

    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);
    event Paused(address indexed by);
    event Unpaused(address indexed by);
    event AuthorizationChanged(address indexed user, bool authorized);

    // Basic modifier
    modifier onlyOwner() {
        require(msg.sender == owner, "Not the owner");
        _;
    }

    // Modifier with parameters
    modifier onlyAuthorized(address _user) {
        require(authorized[_user], "Not authorized");
        _;
    }

    // Modifier with multiple parameters
    modifier validRange(uint256 _value, uint256 _min, uint256 _max) {
        require(_value >= _min && _value <= _max, "Value out of range");
        _;
    }

    // Modifier with conditional logic
    modifier whenNotPaused() {
        require(!paused, "Contract is paused");
        _;
    }

    modifier whenPaused() {
        require(paused, "Contract is not paused");
        _;
    }

    // Modifier with state changes
    modifier costs(uint256 _amount) {
        require(msg.value >= _amount, "Insufficient payment");
        _;
        if (msg.value > _amount) {
            payable(msg.sender).transfer(msg.value - _amount);
        }
    }

    // Modifier with complex logic
    modifier validTransfer(address _to, uint256 _amount) {
        require(_to != address(0), "Cannot transfer to zero address");
        require(_to != address(this), "Cannot transfer to contract");
        require(balances[msg.sender] >= _amount, "Insufficient balance");
        require(_amount > 0, "Amount must be positive");
        _;
    }

    // Modifier that modifies state before and after
    modifier trackingGas() {
        uint256 gasStart = gasleft();
        _;
        uint256 gasUsed = gasStart - gasleft();
        emit GasUsed(msg.sender, gasUsed);
    }

    event GasUsed(address indexed user, uint256 gasAmount);

    // Modifier with return value modification
    modifier doubleResult() {
        _;
        // Note: This is conceptual - modifiers can't directly modify return values
        // But they can affect state that influences return values
    }

    // Virtual modifier (for inheritance)
    modifier onlyAuthorizedUser() virtual {
        require(authorized[msg.sender], "Not authorized user");
        _;
    }

    // Modifier with nested conditions
    modifier complexValidation(uint256 _value) {
        if (_value > 1000) {
            require(authorized[msg.sender], "Large values require authorization");
        } else if (_value > 100) {
            require(balances[msg.sender] >= _value * 2, "Insufficient balance for medium values");
        }
        require(_value >= minValue, "Value below minimum");
        _;
    }

    constructor() {
        owner = msg.sender;
        paused = false;
        minValue = 1;
        authorized[msg.sender] = true;
        balances[msg.sender] = 1000;
        totalSupply = 1000;
    }

    // Functions using single modifiers
    function transferOwnership(address _newOwner) public onlyOwner {
        require(_newOwner != address(0), "New owner cannot be zero address");
        address previousOwner = owner;
        owner = _newOwner;
        emit OwnershipTransferred(previousOwner, _newOwner);
    }

    function pause() public onlyOwner whenNotPaused {
        paused = true;
        emit Paused(msg.sender);
    }

    function unpause() public onlyOwner whenPaused {
        paused = false;
        emit Unpaused(msg.sender);
    }

    // Functions using modifiers with parameters
    function authorizeUser(address _user) public onlyOwner {
        authorized[_user] = true;
        emit AuthorizationChanged(_user, true);
    }

    function revokeAuthorization(address _user) public onlyOwner {
        authorized[_user] = false;
        emit AuthorizationChanged(_user, false);
    }

    function setValueInRange(uint256 _value) public validRange(_value, 1, 1000) {
        minValue = _value;
    }

    // Functions using multiple modifiers
    function restrictedTransfer(address _to, uint256 _amount)
        public
        whenNotPaused
        validTransfer(_to, _amount)
        onlyAuthorizedUser
    {
        balances[msg.sender] -= _amount;
        balances[_to] += _amount;
    }

    function complexOperation(uint256 _value)
        public
        payable
        whenNotPaused
        complexValidation(_value)
        costs(0.01 ether)
        trackingGas
    {
        balances[msg.sender] += _value;
        totalSupply += _value;
    }

    // Function with modifier order dependency
    function orderDependentFunction(address _user, uint256 _amount)
        public
        onlyOwner
        onlyAuthorized(_user)
        whenNotPaused
        validRange(_amount, 1, 100)
    {
        balances[_user] += _amount;
    }

    // Function demonstrating modifier with state changes
    function payableFunction() public payable costs(0.1 ether) {
        balances[msg.sender] += 10;
    }

    // Function with conditional modifier application
    function conditionalModifierFunction(uint256 _value, bool _useAuth) public {
        if (_useAuth) {
            this.authorizedOnlyFunction(_value);
        } else {
            this.publicFunction(_value);
        }
    }

    function authorizedOnlyFunction(uint256 _value) public onlyAuthorizedUser {
        balances[msg.sender] += _value;
    }

    function publicFunction(uint256 _value) public {
        balances[msg.sender] += _value / 2; // Reduced benefit for public access
    }

    // Function with nested modifier calls
    function nestedModifierFunction(address _user, uint256 _value) public onlyOwner {
        this.userSpecificFunction(_user, _value);
    }

    function userSpecificFunction(address _user, uint256 _value)
        public
        onlyAuthorized(_user)
        validRange(_value, 1, 50)
    {
        balances[_user] += _value;
    }

    // Modifier with early return pattern
    modifier earlyReturn(uint256 _value) {
        if (_value == 0) {
            return;
        }
        require(_value > minValue, "Value too small");
        _;
    }

    function earlyReturnFunction(uint256 _value) public earlyReturn(_value) {
        balances[msg.sender] += _value;
    }

    // Modifier with loop
    modifier validateArray(uint256[] memory _values) {
        for (uint256 i = 0; i < _values.length; i++) {
            require(_values[i] > 0, "All values must be positive");
        }
        _;
    }

    function processArray(uint256[] memory _values) public validateArray(_values) {
        for (uint256 i = 0; i < _values.length; i++) {
            balances[msg.sender] += _values[i];
        }
    }

    // Modifier with external call
    modifier externalValidation() {
        // In a real scenario, this might call another contract
        require(this.isValidCaller(msg.sender), "External validation failed");
        _;
    }

    function isValidCaller(address _caller) public view returns (bool) {
        return authorized[_caller] || _caller == owner;
    }

    function externallyValidatedFunction() public externalValidation {
        balances[msg.sender] += 5;
    }

    // Getter functions
    function getBalance(address _user) public view returns (uint256) {
        return balances[_user];
    }

    function isAuthorized(address _user) public view returns (bool) {
        return authorized[_user];
    }

    function getContractState()
        public
        view
        returns (address currentOwner, bool isPaused, uint256 currentMinValue, uint256 currentTotalSupply)
    {
        return (owner, paused, minValue, totalSupply);
    }
}

// Contract demonstrating modifier inheritance
contract ModifierInheritance is ModifierContract {
    // Override virtual modifier
    modifier onlyAuthorizedUser() override {
        require(authorized[msg.sender] || msg.sender == owner, "Not authorized user or owner");
        _;
    }

    // New modifier in derived contract
    modifier additionalCheck() {
        require(block.timestamp > 0, "Invalid timestamp");
        _;
    }

    // Function using both inherited and new modifiers
    function derivedFunction(uint256 _value) public onlyAuthorizedUser additionalCheck validRange(_value, 1, 200) {
        balances[msg.sender] += _value * 2;
    }
}
