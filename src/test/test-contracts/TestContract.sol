// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "./Meh.sol";

contract TestContract {
    uint public value;
    Meh public mehContract;
    
    constructor() {
        mehContract = new Meh();
    }
    
    function setValue(uint _value) public {
        value = _value;
    }
    
    function getValue() public view returns (uint) {
        return value;
    }
    
    function getMehValue() public view returns (uint) {
        return mehContract.valueXYZ();
    }
}
