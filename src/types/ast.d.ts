/**
 * Slither AST and CFG result types
 */

export interface SlitherAstResult {
  success: boolean;
  ast?: SlitherAst;
  cfg?: SlitherCfg;
  error?: string;
  filePath: string;
}

export interface SlitherAst {
  compilation_units: CompilationUnit[];
  detectors: any[];
  printers: any[];
  slither_version: string;
}

export interface CompilationUnit {
  contracts: Contract[];
  crytic_compile_compilation_unit: any;
  source_units: SourceUnit[];
}

export interface Contract {
  name: string;
  id: number;
  inheritance: string[];
  libraries: string[];
  dependencies: string[];
  source_mapping: SourceMapping;
  functions: Function[];
  modifiers: Modifier[];
  variables: Variable[];
  structures: Structure[];
  events: Event[];
  enums: Enum[];
  using_for: UsingFor[];
}

export interface Function {
  name: string;
  id: number;
  signature: string;
  visibility: string;
  modifiers: string[];
  state_mutability: string;
  parameters: Parameter[];
  returns: Parameter[];
  source_mapping: SourceMapping;
  cfg?: ControlFlowGraph;
}

export interface Modifier {
  name: string;
  id: number;
  parameters: Parameter[];
  source_mapping: SourceMapping;
}

export interface Variable {
  name: string;
  id: number;
  type: string;
  visibility: string;
  source_mapping: SourceMapping;
}

export interface Structure {
  name: string;
  id: number;
  elements: StructureElement[];
  source_mapping: SourceMapping;
}

export interface Event {
  name: string;
  id: number;
  parameters: Parameter[];
  source_mapping: SourceMapping;
}

export interface Enum {
  name: string;
  id: number;
  values: string[];
  source_mapping: SourceMapping;
}

export interface UsingFor {
  library: string;
  type: string;
}

export interface Parameter {
  name: string;
  type: string;
}

export interface StructureElement {
  name: string;
  type: string;
}

export interface SourceUnit {
  filename: string;
  id: number;
  source_mapping: SourceMapping;
}

export interface SourceMapping {
  start: number;
  length: number;
  filename_relative: string;
  filename_absolute: string;
  filename_short: string;
  lines: number[];
  starting_column: number;
  ending_column: number;
}

export interface SlitherCfg {
  functions: FunctionCfg[];
}

export interface FunctionCfg {
  name: string;
  contract: string;
  nodes: CfgNode[];
  edges: CfgEdge[];
}

export interface ControlFlowGraph {
  nodes: CfgNode[];
  edges: CfgEdge[];
}

export interface CfgNode {
  id: number;
  type: string;
  expression?: string;
  source_mapping?: SourceMapping;
}

export interface CfgEdge {
  from: number;
  to: number;
  type?: string;
}
