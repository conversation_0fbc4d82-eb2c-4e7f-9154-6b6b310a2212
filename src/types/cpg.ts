/**
 * Code Property Graph (CPG) types for Solidity contract analysis
 * Supports deep state management, taint analysis, and control flow
 */

export interface SourceLocation {
  start: number;
  length: number;
  lines: number[];
  startColumn: number;
  endColumn: number;
  filename: string;
}

export interface CpgNode {
  id: string;
  type: CpgNodeType;
  name: string;
  sourceLocation?: SourceLocation;
  properties: Record<string, unknown>;
}

export interface ContractNode extends CpgNode {
  type: 'CONTRACT';
  properties: {
    inheritance: string[];
    libraries: string[];
    dependencies: string[];
  };
}

export interface FunctionNode extends CpgNode {
  type: 'FUNCTION';
  properties: {
    signature: string;
    visibility: string;
    stateMutability: string;
    modifiers: string[];
    parameters: ParameterInfo[];
    returns: ParameterInfo[];
  };
}

export interface VariableNode extends CpgNode {
  type: 'VARIABLE';
  properties: {
    variableType: string;
    visibility?: string;
    scope: VariableScope;
    isStateVariable: boolean;
    isTainted?: boolean;
    taintSource?: string;
  };
}

export interface ExpressionNode extends CpgNode {
  type: 'EXPRESSION';
  properties: {
    expressionType: string;
    operator?: string;
    value?: string;
    isTainted?: boolean;
    taintSources?: string[];
  };
}

export interface AssignmentNode extends CpgNode {
  type: 'ASSIGNMENT';
  properties: {
    operator: string;
    isSink?: boolean;
    sinkType?: SinkType;
    taintFlow?: TaintFlow;
  };
}

export type CpgNodeType = 'CONTRACT' | 'FUNCTION' | 'VARIABLE' | 'EXPRESSION' | 'ASSIGNMENT';

export type VariableScope = 'STATE' | 'LOCAL' | 'PARAMETER' | 'RETURN';

export type SinkType = 'STATE_WRITE' | 'EXTERNAL_CALL' | 'STORAGE_WRITE' | 'EMIT_EVENT';

export interface ParameterInfo {
  name: string;
  type: string;
}

export interface TaintFlow {
  source: string;
  sink: string;
  path: string[];
}

export interface CpgEdge {
  id: string;
  type: CpgEdgeType;
  source: string;
  target: string;
  properties: Record<string, unknown>;
}

export interface ContainsEdge extends CpgEdge {
  type: 'CONTAINS';
  properties: {
    order?: number;
  };
}

export interface CallsEdge extends CpgEdge {
  type: 'CALLS';
  properties: {
    callType: string;
  };
}

export interface ReadsEdge extends CpgEdge {
  type: 'READS';
  properties: {
    accessType: string;
  };
}

export interface WritesEdge extends CpgEdge {
  type: 'WRITES';
  properties: {
    writeType: string;
    isSink?: boolean;
  };
}

export interface TaintEdge extends CpgEdge {
  type: 'TAINT';
  properties: {
    taintType: string;
    confidence: number;
  };
}

export interface ControlFlowEdge extends CpgEdge {
  type: 'CONTROL_FLOW';
  properties: {
    flowType: string;
    condition?: string;
  };
}

export type CpgEdgeType = 'CONTAINS' | 'CALLS' | 'READS' | 'WRITES' | 'TAINT' | 'CONTROL_FLOW';

export interface CpgGraph {
  nodes: Map<string, CpgNode>;
  edges: Map<string, CpgEdge>;
  metadata: CpgMetadata;
}

export interface CpgMetadata {
  sourceFile: string;
  timestamp: number;
  slitherVersion?: string;
  nodeCount: number;
  edgeCount: number;
  contractCount: number;
  functionCount: number;
  variableCount: number;
  taintFlows: TaintFlow[];
  sinks: SinkInfo[];
}

export interface SinkInfo {
  nodeId: string;
  sinkType: SinkType;
  location: SourceLocation;
  taintSources: string[];
}

// Utility types for analysis
export interface AnalysisContext {
  currentContract?: string;
  currentFunction?: string;
  variableScopes: Map<string, VariableScope>;
  taintedVariables: Set<string>;
  stateVariables: Set<string>;
}
