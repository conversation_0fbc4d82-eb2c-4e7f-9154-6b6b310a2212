#!/usr/bin/env node

/**
 * CLI script for flattening Solidity contracts using Foundry
 * Usage: npm run flatten [folder]
 * Example: npm run flatten src/test/test-contracts
 */

import { FlatteningService } from '../services/flattening.service';

async function main(): Promise<void> {
  const folder = process.argv[2] || 'contracts';
  const outputDir = process.argv[3] || 'contracts/output';
  
  console.log('🚀 Solidity Contract Flattening Tool');
  console.log('====================================');
  console.log(`📁 Input folder: ${folder}`);
  console.log(`📂 Output directory: ${outputDir}`);
  console.log('');

  try {
    const result = await FlatteningService.flattenFolder(folder, outputDir);
    
    if (result.success) {
      console.log('');
      console.log('🎉 Flattening completed successfully!');
      console.log(`📄 Flattened contract saved to: ${result.outputPath}`);
    } else {
      console.error('');
      console.error('❌ Flattening failed:');
      console.error(result.error);
      process.exit(1);
    }
  } catch (error) {
    console.error('');
    console.error('💥 Unexpected error:');
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}
