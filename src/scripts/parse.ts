#!/usr/bin/env node

/**
 * CLI script for parsing Solidity contracts using Slither
 * Usage: npm run parse [file]
 * Example: npm run parse contracts/output/flattened-123456.sol
 */

import { SlitherService } from '../services/slither.service';

async function main(): Promise<void> {
  const filePath = process.argv[2];

  if (!filePath) {
    console.error('❌ Please provide a file path');
    console.error('Usage: npm run parse <file-path>');
    console.error(
      'Example: npm run parse contracts/output/flattened-123456.sol'
    );
    process.exit(1);
  }

  console.log('🚀 Solidity Contract Parser (Slither)');
  console.log('=====================================');
  console.log(`📄 Parsing file: ${filePath}`);
  console.log('');

  try {
    // Check if Slither is available
    const isAvailable = await SlitherService.isSlitherAvailable();
    if (!isAvailable) {
      console.error('❌ Slither is not available');
      console.error('Please install Slither: pip install slither-analyzer');
      process.exit(1);
    }

    const result = await SlitherService.parseContract(filePath);

    if (result.success) {
      console.log('');
      console.log('🎉 Parsing completed successfully!');
      console.log('');
      console.log('📊 Analysis Results:');
      console.log('===================');

      if (result.ast) {
        console.log(`✅ AST generated successfully`);
        console.log(
          `📦 Compilation units: ${result.ast.compilation_units?.length || 0}`
        );

        if (
          result.ast.compilation_units &&
          result.ast.compilation_units.length > 0
        ) {
          const contracts = result.ast.compilation_units[0]?.contracts || [];
          console.log(`🏗️  Contracts found: ${contracts.length}`);

          contracts.forEach((contract, index) => {
            console.log(`   ${index + 1}. ${contract.name}`);
            console.log(
              `      - Functions: ${contract.functions?.length || 0}`
            );
            console.log(
              `      - Variables: ${contract.variables?.length || 0}`
            );
            console.log(`      - Events: ${contract.events?.length || 0}`);
          });
        }
      }

      if (result.cfg) {
        console.log(`✅ CFG generated successfully`);
      }

      console.log('');
      console.log('💾 Full AST data available in result object');

      // Optionally save results to file
      const outputFile = `${filePath}.analysis.json`;
      const fs = await import('fs');
      fs.writeFileSync(outputFile, JSON.stringify(result, null, 2));
      console.log(`📁 Analysis saved to: ${outputFile}`);
    } else {
      console.error('');
      console.error('❌ Parsing failed:');
      console.error(result.error);
      process.exit(1);
    }
  } catch (error) {
    console.error('');
    console.error('💥 Unexpected error:');
    console.error(error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}
