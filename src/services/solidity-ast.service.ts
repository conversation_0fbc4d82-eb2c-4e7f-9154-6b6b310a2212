/**
 * Solidity AST Service - Complete AST parsing using solc compiler
 * Provides comprehensive AST data for modern Solidity features
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface SolidityAstResult {
  success: boolean;
  ast?: SolidityAstNode;
  error?: string;
  filePath: string;
  compilerVersion?: string;
}

export interface SolidityAstNode {
  id: number;
  nodeType: string;
  src: string; // "start:length:sourceUnit"
  nodes?: SolidityAstNode[];
  name?: string;
  canonicalName?: string;
  typeDescriptions?: {
    typeIdentifier: string;
    typeString: string;
  };
  // Contract-specific
  contractKind?: 'contract' | 'interface' | 'library';
  baseContracts?: SolidityAstNode[];
  // Function-specific
  functionSelector?: string;
  visibility?: 'public' | 'private' | 'internal' | 'external';
  stateMutability?: 'pure' | 'view' | 'nonpayable' | 'payable';
  modifiers?: SolidityAstNode[];
  parameters?: SolidityAstNode;
  returnParameters?: SolidityAstNode;
  body?: SolidityAstNode;
  // Variable-specific
  stateVariable?: boolean;
  storageLocation?: 'default' | 'storage' | 'memory' | 'calldata';
  // Expression-specific
  operator?: string;
  left?: SolidityAstNode;
  right?: SolidityAstNode;
  expression?: SolidityAstNode;
  // Additional properties
  [key: string]: any;
}

export interface HybridAstResult {
  solidityAst: SolidityAstResult;
  slitherDetectors?: any;
  combined: boolean;
}

export class SolidityAstService {
  /**
   * Check if solc compiler is available
   */
  static async isSolcAvailable(): Promise<boolean> {
    try {
      const { stdout } = await execAsync('solc --version', { timeout: 5000 });
      return stdout.includes('solc');
    } catch (error) {
      console.warn(
        '⚠️  solc not available:',
        error instanceof Error ? error.message : String(error)
      );
      return false;
    }
  }

  /**
   * Parse Solidity contract using solc compiler
   */
  static async parseContract(
    filePath: string,
    timeout = 30000
  ): Promise<SolidityAstResult> {
    try {
      console.log(`🔍 Parsing contract with solc: ${filePath}`);

      // Check if file exists
      const fs = await import('fs');
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          filePath,
        };
      }

      // Get compiler version
      const { stdout: versionOutput } = await execAsync('solc --version', {
        timeout: 5000,
      });
      const versionMatch = versionOutput.match(/Version: ([^\s]+)/);
      const compilerVersion = versionMatch ? versionMatch[1] : 'unknown';

      // Parse with solc
      const command = `solc --ast-compact-json "${filePath}"`;
      console.log(`📊 Running: ${command}`);

      const { stdout, stderr } = await execAsync(command, {
        timeout,
        maxBuffer: 1024 * 1024 * 10, // 10MB buffer
      });

      if (stderr && stderr.includes('Error')) {
        console.warn('⚠️  Solc compilation warnings/errors:', stderr);
      }

      // Parse JSON output
      let ast: SolidityAstNode | undefined;

      // solc outputs JSON after "======= <filename> =======" for each file
      // The filename in the output might be relative, so try both absolute and relative paths
      const fileName = filePath.split('/').pop() || filePath;
      const relativePath = filePath.includes('src/')
        ? filePath.substring(filePath.indexOf('src/'))
        : filePath;

      // Try multiple patterns to find our file
      const patterns = [
        new RegExp(
          `======= ${filePath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')} =======\\s*\\n([\\s\\S]*?)(?=======|$)`
        ),
        new RegExp(
          `======= ${relativePath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')} =======\\s*\\n([\\s\\S]*?)(?=======|$)`
        ),
        new RegExp(
          `======= .*${fileName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')} =======\\s*\\n([\\s\\S]*?)(?=======|$)`
        ),
      ];

      let jsonMatch: RegExpMatchArray | null = null;
      for (const pattern of patterns) {
        jsonMatch = stdout.match(pattern);
        if (jsonMatch) break;
      }

      if (jsonMatch && jsonMatch[1]) {
        try {
          const jsonContent = jsonMatch[1].trim();
          // Remove any trailing content after the JSON
          const jsonEndIndex = jsonContent.lastIndexOf('}') + 1;
          const cleanJson = jsonContent.substring(0, jsonEndIndex);

          ast = JSON.parse(cleanJson);
        } catch (parseError) {
          console.error('❌ Failed to parse solc JSON output:', parseError);
          console.error(
            'JSON content:',
            jsonMatch[1].substring(0, 200) + '...'
          );
          return {
            success: false,
            error: `Failed to parse solc JSON: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
            filePath,
            ...(compilerVersion && { compilerVersion }),
          };
        }
      } else {
        console.error(
          '❌ No JSON output found in solc response for file:',
          filePath
        );
        console.error('Available output:', stdout.substring(0, 500) + '...');
        return {
          success: false,
          error: 'No JSON AST output found from solc',
          filePath,
          ...(compilerVersion && { compilerVersion }),
        };
      }

      if (!ast) {
        return {
          success: false,
          error: 'AST is undefined',
          filePath,
          ...(compilerVersion && { compilerVersion }),
        };
      }

      console.log(`✅ Successfully parsed contract with solc: ${filePath}`);

      return {
        success: true,
        ast,
        filePath,
        ...(compilerVersion && { compilerVersion }),
      };
    } catch (error) {
      console.error('❌ Solc parsing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        filePath,
      };
    }
  }

  /**
   * Parse contract with both Solidity AST and Slither annotations
   */
  static async parseWithSlitherAnnotations(
    filePath: string
  ): Promise<HybridAstResult> {
    try {
      console.log(`🔍 Parsing contract with hybrid approach: ${filePath}`);

      // Get Solidity AST
      const solidityAst = await this.parseContract(filePath);

      // Get Slither detectors (if available)
      let slitherDetectors: any = undefined;
      try {
        const { SlitherService } = await import('./slither.service');
        const slitherResult = await SlitherService.parseContract(filePath);
        if (slitherResult.success) {
          slitherDetectors = slitherResult.ast;
        }
      } catch (slitherError) {
        console.warn(
          '⚠️  Slither not available for hybrid parsing:',
          slitherError
        );
      }

      return {
        solidityAst,
        slitherDetectors,
        combined: solidityAst.success && !!slitherDetectors,
      };
    } catch (error) {
      console.error('❌ Hybrid parsing failed:', error);
      return {
        solidityAst: {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          filePath,
        },
        combined: false,
      };
    }
  }

  /**
   * Extract source location from Solidity AST src field
   */
  static parseSourceLocation(src: string):
    | {
        start: number;
        length: number;
        sourceUnit: number;
      }
    | undefined {
    try {
      const parts = src.split(':');
      if (parts.length >= 2 && parts[0] && parts[1]) {
        return {
          start: parseInt(parts[0], 10),
          length: parseInt(parts[1], 10),
          sourceUnit: parts.length > 2 && parts[2] ? parseInt(parts[2], 10) : 0,
        };
      }
    } catch (error) {
      console.warn(`⚠️  Failed to parse source location: ${src}`, error);
    }
    return undefined;
  }

  /**
   * Find nodes by type in AST
   */
  static findNodesByType(
    ast: SolidityAstNode,
    nodeType: string
  ): SolidityAstNode[] {
    const results: SolidityAstNode[] = [];

    function traverse(node: SolidityAstNode) {
      if (node.nodeType === nodeType) {
        results.push(node);
      }

      if (node.nodes) {
        node.nodes.forEach(traverse);
      }

      // Check other node properties that might contain child nodes
      Object.values(node).forEach((value) => {
        if (Array.isArray(value)) {
          value.forEach((item) => {
            if (item && typeof item === 'object' && item.nodeType) {
              traverse(item);
            }
          });
        } else if (value && typeof value === 'object' && value.nodeType) {
          traverse(value);
        }
      });
    }

    traverse(ast);
    return results;
  }

  /**
   * Get all contract definitions from AST
   */
  static getContracts(ast: SolidityAstNode): SolidityAstNode[] {
    return this.findNodesByType(ast, 'ContractDefinition');
  }

  /**
   * Get all function definitions from AST
   */
  static getFunctions(ast: SolidityAstNode): SolidityAstNode[] {
    return this.findNodesByType(ast, 'FunctionDefinition');
  }

  /**
   * Get all variable declarations from AST
   */
  static getVariables(ast: SolidityAstNode): SolidityAstNode[] {
    return this.findNodesByType(ast, 'VariableDeclaration');
  }
}
