import { exec } from 'child_process';
import { promisify } from 'util';
import { existsSync, mkdirSync, unlinkSync } from 'fs';
import path from 'path';
import { WrapperGeneratorService } from './wrapper-generator.service';

const execAsync = promisify(exec);

export interface FlatteningResult {
  success: boolean;
  outputPath?: string;
  error?: string;
  wrapperPath?: string | undefined;
}

/**
 * Service for flattening Solidity contracts using Foundry
 */
export class FlatteningService {
  /**
   * Flatten all contracts in a folder using Foundry
   * @param folderPath - Path to the folder containing Solidity contracts
   * @param outputDir - Directory to save the flattened contract (default: contracts/output)
   * @returns Promise<FlatteningResult>
   */
  public static async flattenFolder(
    folderPath: string,
    outputDir: string = 'contracts/output'
  ): Promise<FlatteningResult> {
    let wrapperPath: string | undefined;

    try {
      // Check if folder exists
      if (!existsSync(folderPath)) {
        throw new Error(`Folder does not exist: ${folderPath}`);
      }

      // Create output directory if it doesn't exist
      if (!existsSync(outputDir)) {
        mkdirSync(outputDir, { recursive: true });
      }

      // Generate wrapper contract
      console.log(`🔧 Generating wrapper contract for folder: ${folderPath}`);
      wrapperPath =
        await WrapperGeneratorService.generateWrapperContract(folderPath);

      // Check if forge is available
      try {
        await execAsync('forge --version');
      } catch (error) {
        throw new Error(
          'Foundry (forge) is not installed or not in PATH. Please install Foundry: https://book.getfoundry.sh/getting-started/installation'
        );
      }

      // Flatten the wrapper contract
      const outputFileName = `flattened-${Date.now()}.sol`;
      const outputPath = path.join(outputDir, outputFileName);

      console.log(`🔨 Flattening contracts using Foundry...`);
      const flattenCommand = `forge flatten ${wrapperPath} -o ${outputPath}`;

      const { stdout, stderr } = await execAsync(flattenCommand);

      if (stderr && !stderr.includes('Warning')) {
        console.warn(`⚠️  Foundry warnings: ${stderr}`);
      }

      if (stdout) {
        console.log(`📄 Foundry output: ${stdout}`);
      }

      // Clean up wrapper contract
      if (wrapperPath && existsSync(wrapperPath)) {
        unlinkSync(wrapperPath);
        console.log(`🧹 Cleaned up wrapper contract: ${wrapperPath}`);
      }

      console.log(`✅ Successfully flattened contracts to: ${outputPath}`);

      return {
        success: true,
        outputPath,
        wrapperPath,
      };
    } catch (error) {
      // Clean up wrapper contract on error
      if (wrapperPath && existsSync(wrapperPath)) {
        try {
          unlinkSync(wrapperPath);
        } catch (cleanupError) {
          console.warn(
            `⚠️  Failed to clean up wrapper contract: ${cleanupError}`
          );
        }
      }

      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`❌ Flattening failed: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
        wrapperPath,
      };
    }
  }

  /**
   * Synchronous version for CLI usage
   * @param folderPath - Path to the folder containing Solidity contracts
   * @returns string - Output path or error message
   */
  public static flattenFolderSync(folderPath: string): string {
    try {
      // Check if folder exists first
      if (!existsSync(folderPath)) {
        return `Error: Folder does not exist: ${folderPath}`;
      }

      // This is a simplified sync version - in practice, you'd want to use the async version
      this.flattenFolder(folderPath);
      return `Flattening initiated for: ${folderPath}`;
    } catch (error) {
      return `Error: ${error instanceof Error ? error.message : String(error)}`;
    }
  }
}

// Export for CLI usage
export const flattenFolder = FlatteningService.flattenFolderSync;
