import { readdir, writeFileSync } from 'fs';
import { promisify } from 'util';
import path from 'path';

const readdirAsync = promisify(readdir);

/**
 * Generates a wrapper contract that imports all Solidity files in a folder
 * This wrapper contract is used for flattening all contracts into a single file
 */
export class WrapperGeneratorService {
  /**
   * Generate a wrapper contract that imports all .sol files in the specified folder
   * @param folderPath - Path to the folder containing Solidity contracts
   * @returns Path to the generated wrapper contract
   */
  public static async generateWrapperContract(
    folderPath: string
  ): Promise<string> {
    try {
      const files = await readdirAsync(folderPath);

      const solidityFiles = files
        .filter((f) => f.endsWith('.sol') && f !== 'Wrapper.sol')
        .map((f) => `import "./${f}";`)
        .join('\n');

      const content = `// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

${solidityFiles}

contract Wrapper {}
`;

      const tempPath = path.join(folderPath, 'Wrapper.sol');
      writeFileSync(tempPath, content);

      console.log(`✅ Generated wrapper contract at: ${tempPath}`);
      console.log(
        `📦 Imported ${files.filter((f) => f.endsWith('.sol') && f !== 'Wrapper.sol').length} contracts`
      );

      return tempPath;
    } catch (error) {
      throw new Error(
        `Failed to generate wrapper contract: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Synchronous version of generateWrapperContract for compatibility
   * @param folderPath - Path to the folder containing Solidity contracts
   * @returns Path to the generated wrapper contract
   */
  public static generateWrapperContractSync(folderPath: string): string {
    try {
      const { readdirSync } = require('fs');
      const files: string[] = readdirSync(folderPath);

      const solidityFiles = files
        .filter((f: string) => f.endsWith('.sol') && f !== 'Wrapper.sol')
        .map((f: string) => `import "./${f}";`)
        .join('\n');

      const content = `// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

${solidityFiles}

contract Wrapper {}
`;

      const tempPath = path.join(folderPath, 'Wrapper.sol');
      writeFileSync(tempPath, content);

      console.log(`✅ Generated wrapper contract at: ${tempPath}`);
      console.log(
        `📦 Imported ${files.filter((f: string) => f.endsWith('.sol') && f !== 'Wrapper.sol').length} contracts`
      );

      return tempPath;
    } catch (error) {
      throw new Error(
        `Failed to generate wrapper contract: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}

export default WrapperGeneratorService.generateWrapperContractSync;
