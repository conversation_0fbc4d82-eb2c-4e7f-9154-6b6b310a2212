import { exec } from 'child_process';
import { promisify } from 'util';
import { existsSync } from 'fs';
import { SlitherAstResult } from '../types/ast';

const execAsync = promisify(exec);

/**
 * Service for parsing Solidity contracts using Slither
 */
export class SlitherService {
  private static readonly DEFAULT_TIMEOUT = 30000; // 30 seconds

  /**
   * Parse a Solidity file using Slither to get AST and CFG
   * @param filePath - Path to the Solidity file
   * @param timeout - Timeout in milliseconds (default: 30000)
   * @returns Promise<SlitherAstResult>
   */
  public static async parseContract(
    filePath: string,
    timeout: number = SlitherService.DEFAULT_TIMEOUT
  ): Promise<SlitherAstResult> {
    try {
      // Check if file exists
      if (!existsSync(filePath)) {
        throw new Error(`File does not exist: ${filePath}`);
      }

      // Check if slither is available
      try {
        await execAsync('slither --version', { timeout: 5000 });
      } catch (error) {
        throw new Error(
          'Slither is not installed or not in PATH. Please install Slither: pip install slither-analyzer'
        );
      }

      console.log(`🔍 Parsing contract with Slither: ${filePath}`);

      // Run slither to get analysis results in JSON format
      const analysisCommand = `slither "${filePath}" --json -`;

      const { stdout: analysisOutput, stderr: analysisError } = await execAsync(
        analysisCommand,
        {
          timeout,
          maxBuffer: 1024 * 1024 * 10, // 10MB buffer
        }
      );

      if (
        analysisError &&
        !analysisError.includes('INFO:') &&
        !analysisError.includes('WARNING:')
      ) {
        console.warn(`⚠️  Slither analysis warnings: ${analysisError}`);
      }

      let ast;
      try {
        // Parse the JSON output from Slither
        if (analysisOutput.trim()) {
          ast = JSON.parse(analysisOutput);
        } else {
          throw new Error('No JSON output from Slither analysis');
        }
      } catch (parseError) {
        throw new Error(
          `Failed to parse Slither JSON output: ${parseError instanceof Error ? parseError.message : String(parseError)}`
        );
      }

      // Try to get CFG (Control Flow Graph) - this might not always be available
      let cfg: any = undefined;
      try {
        const cfgCommand = `slither "${filePath}" --print cfg`;
        const { stdout: cfgOutput } = await execAsync(cfgCommand, {
          timeout,
          maxBuffer: 1024 * 1024 * 10,
        });

        // CFG output is usually text-based, not JSON
        // We'll store it as raw output for now
        if (cfgOutput) {
          cfg = { raw: cfgOutput };
        }
      } catch (cfgError) {
        console.warn(
          `⚠️  Could not generate CFG: ${cfgError instanceof Error ? cfgError.message : String(cfgError)}`
        );
      }

      console.log(`✅ Successfully parsed contract: ${filePath}`);

      return {
        success: true,
        ast,
        cfg,
        filePath,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`❌ Failed to parse contract: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
        filePath,
      };
    }
  }

  /**
   * Parse multiple contracts
   * @param filePaths - Array of file paths to parse
   * @param timeout - Timeout per file in milliseconds
   * @returns Promise<SlitherAstResult[]>
   */
  public static async parseContracts(
    filePaths: string[],
    timeout: number = SlitherService.DEFAULT_TIMEOUT
  ): Promise<SlitherAstResult[]> {
    const results: SlitherAstResult[] = [];

    for (const filePath of filePaths) {
      const result = await this.parseContract(filePath, timeout);
      results.push(result);
    }

    return results;
  }

  /**
   * Check if Slither is available
   * @returns Promise<boolean>
   */
  public static async isSlitherAvailable(): Promise<boolean> {
    try {
      await execAsync('slither --version', { timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }
}
