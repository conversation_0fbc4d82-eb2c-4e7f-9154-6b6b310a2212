import { exec } from 'child_process';
import { promisify } from 'util';
import { existsSync } from 'fs';
import { SlitherAstResult } from '../types/ast';

const execAsync = promisify(exec);

/**
 * Service for parsing Solidity contracts using Slither
 */
export class SlitherService {
  private static readonly DEFAULT_TIMEOUT = 30000; // 30 seconds

  /**
   * Parse a Solidity file using Slither to get AST and CFG
   * @param filePath - Path to the Solidity file
   * @param timeout - Timeout in milliseconds (default: 30000)
   * @returns Promise<SlitherAstResult>
   */
  public static async parseContract(
    filePath: string,
    timeout: number = SlitherService.DEFAULT_TIMEOUT
  ): Promise<SlitherAstResult> {
    try {
      // Check if file exists
      if (!existsSync(filePath)) {
        throw new Error(`File does not exist: ${filePath}`);
      }

      // Check if slither is available
      try {
        await execAsync('slither --version', { timeout: 5000 });
      } catch (error) {
        throw new Error(
          'Slither is not installed or not in PATH. Please install Slither: pip install slither-analyzer'
        );
      }

      console.log(`🔍 Parsing contract with Slither: ${filePath}`);

      // Run slither to get analysis results in JSON format
      const tempJsonFile = `/tmp/slither-${Date.now()}.json`;
      const analysisCommand = `slither "${filePath}" --json "${tempJsonFile}"`;

      try {
        const { stderr: analysisError } = await execAsync(analysisCommand, {
          timeout,
          maxBuffer: 1024 * 1024 * 10, // 10MB buffer
        });

        // Slither outputs analysis info to stderr, which is normal
        if (analysisError) {
          console.log(
            `📊 Slither analysis output: ${analysisError.substring(0, 200)}...`
          );
        }
      } catch (execError) {
        // Slither often returns non-zero exit codes when it finds issues
        // This is normal behavior, so we continue if the JSON file was created
        console.log(
          `📊 Slither completed analysis (exit code indicates findings)`
        );
      }

      let ast;
      try {
        // Read the JSON file that Slither created
        const fs = await import('fs');
        if (fs.existsSync(tempJsonFile)) {
          const jsonContent = fs.readFileSync(tempJsonFile, 'utf8');
          ast = JSON.parse(jsonContent);

          // Clean up the temp file
          fs.unlinkSync(tempJsonFile);
        } else {
          throw new Error(
            'Slither did not create the expected JSON output file'
          );
        }
      } catch (parseError) {
        // Clean up temp file on error
        try {
          const fs = await import('fs');
          if (fs.existsSync(tempJsonFile)) {
            fs.unlinkSync(tempJsonFile);
          }
        } catch {
          // Ignore cleanup errors
        }

        throw new Error(
          `Failed to parse Slither JSON output: ${parseError instanceof Error ? parseError.message : String(parseError)}`
        );
      }

      // TODO: Add CFG (Control Flow Graph) generation when needed
      // Currently disabled to avoid generating unwanted .dot files
      // const cfgCommand = `slither "${filePath}" --print cfg`;
      let cfg: any = undefined;

      console.log(`✅ Successfully parsed contract: ${filePath}`);

      return {
        success: true,
        ast,
        cfg,
        filePath,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`❌ Failed to parse contract: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
        filePath,
      };
    }
  }

  /**
   * Parse multiple contracts
   * @param filePaths - Array of file paths to parse
   * @param timeout - Timeout per file in milliseconds
   * @returns Promise<SlitherAstResult[]>
   */
  public static async parseContracts(
    filePaths: string[],
    timeout: number = SlitherService.DEFAULT_TIMEOUT
  ): Promise<SlitherAstResult[]> {
    const results: SlitherAstResult[] = [];

    for (const filePath of filePaths) {
      const result = await this.parseContract(filePath, timeout);
      results.push(result);
    }

    return results;
  }

  /**
   * Check if Slither is available
   * @returns Promise<boolean>
   */
  public static async isSlitherAvailable(): Promise<boolean> {
    try {
      await execAsync('slither --version', { timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }
}
