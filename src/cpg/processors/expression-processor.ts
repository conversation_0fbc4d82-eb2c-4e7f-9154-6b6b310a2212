/**
 * Expression Processor
 * Handles various expression AST nodes (Assignment, FunctionCall, etc.)
 */

import { SolidityAstNode } from '../../services/solidity-ast.service';
import {
  ExpressionNode,
  AssignmentNode,
  StatementNode,
  WritesEdge,
  CallsEdge,
} from '../../types/cpg';
import { BaseProcessor, ProcessorResult } from './base-processor';

export class ExpressionProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    const expressionTypes = [
      'Assignment',
      'FunctionCall',
      'MemberAccess',
      'Identifier',
      'Literal',
      'BinaryOperation',
      'UnaryOperation',
      'ConditionalOperator',
      'IndexAccess',
      'ExpressionStatement',
      'Block',
      'IfStatement',
      'WhileStatement',
      'ForStatement',
      'Return',
      'EmitStatement',
      'VariableDeclarationStatement',
    ];
    return expressionTypes.includes(nodeType);
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 50;
  }

  /**
   * Process expression AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Determine the specific type of expression and create appropriate node
    const result = this.createExpressionNode(
      astNode,
      nodeId,
      sourceLocation,
      filePath
    );

    // Create edges
    const edges = [...result.edges];
    const childNodeIds = [...result.childNodeIds];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Add expression-specific edges
    const expressionEdges = this.createExpressionEdges(astNode, nodeId);
    edges.push(...expressionEdges);

    const finalResult: ProcessorResult = {
      node: result.node,
      edges,
      childNodeIds,
    };

    this.validateResult(finalResult);
    return finalResult;
  }

  /**
   * Create appropriate expression node based on AST node type
   */
  private createExpressionNode(
    astNode: SolidityAstNode,
    nodeId: string,
    sourceLocation: any,
    _filePath: string
  ): ProcessorResult {
    switch (astNode.nodeType) {
      case 'Assignment':
        return this.createAssignmentNode(astNode, nodeId, sourceLocation);

      case 'FunctionCall':
        return this.createFunctionCallNode(astNode, nodeId, sourceLocation);

      case 'ExpressionStatement':
      case 'Block':
      case 'IfStatement':
      case 'WhileStatement':
      case 'ForStatement':
      case 'Return':
      case 'EmitStatement':
      case 'VariableDeclarationStatement':
        return this.createStatementNode(astNode, nodeId, sourceLocation);

      default:
        return this.createGenericExpressionNode(
          astNode,
          nodeId,
          sourceLocation
        );
    }
  }

  /**
   * Create assignment node
   */
  private createAssignmentNode(
    astNode: SolidityAstNode,
    nodeId: string,
    sourceLocation: any
  ): ProcessorResult {
    const isSink = this.isTaintSink(astNode);
    const sinkType = isSink ? this.determineSinkType(astNode) : undefined;

    const assignmentNode: AssignmentNode = {
      id: nodeId,
      type: 'ASSIGNMENT',
      name: `assignment_${astNode.operator || '='}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        operator: astNode.operator || '=',
        isSink,
        ...(sinkType && { sinkType }),
      },
    };

    const childNodeIds: string[] = [];

    // Mark left and right operands for processing
    if (astNode.left) {
      childNodeIds.push(`${nodeId}_left`);
    }
    if (astNode.right) {
      childNodeIds.push(`${nodeId}_right`);
    }

    return {
      node: assignmentNode,
      edges: [],
      childNodeIds,
    };
  }

  /**
   * Create function call node
   */
  private createFunctionCallNode(
    astNode: SolidityAstNode,
    nodeId: string,
    sourceLocation: any
  ): ProcessorResult {
    const functionName = this.extractFunctionName(astNode);
    const isExternal = this.isExternalCall(astNode);

    const functionCallNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: `call_${functionName}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'FunctionCall',
        functionName,
        isExternal,
        isTainted: isExternal, // External calls are tainted
        taintSources: isExternal ? ['external_call'] : [],
      },
    };

    const childNodeIds: string[] = [];

    // Mark arguments for processing
    if (astNode['arguments']) {
      astNode['arguments']['forEach']((_arg: any, index: number) => {
        childNodeIds.push(`${nodeId}_arg_${index}`);
      });
    }

    return {
      node: functionCallNode,
      edges: [],
      childNodeIds,
    };
  }

  /**
   * Create statement node
   */
  private createStatementNode(
    astNode: SolidityAstNode,
    nodeId: string,
    sourceLocation: any
  ): ProcessorResult {
    const statementNode: StatementNode = {
      id: nodeId,
      type: 'STATEMENT',
      name: astNode.nodeType.toLowerCase(),
      ...(sourceLocation && { sourceLocation }),
      properties: {
        statementType: astNode.nodeType,
        ...(astNode.expression && { expression: 'present' }),
      },
    };

    const childNodeIds: string[] = [];

    // Mark child statements/expressions for processing
    this.markStatementChildrenForProcessing(astNode, nodeId, childNodeIds);

    return {
      node: statementNode,
      edges: [],
      childNodeIds,
    };
  }

  /**
   * Create generic expression node
   */
  private createGenericExpressionNode(
    astNode: SolidityAstNode,
    nodeId: string,
    sourceLocation: any
  ): ProcessorResult {
    const isTainted = this.isTaintSource(astNode);

    const expressionNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: astNode.nodeType.toLowerCase(),
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: astNode.nodeType,
        ...(astNode.operator && { operator: astNode.operator }),
        ...(astNode['value'] && { value: String(astNode['value']) }),
        isTainted,
        taintSources: isTainted ? [this.getTaintSourceType(astNode)] : [],
      },
    };

    const childNodeIds: string[] = [];

    // Mark operands for processing
    this.markExpressionChildrenForProcessing(astNode, nodeId, childNodeIds);

    return {
      node: expressionNode,
      edges: [],
      childNodeIds,
    };
  }

  /**
   * Create expression-specific edges (WRITES, CALLS, etc.)
   */
  private createExpressionEdges(
    astNode: SolidityAstNode,
    nodeId: string
  ): Array<WritesEdge | CallsEdge> {
    const edges: Array<WritesEdge | CallsEdge> = [];

    // Create WRITES edge for assignments
    if (astNode.nodeType === 'Assignment' && astNode.left) {
      const targetId = this.getVariableId(astNode.left);
      if (targetId) {
        const writesEdge: WritesEdge = {
          id: this.context.generateEdgeId(),
          type: 'WRITES',
          source: nodeId,
          target: targetId,
          properties: {
            writeType: 'assignment',
            isSink: this.isTaintSink(astNode),
          },
        };
        edges.push(writesEdge);
      }
    }

    // Create CALLS edge for function calls
    if (astNode.nodeType === 'FunctionCall') {
      const functionName = this.extractFunctionName(astNode);
      const callsEdge: CallsEdge = {
        id: this.context.generateEdgeId(),
        type: 'CALLS',
        source: nodeId,
        target: `function_${functionName}`, // Will be resolved later
        properties: {
          callType: this.isExternalCall(astNode) ? 'external' : 'internal',
        },
      };
      edges.push(callsEdge);
    }

    return edges;
  }

  /**
   * Extract function name from function call
   */
  private extractFunctionName(astNode: SolidityAstNode): string {
    if (astNode.expression?.name) {
      return astNode.expression.name;
    }
    if (astNode.expression?.['memberName']) {
      return astNode.expression['memberName'];
    }
    return 'unknown';
  }

  /**
   * Check if function call is external
   */
  private isExternalCall(astNode: SolidityAstNode): boolean {
    // Heuristics for external calls
    if (
      astNode['kind'] === 'functionCall' &&
      astNode.expression?.expression?.name
    ) {
      const target = astNode.expression.expression.name;
      return !['this', 'super'].includes(target);
    }
    return false;
  }

  /**
   * Determine sink type for assignments
   */
  private determineSinkType(
    astNode: SolidityAstNode
  ): 'STATE_WRITE' | 'EXTERNAL_CALL' | 'STORAGE_WRITE' | 'EMIT_EVENT' {
    if (astNode.left?.['stateVariable'] === true) {
      return 'STATE_WRITE';
    }
    return 'STORAGE_WRITE';
  }

  /**
   * Get variable ID from identifier
   */
  private getVariableId(identifierNode: SolidityAstNode): string | undefined {
    if (identifierNode.name) {
      return `var_${identifierNode.name}`;
    }
    return undefined;
  }

  /**
   * Get taint source type
   */
  private getTaintSourceType(astNode: SolidityAstNode): string {
    if (
      astNode.nodeType === 'MemberAccess' &&
      astNode.expression?.name === 'msg'
    ) {
      return 'msg_context';
    }
    if (astNode.nodeType === 'FunctionCall') {
      return 'function_call';
    }
    return 'unknown';
  }

  /**
   * Mark statement children for processing
   */
  private markStatementChildrenForProcessing(
    astNode: SolidityAstNode,
    nodeId: string,
    childNodeIds: string[]
  ): void {
    // Handle different statement types
    if (astNode['statements']) {
      astNode['statements']['forEach']((_stmt: any, index: number) => {
        childNodeIds.push(`${nodeId}_stmt_${index}`);
      });
    }
    if (astNode.expression) {
      childNodeIds.push(`${nodeId}_expr`);
    }
    if (astNode['condition']) {
      childNodeIds.push(`${nodeId}_condition`);
    }
    if (astNode['trueBody']) {
      childNodeIds.push(`${nodeId}_trueBody`);
    }
    if (astNode['falseBody']) {
      childNodeIds.push(`${nodeId}_falseBody`);
    }
  }

  /**
   * Mark expression children for processing
   */
  private markExpressionChildrenForProcessing(
    astNode: SolidityAstNode,
    nodeId: string,
    childNodeIds: string[]
  ): void {
    if (astNode.left) {
      childNodeIds.push(`${nodeId}_left`);
    }
    if (astNode.right) {
      childNodeIds.push(`${nodeId}_right`);
    }
    if (astNode.expression) {
      childNodeIds.push(`${nodeId}_expr`);
    }
    if (astNode['subExpression']) {
      childNodeIds.push(`${nodeId}_subExpr`);
    }
  }
}
