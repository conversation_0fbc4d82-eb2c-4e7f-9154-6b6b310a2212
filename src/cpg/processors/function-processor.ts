/**
 * Function Processor
 * Handles FunctionDefinition AST nodes
 */

import { SolidityAstNode } from '../../services/solidity-ast.service';
import { FunctionNode, ModifiesEdge } from '../../types/cpg';
import { BaseProcessor, ProcessorResult } from './base-processor';

export class FunctionProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'FunctionDefinition';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 80;
  }

  /**
   * Process FunctionDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract function information
    const signature = this.buildFunctionSignature(astNode);
    const parameters = this.extractParameters(astNode.parameters);
    const returns = this.extractParameters(astNode.returnParameters);
    const modifiers = this.extractModifiers(astNode);

    // Phase 1: Critical Missing Features Detection
    const isReceive = this.isReceiveFunction(astNode);
    const isFallback = this.isFallbackFunction(astNode);
    const isVirtual = this.isVirtualFunction(astNode);
    const isOverride = this.isOverrideFunction(astNode);

    // Create function node
    const functionNode: FunctionNode = {
      id: nodeId,
      type: 'FUNCTION',
      name:
        astNode.name ||
        (astNode['kind'] === 'constructor' ? 'constructor' : 'unknown'),
      ...(sourceLocation && { sourceLocation }),
      properties: {
        signature,
        visibility: astNode.visibility || 'internal',
        stateMutability: astNode.stateMutability || 'nonpayable',
        modifiers: modifiers.map((m) => m.name),
        parameters,
        returns,
        isVirtual,
        isOverride,
        isReceive,
        isFallback,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Create MODIFIES edges for function modifiers
    const modifierEdges = this.createModifierEdges(nodeId, modifiers);
    edges.push(...modifierEdges);

    // Update analysis context
    this.context.updateAnalysisContext({
      currentFunction: signature,
    });

    // Mark child nodes for processing
    this.markChildNodesForProcessing(astNode, nodeId, childNodeIds);

    const result: ProcessorResult = {
      node: functionNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract modifier information from function AST node
   */
  private extractModifiers(
    astNode: SolidityAstNode
  ): Array<{ name: string; arguments?: any[] }> {
    if (!astNode.modifiers || !Array.isArray(astNode.modifiers)) {
      return [];
    }

    return astNode.modifiers.map((modifier: any) => ({
      name: modifier.modifierName?.name || modifier.name || 'unknown',
      arguments: modifier.arguments || [],
    }));
  }

  /**
   * Create MODIFIES edges for function modifiers
   */
  private createModifierEdges(
    functionId: string,
    modifiers: Array<{ name: string; arguments?: any[] }>
  ): ModifiesEdge[] {
    return modifiers.map((modifier) => ({
      id: this.context.generateEdgeId(),
      type: 'MODIFIES',
      source: functionId,
      target: `modifier_${modifier.name}`, // Will be resolved later
      properties: {
        modifierName: modifier.name,
      },
    }));
  }

  /**
   * Mark child nodes for processing
   */
  private markChildNodesForProcessing(
    astNode: SolidityAstNode,
    parentId: string,
    childNodeIds: string[]
  ): void {
    // Process parameters
    if (astNode.parameters?.parameters) {
      astNode.parameters.parameters['forEach']((_param: any, index: number) => {
        childNodeIds.push(`${parentId}_param_${index}`);
      });
    }

    // Process return parameters
    if (astNode.returnParameters?.parameters) {
      astNode.returnParameters.parameters['forEach'](
        (_param: any, index: number) => {
          childNodeIds.push(`${parentId}_return_${index}`);
        }
      );
    }

    // Process function body
    if (astNode.body) {
      childNodeIds.push(`${parentId}_body`);
    }

    // Process modifiers
    if (astNode['modifiers']) {
      astNode['modifiers']['forEach']((_modifier: any, index: number) => {
        childNodeIds.push(`${parentId}_modifier_${index}`);
      });
    }
  }

  /**
   * Phase 1: Critical Missing Features Detection Methods
   */

  /**
   * Check if function is a receive function
   */
  private isReceiveFunction(astNode: SolidityAstNode): boolean {
    return astNode['kind'] === 'receive' || astNode.name === 'receive';
  }

  /**
   * Check if function is a fallback function
   */
  private isFallbackFunction(astNode: SolidityAstNode): boolean {
    return astNode['kind'] === 'fallback' || astNode.name === 'fallback';
  }

  /**
   * Check if function is virtual
   */
  private isVirtualFunction(astNode: SolidityAstNode): boolean {
    return Boolean(
      astNode['virtual'] === true ||
        (astNode.modifiers &&
          astNode.modifiers.some((m: any) => m.name === 'virtual'))
    );
  }

  /**
   * Check if function is an override
   */
  private isOverrideFunction(astNode: SolidityAstNode): boolean {
    return Boolean(
      (astNode['override'] !== undefined && astNode['override'] !== null) ||
        (astNode.modifiers &&
          astNode.modifiers.some((m: any) => m.name === 'override'))
    );
  }
}
