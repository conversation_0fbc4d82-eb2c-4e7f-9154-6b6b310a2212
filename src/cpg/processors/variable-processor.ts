/**
 * Variable Processor
 * Handles VariableDeclaration AST nodes
 */

import { SolidityAstNode } from '../../services/solidity-ast.service';
import { VariableNode, VariableScope } from '../../types/cpg';
import { BaseProcessor, ProcessorResult } from './base-processor';

export class VariableProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'VariableDeclaration';
  }

  /**
   * Get the priority of this processor
   */
  getPriority(): number {
    return 70;
  }

  /**
   * Process VariableDeclaration AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(astNode, filePath);

    // Determine variable scope and characteristics
    const scopeInfo = this.determineVariableScope(astNode);
    const typeInfo = this.extractTypeInfo(astNode);
    const storageInfo = this.extractStorageInfo(astNode);
    const taintInfo = this.analyzeTaintProperties(astNode, scopeInfo);

    // Create variable node
    const variableNode: VariableNode = {
      id: nodeId,
      type: 'VARIABLE',
      name: astNode.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        variableType: typeInfo.typeString,
        visibility: astNode.visibility,
        scope: scopeInfo.scope,
        isStateVariable: scopeInfo.isStateVariable,
        isTainted: taintInfo.isTainted,
        ...(taintInfo.taintSource && { taintSource: taintInfo.taintSource }),
        ...storageInfo,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Update analysis context
    this.updateAnalysisContextForVariable(nodeId, scopeInfo, taintInfo);

    // Mark initial value expression for processing if present
    if (astNode.value) {
      childNodeIds.push(`${nodeId}_initialValue`);
    }

    const result: ProcessorResult = {
      node: variableNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Determine variable scope with enhanced logic
   */
  private determineVariableScope(astNode: SolidityAstNode): {
    scope: VariableScope;
    isStateVariable: boolean;
  } {
    // Explicit state variable check
    if (astNode.stateVariable === true) {
      return { scope: 'STATE', isStateVariable: true };
    }

    // Check if it's in a function parameter list
    if (this.isInParameterContext(astNode)) {
      return { scope: 'PARAMETER', isStateVariable: false };
    }

    // Check if it's in a return parameter list
    if (this.isInReturnParameterContext(astNode)) {
      return { scope: 'RETURN', isStateVariable: false };
    }

    // Check storage location for additional context
    if (astNode.storageLocation === 'storage' && !astNode.stateVariable) {
      // Local storage variable
      return { scope: 'LOCAL', isStateVariable: false };
    }

    // Default to local variable
    return { scope: 'LOCAL', isStateVariable: false };
  }

  /**
   * Extract storage-related information
   */
  private extractStorageInfo(astNode: SolidityAstNode): {
    storageLocation?: string;
    constant?: boolean;
    immutable?: boolean;
  } {
    return {
      ...(astNode.storageLocation && { storageLocation: astNode.storageLocation }),
      ...(astNode.constant !== undefined && { constant: astNode.constant }),
      ...(astNode.mutability === 'immutable' && { immutable: true }),
    };
  }

  /**
   * Analyze taint properties of the variable
   */
  private analyzeTaintProperties(
    astNode: SolidityAstNode,
    scopeInfo: { scope: VariableScope; isStateVariable: boolean }
  ): {
    isTainted: boolean;
    taintSource?: string;
  } {
    // Parameters are potential taint sources (user input)
    if (scopeInfo.scope === 'PARAMETER') {
      return {
        isTainted: true,
        taintSource: 'user_input',
      };
    }

    // Variables with initial values from external sources
    if (astNode.value && this.isExternalTaintSource(astNode.value)) {
      return {
        isTainted: true,
        taintSource: 'external_call',
      };
    }

    // msg.* variables are tainted
    if (this.isMsgVariable(astNode)) {
      return {
        isTainted: true,
        taintSource: 'msg_context',
      };
    }

    return { isTainted: false };
  }

  /**
   * Check if variable is in a parameter context
   */
  private isInParameterContext(astNode: SolidityAstNode): boolean {
    // This would need to be determined from the parent context
    // For now, we use heuristics based on the AST structure
    return astNode.scope?.includes?.('Parameter') || false;
  }

  /**
   * Check if variable is in a return parameter context
   */
  private isInReturnParameterContext(astNode: SolidityAstNode): boolean {
    // This would need to be determined from the parent context
    return false; // Placeholder
  }

  /**
   * Check if the initial value is from an external taint source
   */
  private isExternalTaintSource(valueNode: SolidityAstNode): boolean {
    // Check for function calls
    if (valueNode.nodeType === 'FunctionCall') {
      return true;
    }

    // Check for member access to msg, tx, block
    if (valueNode.nodeType === 'MemberAccess') {
      const expression = valueNode.expression;
      if (expression?.name && ['msg', 'tx', 'block'].includes(expression.name)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if variable is related to msg context
   */
  private isMsgVariable(astNode: SolidityAstNode): boolean {
    return astNode.name?.startsWith('msg') || false;
  }

  /**
   * Update analysis context with variable information
   */
  private updateAnalysisContextForVariable(
    nodeId: string,
    scopeInfo: { scope: VariableScope; isStateVariable: boolean },
    taintInfo: { isTainted: boolean; taintSource?: string }
  ): void {
    const context = this.context.getAnalysisContext();

    // Update variable scopes
    context.variableScopes.set(nodeId, scopeInfo.scope);

    // Update state variables set
    if (scopeInfo.isStateVariable) {
      context.stateVariables.add(nodeId);
    }

    // Update tainted variables set
    if (taintInfo.isTainted) {
      context.taintedVariables.add(nodeId);
    }

    this.context.updateAnalysisContext(context);
  }

  /**
   * Get variable mutability information
   */
  private getMutability(astNode: SolidityAstNode): string {
    if (astNode.mutability) {
      return astNode.mutability;
    }
    if (astNode.constant) {
      return 'constant';
    }
    return 'mutable';
  }

  /**
   * Check if variable is indexed (for events)
   */
  private isIndexed(astNode: SolidityAstNode): boolean {
    return astNode.indexed === true;
  }

  /**
   * Get variable's type name information
   */
  private getTypeName(astNode: SolidityAstNode): {
    name?: string;
    nodeType?: string;
  } {
    if (!astNode.typeName) {
      return {};
    }

    return {
      name: astNode.typeName.name,
      nodeType: astNode.typeName.nodeType,
    };
  }
}
