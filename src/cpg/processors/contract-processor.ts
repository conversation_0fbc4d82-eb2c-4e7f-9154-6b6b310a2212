/**
 * Contract Processor
 * Handles ContractDefinition AST nodes
 */

import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ContractNode, InheritsEdge } from '../../types/cpg';
import { BaseProcessor, ProcessorResult } from './base-processor';

export class ContractProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ContractDefinition';
  }

  /**
   * Get the priority of this processor (contracts should be processed first)
   */
  override getPriority(): number {
    return 100;
  }

  /**
   * Process ContractDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract inheritance information
    const inheritance = this.extractInheritance(astNode);
    const libraries = this.extractLibraries(astNode);
    const dependencies = this._extractDependencies(astNode);

    // Phase 1: Abstract contract detection
    const isAbstract = this.isAbstractContract(astNode);

    // Create contract node
    const contractNode: ContractNode = {
      id: nodeId,
      type: 'CONTRACT',
      name: astNode.name || 'Unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        inheritance,
        libraries,
        dependencies,
        isAbstract,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (if any)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Create INHERITS edges for base contracts
    const inheritanceEdges = this.createInheritanceEdges(nodeId, astNode);
    edges.push(...inheritanceEdges);

    // Update analysis context
    if (astNode.name) {
      this.context.updateAnalysisContext({
        currentContract: astNode.name,
      });
    }

    // Add child nodes that should be processed
    if (astNode.nodes) {
      astNode.nodes.forEach((_childNode, index) => {
        // We don't process children here, just mark them for processing
        childNodeIds.push(`${nodeId}_child_${index}`);
      });
    }

    const result: ProcessorResult = {
      node: contractNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract inheritance information from contract AST node
   */
  private extractInheritance(astNode: SolidityAstNode): string[] {
    if (!astNode.baseContracts || !Array.isArray(astNode.baseContracts)) {
      return [];
    }

    return astNode.baseContracts
      .map((baseContract: any) => {
        // Handle different AST structures
        if (baseContract.baseName?.name) {
          return baseContract.baseName.name;
        }
        if (baseContract.name) {
          return baseContract.name;
        }
        return 'unknown';
      })
      .filter((name: string) => name !== 'unknown');
  }

  /**
   * Extract library usage information
   */
  private extractLibraries(astNode: SolidityAstNode): string[] {
    const libraries: string[] = [];

    // Look for UsingForDirective nodes in the contract
    if (astNode.nodes) {
      astNode.nodes.forEach((node: any) => {
        if (node.nodeType === 'UsingForDirective') {
          if (node.libraryName?.name) {
            libraries.push(node.libraryName.name);
          }
        }
      });
    }

    return libraries;
  }

  /**
   * Extract contract dependencies (imports, etc.)
   */
  private _extractDependencies(_astNode: SolidityAstNode): string[] {
    // Dependencies are typically handled at the source unit level
    // For now, return empty array - can be enhanced later
    return [];
  }

  /**
   * Create inheritance edges for base contracts
   */
  private createInheritanceEdges(
    contractId: string,
    astNode: SolidityAstNode
  ): InheritsEdge[] {
    const edges: InheritsEdge[] = [];

    if (!astNode.baseContracts || !Array.isArray(astNode.baseContracts)) {
      return edges;
    }

    astNode.baseContracts.forEach((baseContract: any) => {
      const baseName = baseContract.baseName?.name || baseContract.name;
      if (baseName) {
        const edge: InheritsEdge = {
          id: this.context.generateEdgeId(),
          type: 'INHERITS',
          source: contractId,
          target: `contract_${baseName}`, // Will be resolved later
          properties: {
            inheritanceType: this.determineInheritanceType(astNode),
          },
        };
        edges.push(edge);
      }
    });

    return edges;
  }

  /**
   * Determine the type of inheritance (contract vs interface)
   */
  private determineInheritanceType(
    astNode: SolidityAstNode
  ): 'CONTRACT' | 'INTERFACE' {
    // Check the contract kind
    if (astNode.contractKind === 'interface') {
      return 'INTERFACE';
    }
    return 'CONTRACT';
  }

  /**
   * Phase 1: Check if contract is abstract
   */
  private isAbstractContract(astNode: SolidityAstNode): boolean {
    // Check if contract is explicitly marked as abstract
    if (astNode['abstract'] === true) {
      return true;
    }

    // Check if contract has abstract functions (functions without implementation)
    if (astNode.nodes) {
      return astNode.nodes.some((node: any) => {
        return (
          node.nodeType === 'FunctionDefinition' &&
          !node.body &&
          node.virtual === true
        );
      });
    }

    return false;
  }
}
