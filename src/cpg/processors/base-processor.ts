/**
 * Base Processor Interface
 * Defines the contract for all AST node processors
 */

import { SolidityAstNode } from '../../services/solidity-ast.service';
import {
  CpgNode,
  CpgEdge,
  SourceLocation,
  AnalysisContext,
} from '../../types/cpg';

export interface ProcessorResult {
  node: CpgNode;
  edges: CpgEdge[];
  childNodeIds: string[];
}

export interface ProcessorContext {
  generateNodeId(): string;
  generateEdgeId(): string;
  getAnalysisContext(): AnalysisContext;
  updateAnalysisContext(updates: Partial<AnalysisContext>): void;
  addNode(node: CpgNode): void;
  addEdge(edge: CpgEdge): void;
  getNode(id: string): CpgNode | undefined;
  extractSourceLocation(
    astNode: SolidityAstNode,
    filePath: string
  ): SourceLocation | undefined;
}

export abstract class BaseProcessor {
  protected context: ProcessorContext;

  constructor(context: ProcessorContext) {
    this.context = context;
  }

  /**
   * Check if this processor can handle the given AST node type
   */
  abstract canProcess(nodeType: string): boolean;

  /**
   * Process the AST node and return the CPG node with edges
   */
  abstract process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult;

  /**
   * Get the priority of this processor (higher = processed first)
   */
  getPriority(): number {
    return 0;
  }

  /**
   * Validate the processed result
   */
  protected validateResult(result: ProcessorResult): void {
    if (!result.node) {
      throw new Error('Processor must return a valid node');
    }
    if (!result.node.id) {
      throw new Error('Node must have a valid ID');
    }
    if (!result.node.type) {
      throw new Error('Node must have a valid type');
    }
    if (!result.edges) {
      throw new Error('Processor must return edges array (can be empty)');
    }
    if (!result.childNodeIds) {
      throw new Error(
        'Processor must return childNodeIds array (can be empty)'
      );
    }
  }

  /**
   * Create a CONTAINS edge from parent to child
   */
  protected createContainsEdge(
    parentId: string,
    childId: string,
    order?: number
  ): CpgEdge {
    return {
      id: this.context.generateEdgeId(),
      type: 'CONTAINS',
      source: parentId,
      target: childId,
      properties: {
        ...(order !== undefined && { order }),
      },
    };
  }

  /**
   * Extract parameters from AST parameter list
   */
  protected extractParameters(
    parametersNode?: SolidityAstNode
  ): Array<{ name: string; type: string }> {
    if (!parametersNode || !parametersNode.parameters) {
      return [];
    }

    return parametersNode.parameters['map']((param: any) => ({
      name: param.name || 'unknown',
      type: param.typeDescriptions?.typeString || 'unknown',
    }));
  }

  /**
   * Build function signature from AST node
   */
  protected buildFunctionSignature(astNode: SolidityAstNode): string {
    const name = astNode.name || 'unknown';
    const params = this.extractParameters(astNode.parameters);
    const paramTypes = params.map((p) => p.type).join(',');
    return `${name}(${paramTypes})`;
  }

  /**
   * Determine variable scope from AST context
   */
  protected determineVariableScope(
    astNode: SolidityAstNode,
    parentContext?: string
  ): {
    scope: 'STATE' | 'LOCAL' | 'PARAMETER' | 'RETURN';
    isStateVariable: boolean;
  } {
    // Check if it's a state variable
    if (astNode.stateVariable === true) {
      return { scope: 'STATE', isStateVariable: true };
    }

    // Check if it's a parameter
    if (
      parentContext === 'parameters' ||
      astNode['scope']?.includes?.('Parameter')
    ) {
      return { scope: 'PARAMETER', isStateVariable: false };
    }

    // Check if it's a return parameter
    if (parentContext === 'returnParameters') {
      return { scope: 'RETURN', isStateVariable: false };
    }

    // Default to local variable
    return { scope: 'LOCAL', isStateVariable: false };
  }

  /**
   * Extract type information from AST node
   */
  protected extractTypeInfo(astNode: SolidityAstNode): {
    typeString: string;
    typeIdentifier: string;
  } {
    return {
      typeString: astNode.typeDescriptions?.typeString || 'unknown',
      typeIdentifier: astNode.typeDescriptions?.typeIdentifier || 'unknown',
    };
  }

  /**
   * Check if a node represents a taint source
   */
  protected isTaintSource(astNode: SolidityAstNode): boolean {
    // Parameters are potential taint sources (user input)
    if (
      astNode.stateVariable === false &&
      astNode['scope']?.includes?.('Parameter')
    ) {
      return true;
    }

    // External calls are taint sources
    if (
      astNode.nodeType === 'FunctionCall' &&
      astNode['kind'] === 'functionCall'
    ) {
      return true;
    }

    // msg.sender, msg.value, etc. are taint sources
    if (
      astNode.nodeType === 'MemberAccess' &&
      astNode.expression?.name === 'msg'
    ) {
      return true;
    }

    return false;
  }

  /**
   * Check if a node represents a taint sink
   */
  protected isTaintSink(astNode: SolidityAstNode): boolean {
    // State variable assignments are sinks
    if (
      astNode.nodeType === 'Assignment' &&
      astNode.left?.stateVariable === true
    ) {
      return true;
    }

    // External calls are sinks
    if (
      astNode.nodeType === 'FunctionCall' &&
      astNode['kind'] === 'functionCall'
    ) {
      return true;
    }

    // Event emissions are sinks
    if (astNode.nodeType === 'EmitStatement') {
      return true;
    }

    return false;
  }
}
