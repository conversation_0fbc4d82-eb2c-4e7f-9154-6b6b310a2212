/**
 * Enhanced CPG Transformer for Solidity AST
 * Modular, extensible, and thoroughly tested transformer
 * Converts complete Solidity AST to comprehensive Code Property Graph
 */

import {
  SolidityAstResult,
  SolidityAstNode,
} from '../services/solidity-ast.service';
import {
  CpgGraph,
  CpgNode,
  CpgEdge,
  ContractNode,
  FunctionNode,
  VariableNode,
  ModifierNode,
  EventNode,
  StructNode,
  EnumNode,
  ErrorNode,
  ExpressionNode,
  StatementNode,
  PragmaNode,
  ImportNode,
  AssemblyBlockNode,
  UsingForNode,
  LibraryNode,
  ContainsEdge,
  SourceLocation,
  CpgMetadata,
  AnalysisContext,
  VariableScope,
} from '../types/cpg';

// Note: Specialized processors will be implemented in future iterations
// For now, using the enhanced transformer with built-in processing

export class SolidityCpgTransformer {
  private nodes: Map<string, CpgNode> = new Map();
  private edges: Map<string, CpgEdge> = new Map();
  private context: AnalysisContext = {
    variableScopes: new Map(),
    taintedVariables: new Set(),
    stateVariables: new Set(),
  };
  private nodeIdCounter = 0;
  private edgeIdCounter = 0;

  /**
   * Transform Solidity AST to comprehensive CPG
   */
  public astToCpg(astResult: SolidityAstResult): CpgGraph {
    this.reset();

    if (!astResult.success || !astResult.ast) {
      throw new Error(
        `Invalid AST result: ${astResult.error || 'Unknown error'}`
      );
    }

    console.log('🔄 Transforming Solidity AST to CPG...');

    // Process the root AST node
    this.processAstNode(astResult.ast, astResult.filePath);

    // Perform advanced analysis
    this.performDataFlowAnalysis();
    this.performTaintAnalysis();
    this.buildCallGraph();

    const metadata = this.buildMetadata(
      astResult.filePath,
      astResult.compilerVersion
    );

    console.log(
      `✅ CPG transformation complete: ${this.nodes.size} nodes, ${this.edges.size} edges`
    );

    return {
      nodes: this.nodes,
      edges: this.edges,
      metadata,
    };
  }

  private reset(): void {
    this.nodes.clear();
    this.edges.clear();
    this.context = {
      variableScopes: new Map(),
      taintedVariables: new Set(),
      stateVariables: new Set(),
    };
    this.nodeIdCounter = 0;
    this.edgeIdCounter = 0;
  }

  private generateNodeId(): string {
    return `node_${++this.nodeIdCounter}`;
  }

  private generateEdgeId(): string {
    return `edge_${++this.edgeIdCounter}`;
  }

  private processAstNode(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): string {
    const nodeId = this.generateNodeId();
    const sourceLocation = this.extractSourceLocation(node, filePath);

    let cpgNode: CpgNode;

    switch (node.nodeType) {
      case 'ContractDefinition':
        // Check if this is actually a library
        const nodeAny = node as any;
        if (nodeAny.contractKind === 'library') {
          cpgNode = this.createLibraryNode(nodeId, node, sourceLocation);
        } else {
          cpgNode = this.createContractNode(nodeId, node, sourceLocation);
        }
        this.context.currentContract = node.name || 'unknown';
        break;

      case 'FunctionDefinition':
        cpgNode = this.createFunctionNode(nodeId, node, sourceLocation);
        break;

      case 'VariableDeclaration':
        cpgNode = this.createVariableNode(nodeId, node, sourceLocation);
        break;

      case 'ModifierDefinition':
        cpgNode = this.createModifierNode(nodeId, node, sourceLocation);
        break;

      case 'EventDefinition':
        cpgNode = this.createEventNode(nodeId, node, sourceLocation);
        break;

      case 'StructDefinition':
        cpgNode = this.createStructNode(nodeId, node, sourceLocation);
        break;

      case 'EnumDefinition':
        cpgNode = this.createEnumNode(nodeId, node, sourceLocation);
        break;

      case 'ErrorDefinition':
        cpgNode = this.createErrorNode(nodeId, node, sourceLocation);
        break;

      // New language features
      case 'PragmaDirective':
        cpgNode = this.createPragmaNode(nodeId, node, sourceLocation);
        break;

      case 'ImportDirective':
        cpgNode = this.createImportNode(nodeId, node, sourceLocation);
        break;

      case 'InlineAssembly':
        cpgNode = this.createAssemblyBlockNode(nodeId, node, sourceLocation);
        break;

      case 'UsingForDirective':
        cpgNode = this.createUsingForNode(nodeId, node, sourceLocation);
        break;

      case 'LibraryDefinition':
        cpgNode = this.createLibraryNode(nodeId, node, sourceLocation);
        break;

      // Control flow statements
      case 'IfStatement':
      case 'ForStatement':
      case 'WhileStatement':
      case 'DoWhileStatement':
      case 'Block':
        cpgNode = this.createStatementNode(nodeId, node, sourceLocation);
        break;

      // Expression statements
      case 'ExpressionStatement':
      case 'Return':
      case 'EmitStatement':
      case 'VariableDeclarationStatement':
        cpgNode = this.createStatementNode(nodeId, node, sourceLocation);
        break;

      // Expressions
      case 'Assignment':
      case 'FunctionCall':
      case 'MemberAccess':
      case 'Identifier':
      case 'Literal':
      case 'BinaryOperation':
      case 'UnaryOperation':
      case 'ConditionalOperator':
      case 'IndexAccess':
        cpgNode = this.createExpressionNode(nodeId, node, sourceLocation);
        break;

      default:
        // Create generic expression node for other types
        cpgNode = {
          id: nodeId,
          type: 'EXPRESSION',
          name: node.nodeType,
          ...(sourceLocation && { sourceLocation }),
          properties: {
            expressionType: node.nodeType,
            isTainted: false,
            taintSources: [],
          },
        };
    }

    this.nodes.set(nodeId, cpgNode);

    // Create CONTAINS edge from parent
    if (parentId) {
      this.createContainsEdge(parentId, nodeId);
    }

    // Process child nodes
    if (node.nodes) {
      for (const childNode of node.nodes) {
        this.processAstNode(childNode, filePath, nodeId);
      }
    }

    // Process other child properties
    this.processChildProperties(node, filePath, nodeId);

    return nodeId;
  }

  private createContractNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): ContractNode {
    const inheritance =
      node.baseContracts?.map(
        (base: any) =>
          base.baseName?.name || base['baseName']?.name || 'unknown'
      ) || [];

    return {
      id: nodeId,
      type: 'CONTRACT',
      name: node.name || 'Unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        inheritance,
        libraries: [], // TODO: Extract from using statements
        dependencies: [], // TODO: Extract from imports
      },
    };
  }

  private createFunctionNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): FunctionNode {
    const parameters = this.extractParameters(node.parameters);
    const returns = this.extractParameters(node.returnParameters);
    const nodeAny = node as any;

    // Enhanced detection for function properties
    const isVirtual = nodeAny.virtual === true;
    const isOverride =
      nodeAny.override === true ||
      (Array.isArray(nodeAny.overrides) && nodeAny.overrides.length > 0);
    const isPayable = nodeAny.stateMutability === 'payable';
    const isReceive = nodeAny.kind === 'receive';
    const isFallback = nodeAny.kind === 'fallback';

    return {
      id: nodeId,
      type: 'FUNCTION',
      name:
        node.name ||
        ((node as any).kind === 'constructor'
          ? 'constructor'
          : isReceive
            ? 'receive'
            : isFallback
              ? 'fallback'
              : 'unknown'),
      ...(sourceLocation && { sourceLocation }),
      properties: {
        signature: this.buildFunctionSignature(node),
        visibility: node.visibility || 'internal',
        stateMutability: node.stateMutability || 'nonpayable',
        modifiers:
          node.modifiers?.map(
            (mod: any) =>
              mod.modifierName?.name || mod['modifierName']?.name || 'unknown'
          ) || [],
        parameters,
        returns,
        isVirtual,
        isOverride,
        isPayable,
        isReceive,
        isFallback,
      },
    };
  }

  private createVariableNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): VariableNode {
    const isStateVariable = node.stateVariable || false;
    const scope: VariableScope = isStateVariable ? 'STATE' : 'LOCAL';

    if (isStateVariable) {
      this.context.stateVariables.add(nodeId);
    }

    // Enhanced detection for constants, immutables, and other properties
    const nodeAny = node as any;
    const isConstant =
      nodeAny.constant === true || nodeAny.mutability === 'constant';
    const isImmutable =
      nodeAny.immutable === true || nodeAny.mutability === 'immutable';
    const isPayable =
      nodeAny.payable === true ||
      nodeAny.mutability === 'payable' ||
      nodeAny.typeDescriptions?.typeString?.includes('payable');

    return {
      id: nodeId,
      type: 'VARIABLE',
      name: node.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        variableType: node.typeDescriptions?.typeString || 'unknown',
        ...(node.visibility && { visibility: node.visibility }),
        scope,
        isStateVariable,
        isConstant,
        isImmutable,
        isPayable,
        mutability: nodeAny.mutability,
        isTainted: false, // Will be determined during taint analysis
      },
    };
  }

  private createModifierNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): ModifierNode {
    return {
      id: nodeId,
      type: 'MODIFIER',
      name: node.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        parameters: this.extractParameters(node.parameters),
        virtual: (node as any).virtual || false,
        override: (node as any).override || false,
      },
    };
  }

  private createEventNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): EventNode {
    return {
      id: nodeId,
      type: 'EVENT',
      name: node.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        parameters: this.extractParameters(node.parameters),
        anonymous: (node as any).anonymous || false,
      },
    };
  }

  private createStructNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): StructNode {
    return {
      id: nodeId,
      type: 'STRUCT',
      name: node.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        members:
          (node as any).members?.map((member: any) => ({
            name: member.name || 'unknown',
            type: member.typeDescriptions?.typeString || 'unknown',
          })) || [],
      },
    };
  }

  private createEnumNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): EnumNode {
    return {
      id: nodeId,
      type: 'ENUM',
      name: node.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        values:
          (node as any).members?.map(
            (member: any) => member.name || 'unknown'
          ) || [],
      },
    };
  }

  private createErrorNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): ErrorNode {
    return {
      id: nodeId,
      type: 'ERROR',
      name: node.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        parameters: this.extractParameters(node.parameters),
      },
    };
  }

  private createPragmaNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): PragmaNode {
    const nodeAny = node as any;
    return {
      id: nodeId,
      type: 'PRAGMA',
      name: `pragma_${nodeAny.literals?.[0] || 'unknown'}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        pragmaType: nodeAny.literals?.[0] || 'unknown',
        version: nodeAny.literals?.[1],
        features: nodeAny.literals?.slice(1) || [],
      },
    };
  }

  private createImportNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): ImportNode {
    const nodeAny = node as any;
    return {
      id: nodeId,
      type: 'IMPORT',
      name: `import_${nodeAny.file || 'unknown'}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        importPath: nodeAny.file || nodeAny.absolutePath || 'unknown',
        symbols:
          nodeAny.symbolAliases?.map(
            (alias: any) => alias.foreign?.name || alias.local
          ) || [],
        alias: nodeAny.unitAlias,
      },
    };
  }

  private createAssemblyBlockNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): AssemblyBlockNode {
    const nodeAny = node as any;
    return {
      id: nodeId,
      type: 'ASSEMBLY_BLOCK',
      name: 'assembly_block',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        dialect: nodeAny.dialect || 'yul',
        operations: this.extractAssemblyOperations(nodeAny.body),
      },
    };
  }

  private createUsingForNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): UsingForNode {
    const nodeAny = node as any;
    return {
      id: nodeId,
      type: 'USING_FOR',
      name: `using_${nodeAny.libraryName?.name || 'unknown'}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        libraryName:
          nodeAny.libraryName?.name ||
          nodeAny.functions?.[0]?.function?.name ||
          'unknown',
        typeName:
          nodeAny.typeName?.typeDescriptions?.typeString ||
          nodeAny.typeName?.name ||
          'unknown',
        functions:
          nodeAny.functions?.map(
            (func: any) => func.function?.name || 'unknown'
          ) || [],
      },
    };
  }

  private createLibraryNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): LibraryNode {
    return {
      id: nodeId,
      type: 'LIBRARY',
      name: node.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        functions: [], // Will be populated by child function nodes
        dependencies: [], // TODO: Extract from imports
      },
    };
  }

  private createStatementNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): StatementNode {
    return {
      id: nodeId,
      type: 'STATEMENT',
      name: node.nodeType.toLowerCase(),
      ...(sourceLocation && { sourceLocation }),
      properties: {
        statementType: node.nodeType,
        ...((node as any).condition && { hasCondition: true }),
        ...((node as any).body && { hasBody: true }),
        ...((node as any).initializationExpression && {
          hasInitialization: true,
        }),
        ...((node as any).loopExpression && { hasLoopExpression: true }),
      },
    };
  }

  private createExpressionNode(
    nodeId: string,
    node: SolidityAstNode,
    sourceLocation?: SourceLocation
  ): ExpressionNode {
    const isTainted = this.isTaintSource(node);

    return {
      id: nodeId,
      type: 'EXPRESSION',
      name: node.nodeType.toLowerCase(),
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: node.nodeType,
        ...(node.operator && { operator: node.operator }),
        ...((node as any).value !== undefined && {
          value: String((node as any).value),
        }),
        ...((node as any).functionName && {
          functionName: (node as any).functionName,
        }),
        ...((node as any).memberName && {
          memberName: (node as any).memberName,
        }),
        isTainted,
        taintSources: isTainted ? [this.getTaintSourceType(node)] : [],
      },
    };
  }

  private getTaintSourceType(node: SolidityAstNode): string {
    if (
      node.nodeType === 'MemberAccess' &&
      (node as any).expression?.name === 'msg'
    ) {
      return 'msg_context';
    }
    if (node.nodeType === 'FunctionCall') {
      return 'function_call';
    }
    if (
      node.nodeType === 'Identifier' &&
      (node as any).name?.startsWith('msg')
    ) {
      return 'msg_context';
    }
    return 'unknown';
  }

  private isTaintSource(node: SolidityAstNode): boolean {
    // Parameters are potential taint sources (user input)
    if (
      (node as any).stateVariable === false &&
      (node as any).scope?.includes?.('Parameter')
    ) {
      return true;
    }

    // External calls are taint sources
    if (
      node.nodeType === 'FunctionCall' &&
      (node as any).kind === 'functionCall'
    ) {
      return true;
    }

    // msg.sender, msg.value, etc. are taint sources
    if (
      node.nodeType === 'MemberAccess' &&
      (node as any).expression?.name === 'msg'
    ) {
      return true;
    }

    return false;
  }

  private extractParameters(
    parametersNode?: SolidityAstNode
  ): Array<{ name: string; type: string; indexed?: boolean }> {
    if (!parametersNode || !parametersNode.parameters) {
      return [];
    }

    return (
      (parametersNode as any).parameters?.map((param: any) => ({
        name: param.name || 'unknown',
        type: param.typeDescriptions?.typeString || 'unknown',
        ...(param.indexed !== undefined && { indexed: param.indexed }),
      })) || []
    );
  }

  private extractAssemblyOperations(bodyNode?: any): string[] {
    if (!bodyNode || !bodyNode.statements) {
      return [];
    }

    const operations: string[] = [];

    const extractFromStatement = (stmt: any): void => {
      if (stmt.nodeType) {
        operations.push(stmt.nodeType);
      }

      // Recursively extract from nested statements
      if (stmt.statements) {
        stmt.statements.forEach(extractFromStatement);
      }
      if (stmt.body) {
        extractFromStatement(stmt.body);
      }
    };

    bodyNode.statements.forEach(extractFromStatement);
    return operations;
  }

  private buildFunctionSignature(node: SolidityAstNode): string {
    const name = node.name || 'unknown';
    const params = this.extractParameters(node.parameters);
    const paramTypes = params.map((p) => p.type).join(',');
    return `${name}(${paramTypes})`;
  }

  private processChildProperties(
    node: SolidityAstNode,
    filePath: string,
    parentId: string
  ): void {
    // Process all properties that might contain child nodes
    const nodeAny = node as any;

    // First, process known important properties
    const priorityProperties = [
      'body',
      'parameters',
      'returnParameters',
      'modifiers',
      'baseContracts',
      'statements', // For function bodies and blocks
      'nodes', // For contract-level nodes
    ];

    for (const prop of priorityProperties) {
      const value = nodeAny[prop];
      if (value && typeof value === 'object') {
        if (Array.isArray(value)) {
          value.forEach((item) => {
            if (item && item.nodeType) {
              this.processAstNode(item, filePath, parentId);
            }
          });
        } else if (value.nodeType) {
          this.processAstNode(value, filePath, parentId);
        }
      }
    }

    // Then, recursively process all other properties that might contain nodes
    for (const [key, value] of Object.entries(nodeAny)) {
      // Skip already processed properties and non-object values
      if (
        priorityProperties.includes(key) ||
        !value ||
        typeof value !== 'object'
      ) {
        continue;
      }

      if (Array.isArray(value)) {
        value.forEach((item) => {
          if (item && typeof item === 'object' && item.nodeType) {
            this.processAstNode(item, filePath, parentId);
          }
        });
      } else if ((value as any).nodeType) {
        this.processAstNode(value as SolidityAstNode, filePath, parentId);
      }
    }
  }

  private createContainsEdge(parentId: string, childId: string): void {
    const edgeId = this.generateEdgeId();

    const edge: ContainsEdge = {
      id: edgeId,
      type: 'CONTAINS',
      source: parentId,
      target: childId,
      properties: {},
    };

    this.edges.set(edgeId, edge);
  }

  private performDataFlowAnalysis(): void {
    // TODO: Implement sophisticated data flow analysis
    console.log('📊 Performing data flow analysis...');
  }

  private performTaintAnalysis(): void {
    // TODO: Implement comprehensive taint analysis
    console.log('🔍 Performing taint analysis...');
  }

  private buildCallGraph(): void {
    // TODO: Implement call graph construction
    console.log('📞 Building call graph...');
  }

  private extractSourceLocation(
    node: SolidityAstNode,
    filePath: string
  ): SourceLocation | undefined {
    if (!node.src) return undefined;

    const parts = node.src.split(':');
    if (parts.length < 2) return undefined;

    return {
      start: parseInt(parts[0] || '0', 10),
      length: parseInt(parts[1] || '0', 10),
      lines: [], // TODO: Calculate line numbers
      startColumn: 0, // TODO: Calculate column numbers
      endColumn: 0,
      filename: filePath,
    };
  }

  private buildMetadata(
    filePath: string,
    compilerVersion?: string
  ): CpgMetadata {
    const nodesByType = new Map<string, number>();
    for (const node of this.nodes.values()) {
      nodesByType.set(node.type, (nodesByType.get(node.type) || 0) + 1);
    }

    return {
      sourceFile: filePath,
      timestamp: Date.now(),
      ...(compilerVersion && { slitherVersion: compilerVersion }),
      nodeCount: this.nodes.size,
      edgeCount: this.edges.size,
      contractCount: nodesByType.get('CONTRACT') || 0,
      functionCount: nodesByType.get('FUNCTION') || 0,
      variableCount: nodesByType.get('VARIABLE') || 0,
      taintFlows: [], // TODO: Implement taint flow detection
      sinks: [], // TODO: Implement sink detection
    };
  }
}

/**
 * Main export function for transforming Solidity AST to CPG
 */
export function solidityAstToCpg(astResult: SolidityAstResult): CpgGraph {
  const transformer = new SolidityCpgTransformer();
  return transformer.astToCpg(astResult);
}
