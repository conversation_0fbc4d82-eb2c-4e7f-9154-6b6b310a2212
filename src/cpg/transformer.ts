/**
 * CPG Transformer - Converts Slither AST to Code Property Graph
 * Supports deep state management, taint analysis, and control flow
 */

import { SlitherAstResult } from '../types/ast';
import {
  CpgGraph,
  CpgNode,
  CpgEdge,
  ContractNode,
  FunctionNode,
  VariableNode,
  ExpressionNode,
  ContainsEdge,
  TaintEdge,
  SourceLocation,
  CpgMetadata,
  AnalysisContext,
  VariableScope,
  TaintFlow,
  SinkInfo,
} from '../types/cpg';

export class CpgTransformer {
  private nodes: Map<string, CpgNode> = new Map();
  private edges: Map<string, CpgEdge> = new Map();
  private context: AnalysisContext = {
    variableScopes: new Map(),
    taintedVariables: new Set(),
    stateVariables: new Set(),
  };
  private nodeIdCounter = 0;
  private edgeIdCounter = 0;

  /**
   * Transform Slither AST to CPG
   */
  public astToCpg(ast: SlitherAstResult): CpgGraph {
    this.reset();

    if (!ast.success || !ast.ast) {
      throw new Error(`Invalid AST result: ${ast.error || 'Unknown error'}`);
    }

    // Parse the actual Slither JSON structure
    const slitherData = ast.ast as any;

    if (!slitherData.results || !slitherData.results.detectors) {
      throw new Error(
        'Invalid Slither AST structure: missing results.detectors'
      );
    }

    // Extract contracts, functions, and variables from detector elements
    this.extractFromDetectors(slitherData.results.detectors, ast.filePath);

    // TODO: Add CFG enrichment when available
    // TODO: Enhance taint analysis with inter-procedural flows
    // TODO: Add call graph construction
    // TODO: Implement data dependency analysis

    const metadata = this.buildMetadata(ast.filePath, slitherData);

    return {
      nodes: this.nodes,
      edges: this.edges,
      metadata,
    };
  }

  private reset(): void {
    this.nodes.clear();
    this.edges.clear();
    this.context = {
      variableScopes: new Map(),
      taintedVariables: new Set(),
      stateVariables: new Set(),
    };
    this.nodeIdCounter = 0;
    this.edgeIdCounter = 0;
  }

  private generateNodeId(): string {
    return `node_${++this.nodeIdCounter}`;
  }

  private generateEdgeId(): string {
    return `edge_${++this.edgeIdCounter}`;
  }

  private extractFromDetectors(detectors: any[], filePath: string): void {
    const contractMap = new Map<string, string>(); // contract name -> node id
    const functionMap = new Map<string, string>(); // function signature -> node id

    for (const detector of detectors) {
      if (!detector.elements) continue;

      for (const element of detector.elements) {
        this.processElement(element, contractMap, functionMap, filePath);
      }
    }

    // Perform taint analysis after all nodes are created
    this.performTaintAnalysis();
  }

  private processElement(
    element: any,
    contractMap: Map<string, string>,
    functionMap: Map<string, string>,
    filePath: string
  ): void {
    const sourceLocation = this.extractSourceLocation(
      element.source_mapping,
      filePath
    );

    switch (element.type) {
      case 'variable':
        this.processVariable(element, contractMap, functionMap, sourceLocation);
        break;
      case 'function':
        this.processFunction(element, contractMap, functionMap, sourceLocation);
        break;
      case 'contract':
        this.processContract(element, contractMap, sourceLocation);
        break;
      case 'pragma':
        // Skip pragma elements for now
        break;
      default:
        // Handle other element types as expressions
        this.processExpression(element, sourceLocation);
    }
  }

  private processContract(
    element: any,
    contractMap: Map<string, string>,
    sourceLocation?: SourceLocation
  ): void {
    const contractId = this.generateNodeId();
    const parent = element.type_specific_fields?.parent;

    const contractNode: ContractNode = {
      id: contractId,
      type: 'CONTRACT',
      name: parent?.name || element.name || 'Unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        inheritance: [],
        libraries: [],
        dependencies: [],
      },
    };

    this.nodes.set(contractId, contractNode);
    contractMap.set(contractNode.name, contractId);
    this.context.currentContract = contractNode.name;
  }

  private processFunction(
    element: any,
    contractMap: Map<string, string>,
    functionMap: Map<string, string>,
    sourceLocation?: SourceLocation
  ): void {
    const functionId = this.generateNodeId();
    const parent = element.type_specific_fields?.parent;

    const functionName = parent?.name || element.name || 'unknown';
    const signature =
      parent?.signature || element.signature || `${functionName}()`;

    const functionNode: FunctionNode = {
      id: functionId,
      type: 'FUNCTION',
      name: functionName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        signature,
        visibility: 'public', // Default, could be extracted from more detailed AST
        stateMutability: 'nonpayable', // Default
        modifiers: [],
        parameters: [],
        returns: [],
      },
    };

    this.nodes.set(functionId, functionNode);
    functionMap.set(signature, functionId);
    this.context.currentFunction = signature;

    // Create CONTAINS edge from contract to function
    if (parent?.type === 'contract') {
      const contractName = parent.name;
      const contractId = contractMap.get(contractName);
      if (contractId) {
        this.createContainsEdge(contractId, functionId);
      }
    }
  }

  private processVariable(
    element: any,
    contractMap: Map<string, string>,
    functionMap: Map<string, string>,
    sourceLocation?: SourceLocation
  ): void {
    const variableId = this.generateNodeId();
    const parent = element.type_specific_fields?.parent;

    const variableName = element.name;
    const isParameter = element.additional_fields?.target === 'parameter';

    // Determine variable scope and if it's a state variable
    let scope: VariableScope = 'LOCAL';
    let isStateVariable = false;

    if (isParameter) {
      scope = 'PARAMETER';
    } else if (parent?.type === 'contract') {
      scope = 'STATE';
      isStateVariable = true;
      this.context.stateVariables.add(variableId);
    } else if (parent?.type === 'function') {
      scope = 'LOCAL';
    }

    const variableNode: VariableNode = {
      id: variableId,
      type: 'VARIABLE',
      name: variableName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        variableType: 'uint256', // Default, could be extracted from more detailed AST
        scope,
        isStateVariable,
        isTainted: false, // Will be determined during taint analysis
      },
    };

    this.nodes.set(variableId, variableNode);
    this.context.variableScopes.set(variableId, scope);

    // Create CONTAINS edge from parent to variable
    if (parent) {
      let parentId: string | undefined;

      if (parent.type === 'function') {
        const signature = parent.signature || `${parent.name}()`;
        parentId = functionMap.get(signature);
      } else if (parent.type === 'contract') {
        parentId = contractMap.get(parent.name);
      }

      if (parentId) {
        this.createContainsEdge(parentId, variableId);
      }
    }

    // Mark parameters as potentially tainted (user input)
    if (isParameter) {
      this.context.taintedVariables.add(variableId);
      variableNode.properties.isTainted = true;
      variableNode.properties.taintSource = 'user_input';
    }
  }

  private processExpression(
    element: any,
    sourceLocation?: SourceLocation
  ): void {
    const expressionId = this.generateNodeId();

    const expressionNode: ExpressionNode = {
      id: expressionId,
      type: 'EXPRESSION',
      name: element.name || 'expression',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: element.type || 'unknown',
        isTainted: false,
        taintSources: [],
      },
    };

    this.nodes.set(expressionId, expressionNode);
  }

  private createContainsEdge(parentId: string, childId: string): void {
    const edgeId = this.generateEdgeId();

    const edge: ContainsEdge = {
      id: edgeId,
      type: 'CONTAINS',
      source: parentId,
      target: childId,
      properties: {},
    };

    this.edges.set(edgeId, edge);
  }

  private performTaintAnalysis(): void {
    // Identify taint sources (parameters, external calls)
    const taintSources = new Set<string>();
    const stateSinks = new Set<string>();

    for (const [nodeId, node] of this.nodes) {
      if (node.type === 'VARIABLE') {
        const variable = node as VariableNode;

        // Mark parameters as taint sources
        if (variable.properties.scope === 'PARAMETER') {
          taintSources.add(nodeId);
          variable.properties.isTainted = true;
          variable.properties.taintSource = 'user_input';
        }

        // Mark state variables as potential sinks
        if (variable.properties.isStateVariable) {
          stateSinks.add(nodeId);
        }
      }
    }

    // Create taint edges from sources to sinks
    this.createTaintFlows(taintSources, stateSinks);

    // TODO: Implement inter-procedural taint analysis
    // TODO: Add support for complex expressions and assignments
    // TODO: Track taint through function calls
    // TODO: Implement points-to analysis for precise taint tracking
  }

  private createTaintFlows(sources: Set<string>, sinks: Set<string>): void {
    // For now, create potential taint flows between all sources and sinks
    // In a real implementation, this would be based on actual data flow analysis
    for (const sourceId of sources) {
      for (const sinkId of sinks) {
        const edgeId = this.generateEdgeId();

        const taintEdge: TaintEdge = {
          id: edgeId,
          type: 'TAINT',
          source: sourceId,
          target: sinkId,
          properties: {
            taintType: 'potential_flow',
            confidence: 0.5, // Low confidence without proper flow analysis
          },
        };

        this.edges.set(edgeId, taintEdge);
      }
    }
  }

  private extractSourceLocation(
    sourceMapping: any,
    filePath: string
  ): SourceLocation | undefined {
    if (!sourceMapping) return undefined;

    return {
      start: sourceMapping.start || 0,
      length: sourceMapping.length || 0,
      lines: sourceMapping.lines || [],
      startColumn: sourceMapping.starting_column || 0,
      endColumn: sourceMapping.ending_column || 0,
      filename: sourceMapping.filename_short || filePath,
    };
  }

  private buildMetadata(filePath: string, slitherData: any): CpgMetadata {
    const nodesByType = new Map<string, number>();
    for (const node of this.nodes.values()) {
      nodesByType.set(node.type, (nodesByType.get(node.type) || 0) + 1);
    }

    // Extract taint flows from TAINT edges
    const taintFlows: TaintFlow[] = [];
    const sinks: SinkInfo[] = [];

    for (const edge of this.edges.values()) {
      if (edge.type === 'TAINT') {
        const sourceNode = this.nodes.get(edge.source);
        const targetNode = this.nodes.get(edge.target);

        if (sourceNode && targetNode) {
          taintFlows.push({
            source: sourceNode.name,
            sink: targetNode.name,
            path: [sourceNode.id, targetNode.id],
          });

          // Add sink information
          if (
            targetNode.type === 'VARIABLE' &&
            (targetNode as VariableNode).properties.isStateVariable
          ) {
            sinks.push({
              nodeId: targetNode.id,
              sinkType: 'STATE_WRITE',
              location: targetNode.sourceLocation || {
                start: 0,
                length: 0,
                lines: [],
                startColumn: 0,
                endColumn: 0,
                filename: filePath,
              },
              taintSources: [sourceNode.name],
            });
          }
        }
      }
    }

    return {
      sourceFile: filePath,
      timestamp: Date.now(),
      slitherVersion: slitherData.version,
      nodeCount: this.nodes.size,
      edgeCount: this.edges.size,
      contractCount: nodesByType.get('CONTRACT') || 0,
      functionCount: nodesByType.get('FUNCTION') || 0,
      variableCount: nodesByType.get('VARIABLE') || 0,
      taintFlows,
      sinks,
    };
  }
}

/**
 * Main export function for transforming AST to CPG
 */
export function astToCpg(ast: SlitherAstResult): CpgGraph {
  const transformer = new CpgTransformer();
  return transformer.astToCpg(ast);
}
