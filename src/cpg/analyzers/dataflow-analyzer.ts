/**
 * Data Flow Analyzer
 * Performs data flow analysis on the CPG
 */

import {
  CpgGraph,
  CpgNode,
  DataFlowEdge,
  AnalysisContext,
} from '../../types/cpg';

export interface DataFlowResult {
  defUseChains: Map<string, DefUseChain>;
  reachingDefinitions: Map<string, Set<string>>;
  liveVariables: Map<string, Set<string>>;
}

export interface DefUseChain {
  variable: string;
  definitions: string[];
  uses: string[];
}

export class DataFlowAnalyzer {
  private cpg: CpgGraph;

  constructor(cpg: CpgGraph, _context: AnalysisContext) {
    this.cpg = cpg;
  }

  /**
   * Perform comprehensive data flow analysis
   */
  analyze(): DataFlowResult {
    console.log('📊 Starting data flow analysis...');

    // Step 1: Build def-use chains
    const defUseChains = this.buildDefUseChains();
    console.log(`📊 Built ${defUseChains.size} def-use chains`);

    // Step 2: Calculate reaching definitions
    const reachingDefinitions = this.calculateReachingDefinitions();
    console.log(
      `📊 Calculated reaching definitions for ${reachingDefinitions.size} nodes`
    );

    // Step 3: Calculate live variables
    const liveVariables = this.calculateLiveVariables();
    console.log(`📊 Calculated live variables for ${liveVariables.size} nodes`);

    // Step 4: Create data flow edges
    this.createDataFlowEdges(defUseChains);

    console.log('✅ Data flow analysis complete');

    return {
      defUseChains,
      reachingDefinitions,
      liveVariables,
    };
  }

  /**
   * Build def-use chains for all variables
   */
  private buildDefUseChains(): Map<string, DefUseChain> {
    const chains = new Map<string, DefUseChain>();
    const variables = this.getAllVariables();

    for (const variable of variables) {
      const definitions = this.findDefinitions(variable);
      const uses = this.findUses(variable);

      chains.set(variable, {
        variable,
        definitions,
        uses,
      });
    }

    return chains;
  }

  /**
   * Calculate reaching definitions for each program point
   */
  private calculateReachingDefinitions(): Map<string, Set<string>> {
    const reachingDefs = new Map<string, Set<string>>();
    const workList = Array.from(this.cpg.nodes.keys());

    // Initialize
    for (const nodeId of workList) {
      reachingDefs.set(nodeId, new Set());
    }

    // Iterative data flow analysis
    let changed = true;
    while (changed) {
      changed = false;

      for (const nodeId of workList) {
        const oldSet = new Set(reachingDefs.get(nodeId));
        const newSet = this.computeReachingDefinitions(nodeId, reachingDefs);

        if (!this.setsEqual(oldSet, newSet)) {
          reachingDefs.set(nodeId, newSet);
          changed = true;
        }
      }
    }

    return reachingDefs;
  }

  /**
   * Calculate live variables for each program point
   */
  private calculateLiveVariables(): Map<string, Set<string>> {
    const liveVars = new Map<string, Set<string>>();
    const workList = Array.from(this.cpg.nodes.keys());

    // Initialize
    for (const nodeId of workList) {
      liveVars.set(nodeId, new Set());
    }

    // Backward data flow analysis
    let changed = true;
    while (changed) {
      changed = false;

      for (const nodeId of workList) {
        const oldSet = new Set(liveVars.get(nodeId));
        const newSet = this.computeLiveVariables(nodeId, liveVars);

        if (!this.setsEqual(oldSet, newSet)) {
          liveVars.set(nodeId, newSet);
          changed = true;
        }
      }
    }

    return liveVars;
  }

  /**
   * Create data flow edges in the CPG
   */
  private createDataFlowEdges(defUseChains: Map<string, DefUseChain>): void {
    let edgeCount = 0;

    for (const [variable, chain] of defUseChains) {
      // Create DEF edges
      for (const defId of chain.definitions) {
        const defEdge: DataFlowEdge = {
          id: `dataflow_def_${++edgeCount}`,
          type: 'DATA_FLOW',
          source: defId,
          target: `var_${variable}`,
          properties: {
            flowType: 'DEF',
            variable,
          },
        };
        this.cpg.edges.set(defEdge.id, defEdge);
      }

      // Create USE edges
      for (const useId of chain.uses) {
        const useEdge: DataFlowEdge = {
          id: `dataflow_use_${++edgeCount}`,
          type: 'DATA_FLOW',
          source: `var_${variable}`,
          target: useId,
          properties: {
            flowType: 'USE',
            variable,
          },
        };
        this.cpg.edges.set(useEdge.id, useEdge);
      }

      // Create DEF-USE edges
      for (const defId of chain.definitions) {
        for (const useId of chain.uses) {
          if (this.isReachable(defId, useId)) {
            const defUseEdge: DataFlowEdge = {
              id: `dataflow_defuse_${++edgeCount}`,
              type: 'DATA_FLOW',
              source: defId,
              target: useId,
              properties: {
                flowType: 'DEF_USE',
                variable,
              },
            };
            this.cpg.edges.set(defUseEdge.id, defUseEdge);
          }
        }
      }
    }
  }

  /**
   * Get all variables in the CPG
   */
  private getAllVariables(): string[] {
    const variables: string[] = [];

    for (const [_nodeId, node] of this.cpg.nodes) {
      if (node.type === 'VARIABLE') {
        variables.push(node.name);
      }
    }

    return [...new Set(variables)]; // Remove duplicates
  }

  /**
   * Find all definitions of a variable
   */
  private findDefinitions(variable: string): string[] {
    const definitions: string[] = [];

    for (const [nodeId, node] of this.cpg.nodes) {
      if (this.isDefinition(node, variable)) {
        definitions.push(nodeId);
      }
    }

    return definitions;
  }

  /**
   * Find all uses of a variable
   */
  private findUses(variable: string): string[] {
    const uses: string[] = [];

    for (const [nodeId, node] of this.cpg.nodes) {
      if (this.isUse(node, variable)) {
        uses.push(nodeId);
      }
    }

    return uses;
  }

  /**
   * Check if a node is a definition of the variable
   */
  private isDefinition(node: CpgNode, variable: string): boolean {
    // Variable declaration is a definition
    if (node.type === 'VARIABLE' && node.name === variable) {
      return true;
    }

    // Assignment to variable is a definition
    if (node.type === 'ASSIGNMENT') {
      // Check if the assignment target is our variable
      return this.assignmentTargetsVariable(node, variable);
    }

    return false;
  }

  /**
   * Check if a node is a use of the variable
   */
  private isUse(node: CpgNode, variable: string): boolean {
    // Expression that references the variable
    if (node.type === 'EXPRESSION' && node.name === variable) {
      return true;
    }

    // Assignment that reads the variable (right-hand side)
    if (node.type === 'ASSIGNMENT') {
      return this._assignmentUsesVariable(node, variable);
    }

    return false;
  }

  /**
   * Check if assignment targets the variable
   */
  private assignmentTargetsVariable(node: CpgNode, variable: string): boolean {
    // This would need to be enhanced with actual AST analysis
    // For now, use heuristics based on node name
    return node.name.includes(variable);
  }

  /**
   * Check if assignment uses the variable
   */
  private _assignmentUsesVariable(_node: CpgNode, _variable: string): boolean {
    // This would need to be enhanced with actual AST analysis
    // For now, use heuristics based on node properties
    return false; // Placeholder
  }

  /**
   * Compute reaching definitions for a node
   */
  private computeReachingDefinitions(
    nodeId: string,
    reachingDefs: Map<string, Set<string>>
  ): Set<string> {
    const newSet = new Set<string>();

    // Get predecessors
    const predecessors = this.getPredecessors(nodeId);

    // Union of reaching definitions from all predecessors
    for (const pred of predecessors) {
      const predDefs = reachingDefs.get(pred) || new Set();
      for (const def of predDefs) {
        newSet.add(def);
      }
    }

    // Add definitions generated by this node
    const generated = this.getGeneratedDefinitions(nodeId);
    for (const def of generated) {
      newSet.add(def);
    }

    // Remove definitions killed by this node
    const killed = this.getKilledDefinitions(nodeId);
    for (const def of killed) {
      newSet.delete(def);
    }

    return newSet;
  }

  /**
   * Compute live variables for a node
   */
  private computeLiveVariables(
    nodeId: string,
    liveVars: Map<string, Set<string>>
  ): Set<string> {
    const newSet = new Set<string>();

    // Get successors
    const successors = this.getSuccessors(nodeId);

    // Union of live variables from all successors
    for (const succ of successors) {
      const succLive = liveVars.get(succ) || new Set();
      for (const variable of succLive) {
        newSet.add(variable);
      }
    }

    // Add variables used by this node
    const used = this.getUsedVariables(nodeId);
    for (const variable of used) {
      newSet.add(variable);
    }

    // Remove variables defined by this node
    const defined = this.getDefinedVariables(nodeId);
    for (const variable of defined) {
      newSet.delete(variable);
    }

    return newSet;
  }

  /**
   * Get predecessor nodes
   */
  private getPredecessors(nodeId: string): string[] {
    const predecessors: string[] = [];

    for (const [_edgeId, edge] of this.cpg.edges) {
      if (edge.target === nodeId && edge.type === 'CONTROL_FLOW') {
        predecessors.push(edge.source);
      }
    }

    return predecessors;
  }

  /**
   * Get successor nodes
   */
  private getSuccessors(nodeId: string): string[] {
    const successors: string[] = [];

    for (const [_edgeId, edge] of this.cpg.edges) {
      if (edge.source === nodeId && edge.type === 'CONTROL_FLOW') {
        successors.push(edge.target);
      }
    }

    return successors;
  }

  /**
   * Get definitions generated by a node
   */
  private getGeneratedDefinitions(nodeId: string): string[] {
    const node = this.cpg.nodes.get(nodeId);
    if (!node) return [];

    if (this.isDefinition(node, node.name)) {
      return [nodeId];
    }

    return [];
  }

  /**
   * Get definitions killed by a node
   */
  private getKilledDefinitions(nodeId: string): string[] {
    // A definition kills other definitions of the same variable
    const node = this.cpg.nodes.get(nodeId);
    if (!node) return [];

    const killed: string[] = [];

    if (node.type === 'ASSIGNMENT') {
      // Find other definitions of the same variable
      for (const [otherId, otherNode] of this.cpg.nodes) {
        if (otherId !== nodeId && this.isDefinition(otherNode, node.name)) {
          killed.push(otherId);
        }
      }
    }

    return killed;
  }

  /**
   * Get variables used by a node
   */
  private getUsedVariables(nodeId: string): string[] {
    const node = this.cpg.nodes.get(nodeId);
    if (!node) return [];

    const used: string[] = [];

    if (node.type === 'EXPRESSION' && node.name) {
      used.push(node.name);
    }

    return used;
  }

  /**
   * Get variables defined by a node
   */
  private getDefinedVariables(nodeId: string): string[] {
    const node = this.cpg.nodes.get(nodeId);
    if (!node) return [];

    const defined: string[] = [];

    if (node.type === 'VARIABLE') {
      defined.push(node.name);
    }

    return defined;
  }

  /**
   * Check if one node is reachable from another
   */
  private isReachable(fromId: string, toId: string): boolean {
    const visited = new Set<string>();
    const queue = [fromId];

    while (queue.length > 0) {
      const current = queue.shift()!;

      if (current === toId) {
        return true;
      }

      if (visited.has(current)) {
        continue;
      }
      visited.add(current);

      const successors = this.getSuccessors(current);
      queue.push(...successors);
    }

    return false;
  }

  /**
   * Check if two sets are equal
   */
  private setsEqual<T>(set1: Set<T>, set2: Set<T>): boolean {
    if (set1.size !== set2.size) {
      return false;
    }

    for (const item of set1) {
      if (!set2.has(item)) {
        return false;
      }
    }

    return true;
  }
}
