#!/usr/bin/env node

/**
 * Solidity Contract to Graph Database
 *
 * This application analyzes Solidity smart contracts using Slither
 * and stores the analysis results in a Neo4j graph database.
 */

import { config } from 'dotenv';

// Load environment variables
config();

async function main(): Promise<void> {
  console.log('🚀 Sol Contract 2 Graph DB');
  console.log('===========================');

  console.log('✅ TypeScript workspace initialized successfully!');
  console.log('📦 Dependencies installed');
  console.log('🔧 Foundry & Slither integration ready');

  // Demonstrate the workflow
  console.log('\n🔄 Available Workflow:');
  console.log('  1. Place Solidity contracts in /contracts');
  console.log('  2. Run `npm run flatten <folder>` to flatten contracts');
  console.log(
    '  3. Run `npm run parse <flattened-file>` to analyze with Slither'
  );
  console.log('  4. Store analysis results in Neo4j graph database');

  console.log('\n📁 Test Contracts Available:');
  console.log('  - src/test/test-contracts/Meh.sol');
  console.log('  - src/test/test-contracts/TestContract.sol');

  console.log('\n🧪 Try the workflow:');
  console.log('  npm run flatten src/test/test-contracts');
  console.log('  npm run parse contracts/output/flattened-*.sol');

  console.log('\n🎯 Next steps:');
  console.log(
    '  1. Install Foundry: https://book.getfoundry.sh/getting-started/installation'
  );
  console.log('  2. Install Slither: pip install slither-analyzer');
  console.log('  3. Configure Neo4j connection in .env file');
  console.log('  4. Build the graph database storage pipeline');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Application failed to start:', error);
    process.exit(1);
  });
}

export { main };
